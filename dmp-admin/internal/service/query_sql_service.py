import json
import traceback
from loguru import logger

from base import repository
from dmplib.saas.project import get_db as get_project_db
from dmplib.utils.errors import UserError
from components.deshelper import decrypt, encrypt

phase = 'aOPCXImQ'


def query(project_code, sql: str, is_config_db=0):
    lower_sql = sql.lower().strip()
    if not any([
        lower_sql.startswith('select'),
        lower_sql.startswith('desc')
    ]):
        raise UserError(message='只支持select！')

    try:
        if not is_config_db:
            conn = get_project_db(project_code)
            data = conn.query(sql=sql)
            conn.end()
            return data
        else:
            return repository.get_data_by_sql(sql, params={})
    except Exception as e:
        logger.error(f'sql查询出错，原因是：{traceback.format_exc()}')
        raise UserError(message=str(e))


def encrypt_sql_data(data):
    return encrypt(data, phase)


def validate_request_data(data):
    if not data:
        return False
    try:
        decrypted_data = decrypt(data, phase=phase)
        return json.loads(decrypted_data)
    except:
        logger.error(f'转换加密数据data【{data}】请求数据失败：{traceback.format_exc()}')
        return False
