#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    dmp-admin
    <NAME_EMAIL> on 2018/03/26.
"""
import io
import json

import hug
import jwt
import time
from hug.authentication import authenticator

from dmplib import config
from dmplib.hug import APIWrapper, g
from dmplib.utils.errors import UserError
from project.services import project_service
from project.models import ProjectQueryModel
from .service import query_sql_service


@authenticator
def _verify_auth_key_handle(request, response, verify_user, **kwargs):
    """
    :param request:
    :param response:
    :return:
    """
    try:
        api_token = request.headers.get('X-API-TOKEN')
        if not api_token:
            raise UserError(message='请指定接口token')
        secret = config.get('JWT.secret')
        data = jwt.decode(api_token, secret, algorithms=["HS256"])
        if not data:
            raise UserError(message='校验失败！')
        current_time = int(time.time())
        if data.get('time') - current_time > 600:
            raise UserError(message='请求超时')
        return True
    except Exception as e:
        response.status = 401
        response.body = str(e)
        return False
    g.account = 'internalapi'
    return True


class InternalAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_internal_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._internal_route = None
        self.api.http.base_url = '/internalapi'

    @property
    def internal_route(self):
        if not self._internal_route:
            self._internal_route = hug.http(api=self.api, requires=_verify_auth_key_handle(None))
        return self._internal_route


class InternalSuperCAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_internal_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._internal_route = None
        self.api.http.base_url = '/internalapi'

    @property
    def internal_route(self):
        if not self._internal_route:
            # self._internal_route = hug.http(api=self.api, requires=_verify_superc_auth_key_handle(None))
            self._internal_route = hug.http(api=self.api, requires=None)
        return self._internal_route


api = InternalAPIWrapper(__name__)
sapi = InternalSuperCAPIWrapper(__name__)


@api.internal_route.get('/tenants')
def get_tenants(**kwargs):
    """
    /**
    @apiVersion 1.0.7
    @api {post} /internalapi/tenants 获取租户列表
    @apiGroup internalapi
    @apiParam   query  {string}    keyword关键字
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data":{
            "total": 10,
            "items":[
                {
                    "id":"主键ID",
                    "name":"租户名称",
                    "code":"租户代码",
                    "description":"描述"
                }
            ]
        }
    }
    **/
    """
    return True, None, project_service.get_project_list(ProjectQueryModel(**kwargs)).get_result_dict()


@sapi.internal_route.get('/query')
@sapi.internal_route.post('/query')
def query_sql_api(response, **kwargs):
    data = kwargs.get('data', '')
    decrypted_data = query_sql_service.validate_request_data(data=data)
    if not decrypted_data:
        response.status = hug.falcon.HTTP_404
        response.body = hug.falcon.HTTP_404
        return

    sql = decrypted_data.get('sql', '')
    code = decrypted_data.get('code', '')
    is_config_db = decrypted_data.get('is_config_db', 0)
    if not sql:
        raise UserError(message='缺乏必要的参数!')

    sql_data = query_sql_service.query(code, sql, is_config_db)

    response.body = query_sql_service.encrypt_sql_data(json.dumps(sql_data))
