#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/3.
"""
from base.models import BaseModel, QueryBaseModel


class ResourceModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = ''
        self.name = ''
        self.type = 'jar'
        self.remark = ''
        self.url = ''
        self.form_mode = 'add'
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        if self.form_mode == 'add':
            rules.append(('name', 'string', {'max': 50}))
        rules.append(('remark', 'string', {'max': 500, 'required': False}))
        rules.append(('url', 'string', {'max': 500}))
        return rules


class ResourceQueryModel(QueryBaseModel):
    def __init__(self, **kwargs):
        self.type = ''
        super().__init__(**kwargs)


class FunctionModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = ''
        self.func_name = ''
        self.class_name = ''
        self.resource_id = ''
        self.purpose = ''
        self.cmd_format = ''
        self.param_remark = ''
        self.form_mode = 'add'
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        if self.form_mode == 'add':
            rules.append(('func_name', 'string', {'max': 50}))
        rules.append(('class_name', 'string', {'max': 254}))
        rules.append(('resource_id', 'string', {'max': 36}))
        rules.append((['purpose', 'cmd_format', 'param_remark'], 'string', {'max': 1000, 'required': False}))
        return rules


class FunctionQueryModel(QueryBaseModel):
    pass
