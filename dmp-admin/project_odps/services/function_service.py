#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/3.
"""
from odps.core import ODPS

from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from project_odps.models import FunctionModel
from project_odps.repositories import function_repository
from project_odps.services import resource_service


def get_function_list(query_model):
    """
    获取函数列表
    :param project_odps.models.FunctionQueryModel query_model:
    :return:project_odps.models.FunctionQueryModel
    """
    return function_repository.get_function_list(query_model)


def get_function(function_id):
    """
    获取函数
    :param str function_id:
    :return: project_odps.models.FunctionModel
    """
    if not function_id:
        raise UserError(message='缺少函数id')
    fields = ['id', 'func_name', 'class_name', 'resource_id', 'purpose', 'cmd_format', 'param_remark']
    data = repository.get_data('dap_bi_odps_func', {'id': function_id}, fields)
    if not data:
        raise UserError(message='函数不存在')
    return FunctionModel(**data)


def add_function(model):
    """
    添加函数
    :param project_odps.models.FunctionModel model:
    :return: project_odps.models.FunctionModel
    """
    model.id = seq_id()
    model.form_mode = 'add'
    model.validate()
    if repository.data_is_exists('dap_bi_odps_func', {'func_name': model.func_name}):
        raise UserError(message='函数(' + model.func_name + ')已经存在')
    fields = ['id', 'func_name', 'class_name', 'resource_id', 'purpose', 'cmd_format', 'param_remark']
    repository.add_model('dap_bi_odps_func', model, fields)
    return model


def update_function(model):
    """
    修改函数
    :param project_odps.models.FunctionModel model:
    :return:project_odps.models.FunctionModel
    """
    model.validate()
    if not repository.data_is_exists('dap_bi_odps_func', {'id': model.id}):
        raise UserError(message='函数不存在')
    fields = ['class_name', 'resource_id', 'purpose', 'cmd_format', 'param_remark']
    try:
        _update_odps_function(model)
    except Exception as e:
        raise UserError(message='更新已经使用ODPS函数失败:' + str(e))
    return repository.update_model('dap_bi_odps_func', model, {'id': model.id}, fields)


def _update_odps_function(model):
    """
    更新已经使用ODPS函数
    :param project_odps.models.FunctionModel model:
    :return:
    """
    used_function = function_repository.get_used(model.id)
    if not used_function:
        return
    resource = resource_service.get_resource(model.resource_id)
    for func in used_function:
        odps = ODPS(func.get('odps_access_id'), func.get('odps_access_secret'), func.get('odps_proj'))
        if not odps.exist_resource(resource.name):
            odps.create_resource(resource.name, resource.type,
                                 file_obj=resource_service.get_resource_content(resource.url))
        func_name = func.get('func_name')
        if odps.exist_function(func_name):
            odps.delete_function(func_name)
        odps.create_function(func_name, class_type=model.class_name, resources=[odps.get_resource(resource.name)])


def delete_function(function_id):
    """
    删除函数
    :param function_id:
    :return:
    """
    if not repository.data_is_exists('dap_bi_odps_func', {'id': function_id}):
        raise UserError(message='函数不存在')
    try:
        _delete_odps_function(function_id)
    except Exception as e:
        raise UserError(message='删除已经使用的ODPS函数失败:' + str(e))
    record = repository.delete_data('dap_bi_odps_func', {'id': function_id})
    record += repository.delete_data('dap_bi_project_odps_func', {'func_id': function_id})
    return record


def _delete_odps_function(function_id):
    """
    删除已经使用的ODPS函数
    :param function_id:
    :return:
    """
    used_function = function_repository.get_used(function_id)
    if not used_function:
        return
    for func in used_function:
        odps = ODPS(func.get('odps_access_id'), func.get('odps_access_secret'), func.get('odps_proj'))
        func_name = func.get('func_name')
        if odps.exist_function(func_name):
            odps.delete_function(func_name)


def get_function_and_resource(function_id):
    """
    获取函数与资源
    :param str|list function_id:
    :return: list
    """
    if not function_id:
        raise UserError(message='缺少函数id')
    return function_repository.get_function_and_resource(function_id)
