#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/3.
"""
import os

from odps.core import ODPS

from base import repository
from dmplib.utils.errors import UserError
from components.oss import OSSFileProxy
from dmplib.utils.strings import seq_id
from project_odps.models import ResourceModel
from project_odps.repositories import resource_repository


def get_resource_list(query_model):
    """
    获取资源列表
    :param project_odps.models.ResourceQueryModel query_model:
    :return: project_odps.models.ResourceQueryModel
    """
    return resource_repository.get_resource_list(query_model)


def get_resource(resource_id):
    if not resource_id:
        raise UserError(message='缺少资源id')
    fields = ['id', 'name', 'type', 'remark', 'url']
    data = repository.get_data('dap_bi_odps_resource', {'id': resource_id}, fields)
    if not data:
        raise UserError(message='资源不存在')
    return ResourceModel(**data)


def add_resource(model):
    """
    添加资源
    :param project_odps.models.ResourceModel model:
    :return: project_odps.models.ResourceModel
    """
    model.id = seq_id()
    model.form_mode = 'add'
    model.validate()
    if repository.data_is_exists('dap_bi_odps_resource', {'name': model.name}):
        raise UserError(message='资源名称(' + model.name + ')已经存在')
    fields = ['id', 'name', 'type', 'remark', 'url']
    repository.add_model('dap_bi_odps_resource', model, fields)
    return model


def update_resource(model):
    """
    更新资源（仅更新资源文件）
    :param project_odps.models.ResourceModel model:
    :return:
    """
    model.form_mode = 'update'
    model.validate()
    fields = ['type', 'remark', 'url']
    try:
        _update_odps_resource(model)
    except Exception as e:
        raise UserError(message='更新已经使用的ODPS资源失败:' + str(e))
    return repository.update_model('dap_bi_odps_resource', model, {'id': model.id}, fields)


def _update_odps_resource(model):
    """
    更新已经使用的ODPS资源
    :param project_odps.models.ResourceModel model:
    :return:
    """
    used_resource = resource_repository.get_used(model.id)
    if not used_resource:
        return
    updated_res = []
    for resource in used_resource:
        resource_name = resource.get('resource_name')
        identity = resource.get('odps_proj') + '$' + resource_name
        if identity in updated_res:
            continue
        odps = ODPS(resource.get('odps_access_id'), resource.get('odps_access_secret'), resource.get('odps_proj'))
        if odps.exist_resource(resource_name):
            odps.delete_resource(resource_name)
        odps.create_resource(resource_name, model.type, file_obj=get_resource_content(model.url))
        updated_res.append(identity)


def delete_resource(resource_id):
    """
    删除资源
    :param str resource_id:
    :return:
    """
    get_resource(resource_id)
    try:
        _delete_odps_resource(resource_id)
    except Exception as e:
        raise UserError(message='删除已经使用的ODPS资源失败:' + str(e))
    record = resource_repository.delete_used(resource_id)
    record += repository.delete_data('dap_bi_odps_resource', {'id': resource_id})
    record += repository.delete_data('dap_bi_odps_func', {'resource_id': resource_id})
    return record


def _delete_odps_resource(resource_id):
    """
    删除已经使用的ODPS资源
    :param resource_id:
    :return:
    """
    used_resource = resource_repository.get_used(resource_id)
    if not used_resource:
        return
    for resource in used_resource:
        odps = ODPS(resource.get('odps_access_id'), resource.get('odps_access_secret'), resource.get('odps_proj'))
        func_name = resource.get('func_name')
        if odps.exist_function(func_name):
            odps.delete_function(func_name)
        resource_name = resource.get('resource_name')
        if odps.exist_resource(resource_name):
            odps.delete_resource(resource_name)


def get_resource_content(resource_url):
    """
    获取资源内容
    :param resource_url:
    :return byte:
    """
    try:
        return OSSFileProxy().get_object_content(resource_url)
    except Exception as e:
        raise UserError(message='获取资源文件失败：' + str(e))
