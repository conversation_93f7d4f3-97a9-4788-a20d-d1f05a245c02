# !/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/3.
"""
from dmplib.db.mysql_wrapper import get_db


def get_function_list(query_model):
    """
    获取函数列表（只包含自定义函数）
    :param project_odps.models.FunctionQueryModel query_model:
    :return:project_odps.models.FunctionQueryModel
    """
    sql = 'SELECT f.`id`,f.`func_name`,f.`class_name`,f.`resource_id`,' \
          's.`name` as `resource_name`,f.`purpose`,f.`cmd_format`,f.`param_remark` FROM `dap_bi_odps_func` as f ' \
          'INNER JOIN `dap_bi_odps_resource` as s on f.`resource_id`=s.`id` '
    params = {}
    wheres = ['f.`category` IS NULL']
    if query_model.keyword:
        wheres.append('f.`func_name` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY f.created_on DESC'
    with get_db() as db:
        query_model.total = db.query_scalar('select count(*) as total from ({}) a'.format(sql), params)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_function_and_resource(function_id):
    """
    获取函数与资源
    :param str|list function_id:
    :return: list
    """
    sql = 'SELECT f.id,f.func_name,f.class_name,' \
          'r.`name` AS resource_name, r.type AS resource_type, r.url AS resource_lib ' \
          'FROM dap_bi_odps_func AS f ' \
          'INNER JOIN dap_bi_odps_resource AS r ON f.resource_id=r.id ' \
          'WHERE f.category is NULL '
    params = {}
    if isinstance(function_id, str):
        params['func_id'] = function_id
    else:
        i_temp = 1
        for func_id in function_id:
            params['func_id' + str(i_temp)] = func_id
            i_temp += 1
    if params:
        sql += ' AND f.id IN (%(' + ')s,%('.join(params.keys()) + ')s)'
    with get_db() as db:
        return db.query(sql, params)


def get_used(function_id):
    """
    获取已经使用的ODPS函数
    :param function_id:
    :return:
    """
    sql = 'SELECT DISTINCT p.odps_proj,p.odps_access_id,p.odps_access_secret,f.func_name FROM dap_bi_odps_func AS f ' \
          'INNER JOIN dap_bi_project_odps_func AS pf ON pf.func_id=f.id ' \
          'INNER JOIN dap_bi_tenant_setting AS p ON p.id=pf.project_id ' \
          'WHERE f.id=%(func_id)s'
    with get_db() as db:
        return db.query(sql, {'func_id': function_id})
