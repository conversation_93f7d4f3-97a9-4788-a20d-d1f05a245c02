#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/3.
"""
from dmplib.db.mysql_wrapper import get_db


def get_resource_list(query_model):
    """
    获取资源列表
    :param project_odps.models.ResourceQueryModel query_model:
    :return:project_odps.models.ResourceQueryModel
    """
    sql = 'SELECT `id`,`name`,`type`,`remark`,`url` FROM `dap_bi_odps_resource` '
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('`name` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY created_on DESC'
    with get_db() as db:
        query_model.total = db.query_scalar('select count(*) as total from ({}) a'.format(sql), params)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_used(resource_id):
    """
    获取已经使用的资源
    :param resource_id:
    :return:
    """
    sql = 'SELECT DISTINCT p.odps_proj,p.odps_access_id,p.odps_access_secret,s.`name` AS resource_name,f.func_name ' \
          'FROM dap_bi_odps_resource AS s ' \
          'INNER JOIN dap_bi_odps_func AS  f ON f.resource_id=s.id ' \
          'INNER JOIN dap_bi_project_odps_func AS pf ON pf.func_id=f.id ' \
          'INNER JOIN dap_bi_tenant_setting AS p ON p.id=pf.project_id ' \
          'WHERE s.id=%(resource_id)s'
    with get_db() as db:
        return db.query(sql, {'resource_id': resource_id})


def delete_used(resource_id):
    """
    删除已经使用的ODPS资源
    :param resource_id:
    :return:
    """
    sql = 'DELETE pf FROM dap_bi_project_odps_func AS pf ' \
          'INNER JOIN dap_bi_odps_func AS  f ON pf.func_id=f.id ' \
          'WHERE f.resource_id=%(resource_id)s'
    with get_db() as db:
        return db.exec_sql(sql)
