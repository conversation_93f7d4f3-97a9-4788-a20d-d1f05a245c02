#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/3.
"""

from dmplib.hug import APIWrapper
from project_odps.models import ResourceQueryModel, ResourceModel, FunctionQueryModel, FunctionModel
from project_odps.services import resource_service, function_service

api = APIWrapper(__name__)


@api.admin_route.get('/resource/list')
def get_resource_list(**kwargs):
    """
    获取资源列表
    :param kwargs:
    :return:
    """
    return True, None, resource_service.get_resource_list(ResourceQueryModel(**kwargs))


@api.admin_route.get('/resource/get')
def get_resource(**kwargs):
    """
    获取资源
    :param kwargs:
    :return:
    """
    return True, None, resource_service.get_resource(kwargs.get('id'))


@api.admin_route.post('/resource/add')
def add_resource(**kwargs):
    """
    添加资源
    :param kwargs:
    :return:
    """
    return True, '添加成功', resource_service.add_resource(ResourceModel(**kwargs)).id


@api.admin_route.post('/resource/update')
def update_resource(**kwargs):
    """
    修改资源
    :param kwargs:
    :return:
    """
    return True, '修改成功', resource_service.update_resource(ResourceModel(**kwargs))


@api.admin_route.post('/resource/delete')
def delete_resource(**kwargs):
    """
    删除资源
    :param kwargs:
    :return:
    """
    return True, '删除成功', resource_service.delete_resource(kwargs.get('id'))


@api.admin_route.get('/function/list')
def get_function_list(**kwargs):
    """
    获取函数列表
    :param kwargs:
    :return:
    """
    return True, None, function_service.get_function_list(FunctionQueryModel(**kwargs))


@api.admin_route.get('/function/get')
def get_function(**kwargs):
    """
    获取函数
    :param kwargs:
    :return:
    """
    return True, None, function_service.get_function(kwargs.get('id'))


@api.admin_route.post('/function/add')
def add_function(**kwargs):
    """
    添加函数
    :param kwargs:
    :return:
    """
    return True, '添加成功', function_service.add_function(FunctionModel(**kwargs)).id


@api.admin_route.post('/function/update')
def update_function(**kwargs):
    """
    修改函数
    :param kwargs:
    :return:
    """
    return True, '修改成功', function_service.update_function(FunctionModel(**kwargs))


@api.admin_route.post('/function/delete')
def delete_function(**kwargs):
    """
    删除函数
    :param kwargs:
    :return:
    """
    return True, '删除成功', function_service.delete_function(kwargs.get('id'))
