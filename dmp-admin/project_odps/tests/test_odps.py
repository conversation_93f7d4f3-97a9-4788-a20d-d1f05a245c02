#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    todo 测试创建项目
    <NAME_EMAIL> on 2018/4/23.
"""

import unittest

from tests.base import BaseTest

import logging
from project_odps.services.resource_service import get_resource_content

logger = logging.getLogger(__name__)


class TestProject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_get_content(self):
        url = "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/upload-file/****************/oss导出_20201022190427.zip"
        content = get_resource_content(url)
        print(content)


if __name__ == '__main__':
    unittest.main()
