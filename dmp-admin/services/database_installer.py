#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    DatabaseInstaller
    Created by wangl10 on 2016/9/7.
"""
import logging
import os
import pymysql
import base64
import hashlib
import hmac
import uuid
import subprocess
import tempfile 
from time import sleep
from datetime import datetime
from urllib import request
from urllib.error import HTTPError
from urllib.parse import quote, urlencode
from raven.utils import json

from base.sql_adapter import adapter_sql, FILE_SUFFIX
from dmplib.db.mysql_wrapper import get_db, SimpleMysql

logger = logging.getLogger(__name__)


class DatabaseInstaller:
    def __init__(self, project):
        self._project = project
        # 项目库基础库名称
        self._project_db_name = project['tenant_db_name']
        # 项目库数据库名称
        self._project_data_db_name = self._project_db_name + '_' + os.environ.get('PROJECT_DATA_DB_NAME_SUFFIX')
        # 项目空库名称
        self._project_empty_db_name = os.environ.get('PROJECT_EMPTY_DB_NAME')
        self._db_instance = None
        self._sql_back_file_name = tempfile.NamedTemporaryFile(suffix=FILE_SUFFIX, prefix="dmp_").name
        self._success_db_created = dict()

    def install(self):
        res = self._process_install()
        self._remove_db_back_file()
        if res is False:
            """数据库已经创建，其他环节失败，删除已经创建数据库"""
            if self._success_db_created.get(self._project_db_name):
                self._drop_db(self._project_db_name)
            if self._success_db_created.get(self._project_data_db_name):
                self._drop_db(self._project_data_db_name)
        return res

    def _process_install(self):
        if not self._create_db(self._project_db_name):
            logger.error('crete new project db failed')
            return False
        if not self._create_db(self._project_data_db_name):
            logger.error('crete new project data db failed')
            return False
        result, msg = self._backup_empty_db()
        if not result:
            logger.error('backup empty failed, error message is :' + msg)
            return False
        result, msg =self._restore_project_db()
        if not result:
            logger.error('restore db failed, error message is :' + msg)
            return False
        if not self._init_db_data():
            logger.error('init data failed')
            return False
        return True

    def _create_db(self, db_name):
        pass

    def _backup_empty_db(self):
        """
        备份空库，用于创建项目库之后进行初始化
        :return:
        """
        db_instance = self._get_db_instance()
        if not db_instance:
            return False
        cmd = adapter_sql('export_sql') % (
            db_instance['host'],
            db_instance['port'],
            db_instance['account'],
            db_instance['pwd'],
            self._project_empty_db_name,
            self._sql_back_file_name
        )
        exec_status, exec_result = subprocess.getstatusoutput(cmd)
        return exec_status == 0 and os.path.exists(self._sql_back_file_name), exec_result

    def _restore_project_db(self):
        """
        将项目空库备份文件还原到新的项目数据库
        :return:
        """
        db_instance = self._get_db_instance()
        if not db_instance:
            return False
        cmd = adapter_sql('import_sql') % (
            db_instance['host'],
            db_instance['port'],
            db_instance['account'],
            db_instance['pwd'],
            self._project_db_name,
            self._sql_back_file_name
        )
        logger.error(self._sql_back_file_name)
        exec_status, exec_result = subprocess.getstatusoutput(cmd)
        return os.path.exists(self._sql_back_file_name) and exec_status == 0, exec_result

    def _init_db_data(self):
        """
        初始项目库数据
        :return:
        """
        try:
            with self.get_project_db(self._project_db_name) as conn:
                conn.update('dap_p_user', {'name': self._project['name'], 'account': self._project['code']})
                conn.update('dap_p_user_group', {'name': self._project['cust_name']})
        except pymysql.Error as me:
            logger.error('初始默认用户错误' + str(me))
            return False
        return True

    def _remove_db_back_file(self):
        """删除sql备份文件"""
        if self._sql_back_file_name and os.path.exists(self._sql_back_file_name):
            os.remove(self._sql_back_file_name)

    def uninstall(self):
        if self._drop_db(self._project_db_name) and self._drop_db(self._project_data_db_name):
            return True
        else:
            return False

    def _drop_db(self, db_name):
        pass

    def _get_db_instance(self):
        """
        获取项目所属库实例信息
        :return:
        """
        if not self._db_instance and self._project and self._project['rds_id']:
            sql = 'SELECT * FROM dap_bi_rds WHERE id= %(rds_id)s'
            try:
                with get_db() as db:
                    db.connect()
                    self._db_instance = db.query_one(sql, {'rds_id': self._project['rds_id']})
            except pymysql.Error:
                return None
        if self._db_instance:
            return self._db_instance
        return None

    def get_project_db(self, db_name):
        """
        获取项目库链接
        :return:
        """
        db_instance = self._get_db_instance()
        if not db_instance:
            return None
        return SimpleMysql(host=db_instance['host'],
                           port=db_instance['port'],
                           db=db_name,
                           user=db_instance['account'],
                           passwd=db_instance['pwd'])

    @staticmethod
    def create(project):
        mysql_instance_type = os.environ.get('MYSQL_INSTANCE_TYPE')
        if not mysql_instance_type:
            return None
        if not project:
            return None
        if str(mysql_instance_type).lower() == 'local':
            return LocalDatabaseInstaller(project)
        elif str(mysql_instance_type).lower() == 'rds':
            return RdsDatabaseInstaller(project)


class LocalDatabaseInstaller(DatabaseInstaller):
    def __init__(self, project):
        DatabaseInstaller.__init__(self, project)

    def _create_db(self, db_name):
        """
        创建数据库
        :return:
        """
        sql = 'CREATE DATABASE ' + db_name
        db_instance = self._get_db_instance()
        try:
            conn = pymysql.connect(host=db_instance['host'],
                                   port=db_instance['port'],
                                   user=db_instance['account'],
                                   passwd=db_instance['pwd'],
                                   charset='utf8',
                                   connect_timeout=3,
                                   ssl_disabled=True)
            cursor = conn.cursor()
            cursor.execute(sql)
        except pymysql.Error as e:
            logger.error('创建数据库' + db_name + '异常' + str(e))
            return False
        self._success_db_created[db_name] = True
        return True

    def _drop_db(self, db_name):
        """
        删除数据库
        :return:
        """
        sql = 'DROP DATABASE ' + db_name
        db_instance = self._get_db_instance()
        try:
            conn = pymysql.connect(host=db_instance['host'],
                                   port=db_instance['port'],
                                   user=db_instance['account'],
                                   passwd=db_instance['pwd'],
                                   charset='utf8',
                                   connect_timeout=3, ssl_disabled=True)
            cursor = conn.cursor()
            cursor.execute(sql)
        except pymysql.Error as e:
            logger.error('删除数据库' + db_name + '异常' + str(e))
            return False
        return True


class RdsDatabaseInstaller(DatabaseInstaller):
    def __init__(self, project):
        self._access_key_id = os.environ.get('RDS_ACCESS_KEY_ID')
        self._access_key_secret = os.environ.get('RDS_ACCESS_KEY_SECRET')
        if self._access_key_id and self._access_key_secret:
            self._rds_api = RdsApi(self._access_key_id, self._access_key_secret)
        DatabaseInstaller.__init__(self, project)

    def _check_db_status(self, db_name, grant_account_privilege):
        db_instance_id = self._get_db_instance_id()
        if not db_instance_id:
            return False
        check_res = False
        db_check_max_count = 100
        check_count = 1
        while check_count < db_check_max_count:
            res = self._rds_api.describe_databases(db_instance_id, db_name)
            if not res['result']:
                check_res = False
                logger.error('检查数据库' + db_name + '运行状态出错')
                break
            else:
                database = res['data'].get('Databases').get('Database')
                if not database:
                    """数据库未就绪，3秒后继续检查"""
                    sleep(3)
                else:
                    check_res = self.deal_database(database, grant_account_privilege, db_name)
                    if not check_res:
                        continue
                    else:
                        break
            check_count += 1
        return check_res

    def deal_database(self, database, grant_account_privilege, db_name):
        if not database:
            """数据库未就绪，3秒后继续检查"""
            sleep(3)
        else:
            if grant_account_privilege:
                if not database[0].get('Accounts').get('AccountPrivilegeInfo'):
                    """账号授权未就绪，3秒后继续检查"""
                    sleep(3)
                else:
                    """检查授权"""
                    try:
                        self.get_project_db(db_name)
                    except pymysql.Error:
                        sleep(3)
                        return False
                    return True
            else:
                return True
        return False

    def _create_db(self, db_name):
        if not self._rds_api:
            return False
        db_instance_id = self._get_db_instance_id()
        if not db_instance_id:
            return False
        """创建数据库"""
        res = self._rds_api.create_database(db_instance_id, db_name, db_name)
        if not res['result']:
            return False
        """必须等待，等待阿里数据库状态完成，否则授权出错：The operation is not permitted due to status of the database."""
        if not self._check_db_status(db_name, False):
            logger.error(db_name + '数据库状态未就绪')
            return False
        self._success_db_created[db_name] = True

        """授权账号"""
        db_account = self._get_db_account()
        if not db_account:
            return False
        res = self._rds_api.grant_account_privilege(db_instance_id, db_name, db_account)
        if not res['result']:
            return False
        """必须等待，等待阿里数据库状态完成，否则还原出错"""
        if not self._check_db_status(db_name, True):
            logger.error(db_name + '数据库授权未就绪')
            return False
        return True

    def _drop_db(self, db_name):
        if not self._rds_api:
            return False
        db_instance_id = self._get_db_instance_id()
        if not db_instance_id:
            return False
        if not self._check_db_status(db_name, False):
            logger.error(db_name + '数据库状态未就绪')
            return False
        """删除数据库"""
        res = self._rds_api.delete_database(db_instance_id, db_name)
        if not res['result']:
            return False
        return True

    def _get_db_instance_id(self):
        db_instance = self._get_db_instance()
        if not db_instance:
            return None
        host = str.split(db_instance['host'], '.')
        if len(host) < 1:
            return None
        return host[0]

    def _get_db_account(self):
        db_instance = self._get_db_instance()
        if not db_instance:
            return None
        return db_instance['account']


class RdsApi:
    def __init__(self, access_key_id, access_key_secret):
        self._domain = 'rds.aliyuncs.com'
        self._access_key_id = access_key_id
        self._access_key_secret = access_key_secret

    def create_database(self, db_instance_id, db_name, db_description=None, character_set_name='utf8'):
        """
        创建数据库
        :param db_instance_id:实例名
        :param db_name:数据库名称
        :param character_set_name:字符集，默认为utf8
        :param db_description: 数据库备注
        :return:
        """
        parameters = {
            'Action': 'CreateDatabase',
            'DBInstanceId': db_instance_id,
            'DBName': db_name,
            'CharacterSetName': character_set_name,
            'DBDescription': db_description
        }
        return self._api_request(parameters)

    def grant_account_privilege(self, db_instance_id, db_name, account_name, account_privilege='ReadWrite'):
        """
        授权账号权限
        :param db_instance_id:
        :param db_name:
        :param account_name:
        :param account_privilege:
        :return:
        """
        parameters = {
            'Action': 'GrantAccountPrivilege',
            'DBInstanceId': db_instance_id,
            'DBName': db_name,
            'AccountName': account_name,
            'AccountPrivilege': account_privilege
        }
        return self._api_request(parameters)

    def describe_databases(self, db_instance_id, db_name=None, db_status='Running'):
        """
        获取数据列表
        :param db_instance_id:
        :param db_name:
        :param db_status:Creating,Running,Deleting
        :return:
        """
        parameters = {
            'Action': 'DescribeDatabases',
            'DBInstanceId': db_instance_id
        }
        if db_name:
            parameters['DBName'] = db_name
        if db_status:
            parameters['DBStatus'] = db_status

        return self._api_request(parameters)

    def delete_database(self, db_instance_id, db_name):
        """
        删除数据库
        :param db_instance_id:
        :param db_name:
        :return:
        """
        parameters = {
            'Action': 'DeleteDatabase',
            'DBInstanceId': db_instance_id,
            'DBName': db_name
        }
        return self._api_request(parameters)

    def _refresh_parameters(self, parameters):
        if parameters is None or not isinstance(parameters, dict):
            parameters = dict()
        parameters['Format'] = 'JSON'
        parameters['Version'] = '2014-08-15'
        parameters['AccessKeyId'] = self._access_key_id
        parameters['SignatureMethod'] = 'HMAC-SHA1'
        parameters['Timestamp'] = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%SZ')
        parameters['SignatureVersion'] = '1.0'
        parameters['SignatureNonce'] = str(uuid.uuid4())
        return parameters

    def _parameters_sign(self, parameters, request_method='GET'):
        query_string = ''
        sorted_parameters = sorted(parameters.items(), key=lambda item: item[0])
        for (k, v) in sorted_parameters:
            query_string += '&' + self.percent_encode(k) + '=' + self.percent_encode(v)
        sign_to_string = request_method + "&%2F&" + self.percent_encode(query_string[1:])
        h = hmac.new(str.encode(self._access_key_secret + '&'), str.encode(sign_to_string), hashlib.sha1)
        signature = bytes.decode(base64.b64encode(h.digest())).strip()
        parameters['Signature'] = signature
        return parameters

    def _get_signed_url(self, parameters):
        parameters = self._refresh_parameters(parameters)
        parameters = self._parameters_sign(parameters)
        url = ('https://%s/?%s' % (self._domain, urlencode(parameters)))
        return url

    def _api_request(self, parameters):
        data = None
        try:
            with request.urlopen(self._get_signed_url(parameters)) as res:
                result = True
                tmp_response = bytes.decode(res.read())
        except HTTPError as e:
            result = False
            tmp_response = bytes.decode(e.read())
        if tmp_response:
            data = json.loads(tmp_response)
        if not result:
            logger.error('调用RDS_API失败:%s,参数:%s' % (tmp_response, json.dumps(parameters)))

        return {'result': result, 'data': data}

    @staticmethod
    def percent_encode(encode_str):
        res = quote(str(encode_str))
        res = res.replace('+', '%20')
        res = res.replace('*', '%2A')
        res = res.replace('%7E', '~')
        return res
