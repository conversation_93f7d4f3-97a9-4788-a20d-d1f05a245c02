#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    DatabaseInstaller
    Created by wangl10 on 2016/9/7.
"""
import time
import builtins

from dmplib.hug import APIWrapper
from dmplib.hug.lic import Licenser


class MockBoot:
    def start_validate(self):
        return


builtins.license_validate = dict(
    failed_times=0,
    limit_times=3,
    is_invalid=False,
    last_verify_result=True,
    last_verify_at=time.time(),
    last_pull_at=time.time(),
    message='',
)

builtins.boot_process_obj = MockBoot()
builtins.interval = dict(
    verify=1 * 60 * 60,
    pull=0.25 * 1 * 60 * 60
)

try:
    from dmplib.umbrella.middleware import LicenseMiddleware
    LicenseMiddleware = LicenseMiddleware
except:
    LicenseMiddleware = None

# api = APIWrapper(__name__)


# patch old license middleware
def patch_lic(api):
    middlewares = api.http.middleware or []  # type: list
    for mw in middlewares.copy():
        if isinstance(mw, Licenser):
            middlewares.remove(mw)

        # 可能新的license不存在
        if LicenseMiddleware is not None and isinstance(mw, LicenseMiddleware):
            middlewares.remove(mw)
