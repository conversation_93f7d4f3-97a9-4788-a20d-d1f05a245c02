#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
变量取值来源数据迁移升级

备注：
1，支持重复执行

"""

# ---------------- 标准模块 ----------------
import logging
from collections import defaultdict

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.utils.strings import seq_id
from base.tenant_repository import replace_data

logger = logging.getLogger(__name__)


class DatasetVarsValueSourceUpgradeCommand(UpgradeCommand):
    def exec(self):
        if not self.tenant_code:
            return True

        try:
            # 获取当前租户下的所有变量
            vars_dict = self._get_dataset_var_dict()

            # 获取单图引用到的变量
            dashboard_var_dict = self._collect_vars()

            # 执行升级
            self._op_upgrade(dashboard_var_dict, vars_dict)

        except BaseException as e:
            message = '''Error 租户code:{0} TaskId:{1} 变量取值来源数据迁移升级 异常信息{2}'''.format(
                self.tenant_code, self.task_id, str(e)
            )
            logger.exception(message)
            return False

    def _get_dataset_var_dict(self):
        """
        获取所有的数据集变量
        :return:
        """
        results = {}
        sql = """SELECT * FROM dap_bi_dataset_vars """
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql)
        if query_data:
            for item in query_data:
                results[item.get("id")] = item
        return results

    def _add_to_var_dict(self, sql, dashboard_var_dict):
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql)
        if query_data:
            for i in query_data:
                dashboard_var_dict[i.get("dashboard_id")].add(i.get("var_id"))

    def _collect_vars(self):
        """
        从单图的各个组成部分收集变量
        :return:
        """
        dashboard_var_dict = defaultdict(set)

        # dim
        sql = """SELECT dcd.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_dim dcd 
                  JOIN dap_bi_dataset_field df  ON dcd.dim=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        # num
        sql = """SELECT dcn.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_num dcn 
                  JOIN dap_bi_dataset_field df  ON dcn.num=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        # desire
        sql = """SELECT dcd.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_desire dcd 
                  JOIN dap_bi_dataset_field df  ON dcd.dataset_field_id=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        # comparison
        sql = """SELECT dcc.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_comparison dcc 
                  JOIN dap_bi_dataset_field df  ON dcc.dataset_field_id=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        # params
        sql = """SELECT dcp.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_params dcp 
                  JOIN dap_bi_dataset_field df  ON dcp.dataset_field_id=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        # markline
        sql = """SELECT dcm.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_markline dcm 
                  JOIN dap_bi_dataset_field df  ON dcm.num=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        # filter
        sql = """SELECT dcf.dashboard_id,dfiv.var_id FROM dap_bi_dashboard_chart_filter dcf 
                  JOIN dap_bi_dataset_field df  ON dcf.dataset_field_id=df.id
                  JOIN dap_bi_dataset_field_include_vars dfiv ON df.id=dfiv.field_id"""
        self._add_to_var_dict(sql, dashboard_var_dict)

        return dashboard_var_dict

    def _get_vars_relation_dict(self, results):
        """
        获取筛选器绑定变量关系
        :return:
        """
        sql = """SELECT * FROM dap_bi_dashboard_dataset_vars_relation """
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql)
        if query_data:
            for item in query_data:
                if item.get("dashboard_id") and item.get("var_id"):
                    results[item.get("dashboard_id")].add(item.get("var_id"))

    def _get_vars_jump_relation_dict(self, results):
        """
        获取变量跳转关系
        :return:
        """
        sql = """SELECT * FROM dap_bi_dashboard_vars_jump_relation """
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql)
        if query_data:
            for item in query_data:
                if item.get("dashboard_id") and item.get("var_id"):
                    results[item.get("dashboard_id")].add(item.get("var_id"))

    def _add_value_source(self, dashboard_id, value_source, value_identifier):
        """
        新增取值来源
        :param dashboard_id:
        :param value_source:
        :param value_identifier:
        :return:
        """
        value_source_id = seq_id()
        data_for_add = {
            "id": value_source_id,
            "dashboard_id": dashboard_id,
            "value_source": value_source,
            "value_source_name": value_identifier,
            "value_identifier": value_identifier,
            "created_by": "celery",
            "modified_by": "celery",
        }
        fields = [
            "id",
            "dashboard_id",
            "value_source",
            "value_source_name",
            "value_identifier",
            "created_by",
            "modified_by",
        ]
        replace_data(self.tenant_code, "dap_bi_dashboard_value_source", [data_for_add], fields, commit=True, condition_field=['id'])
        return value_source_id

    def _add_var_value_source_relation(self, dashboard_id, var_id, value_source_id):
        """
        插入绑定关系
        :param dashboard_id:
        :param var_id:
        :param value_source_id:
        :return:
        """
        data_for_add = {
            "dashboard_id": dashboard_id,
            "var_id": var_id,
            "value_source_id": value_source_id,
            "created_by": "celery",
            "modified_by": "celery",
        }
        fields = ["dashboard_id", "var_id", "value_source_id", "created_by", "modified_by"]
        res = replace_data(
            self.tenant_code, "dap_bi_dashboard_vars_value_source_relation", [data_for_add], fields, commit=True, condition_field=['dashboard_id', 'var_id', 'value_source_id']
        )
        return res

    def _get_db_value_source_dict(self, dashboard_id):
        """
        获取dict结构数据
        :param dashboard_id:
        :return:
        """
        results = {}
        sql = """SELECT * FROM dap_bi_dashboard_value_source WHERE dashboard_id=%(dashboard_id)s """
        params = {"dashboard_id": dashboard_id}
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql, params)
        if not query_data:
            return results
        for item in query_data:
            value_source = item.get("value_source")
            value_identifier = item.get("value_identifier")
            key = "_".join([value_source, value_identifier])
            results[key] = item.get("id")
        return results

    def _save_value_source(self, dashboard_id, distinct_value_sources_dict):
        """
        保存取值来源
        :param dashboard_id:
        :param distinct_value_sources_dict:
        :return:
        """
        value_source_id_dict = {}

        # 获取db中已存在的取值来源
        db_value_source_dict = self._get_db_value_source_dict(dashboard_id)

        # 将不同的取值来源插入新表，然后输出以`value_source`_`value_identifier`为key的dict
        for value_source, identifier_list in distinct_value_sources_dict.items():
            if value_source == "userdefined":
                continue
            for single_identifier in identifier_list:
                key = "_".join([value_source, single_identifier])
                db_value_source_id = db_value_source_dict.get(key) if db_value_source_dict else None
                if db_value_source_id:
                    value_source_id_dict[key] = db_value_source_id
                    continue
                if value_source_id_dict.get(key):
                    continue
                # 取值来源插入新表
                value_source_id = self._add_value_source(dashboard_id, value_source, single_identifier)
                value_source_id_dict[key] = value_source_id
        return value_source_id_dict

    def _get_value_source_id_dict(self, var_id_list, vars_dict):
        """
        获取取值来源及对应的标识符
        :param var_id_list:
        :param vars_dict:
        :return:
        """
        distinct_value_sources_dict = defaultdict(list)
        for var_id in var_id_list:
            var_data = vars_dict.get(var_id, {})
            if not var_data:
                continue
            value_source = var_data.get("value_source")
            value_identifier = var_data.get("value_identifier", "")
            # 用户自定义只有一个
            if value_source == "userdefined" and value_source in distinct_value_sources_dict:
                continue
            distinct_value_sources_dict[value_source].append(value_identifier)
        return distinct_value_sources_dict

    def _save_var_value_source_relation(self, dashboard_id, var_id_list, vars_dict, value_source_id_dict):
        """
        保存绑定关系
        :param dashboard_id:
        :param var_id_list:
        :param vars_dict:
        :param value_source_id_dict:
        :return:
        """
        for var_id in var_id_list:
            var_data = vars_dict.get(var_id, {})
            if not var_data:
                continue
            value_source = var_data.get("value_source")
            if value_source == "userdefined":
                continue
            value_identifier = var_data.get("value_identifier", "")
            match_key = "_".join([value_source, value_identifier])
            match_value_source_id = value_source_id_dict.get(match_key)
            if not match_value_source_id:
                continue
            self._add_var_value_source_relation(dashboard_id, var_id, match_value_source_id)

    def _op_upgrade(self, distinct_dashboard_var_dict, vars_dict):
        """
        执行升级
        :param distinct_dashboard_var_dict:
        :param vars_dict:
        :return:
        """
        # 以报告id为单位，将使用到的变量及取值来源保存到两张新表
        for dashboard_id, var_id_list in distinct_dashboard_var_dict.items():
            if not dashboard_id or not var_id_list:
                continue

            # 获取以取值来源为key的所有值标识符
            distinct_value_sources_dict = self._get_value_source_id_dict(var_id_list, vars_dict)

            # 将取值来源和值标识符插入新表，获取到主键id
            value_source_id_dict = self._save_value_source(dashboard_id, distinct_value_sources_dict)

            # 将变量和取值来源的绑定关系插入新表
            self._save_var_value_source_relation(dashboard_id, var_id_list, vars_dict, value_source_id_dict)
