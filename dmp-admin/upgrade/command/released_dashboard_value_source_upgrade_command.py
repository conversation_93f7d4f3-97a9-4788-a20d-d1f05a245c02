#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
已发布报告使用的变量，取值来源数据迁移升级

备注：
1，支持重复执行
2，执行后需要清理已发布报告的redis缓存


"""

# ---------------- 标准模块 ----------------
import logging
from collections import defaultdict
import json

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from base.tenant_repository import update_data

logger = logging.getLogger(__name__)


class ReleasedDashboardValueSourceUpgradeCommand(UpgradeCommand):
    def exec(self):
        if not self.tenant_code:
            return True
        try:
            # 以报告id为单位，收集chart_vars中的变量id
            released_dashboard_vars_dict = self._collect_vars_from_released_chart()

            # 获取变量对应的定义
            vars_dict = self._assign_vars(released_dashboard_vars_dict)

            # 将拼装后的数据插入到var_value_sources字段中
            self._op_upgrade(released_dashboard_vars_dict, vars_dict)

        except BaseException as e:
            message = '''Error 租户code:{0} TaskId:{1} 已发布报告的变量取值来源数据迁移升级 异常信息{2}'''.format(
                self.tenant_code, self.task_id, str(e)
            )
            logger.exception(message)
            return False

    def _collect_vars_from_released_chart(self):
        """
        收集已发布报告中使用的变量
        :return:
        """
        results = defaultdict(set)
        sql = """SELECT snapshot_id,dashboard_id,chart_vars FROM dap_bi_dashboard_released_snapshot_chart 
                 WHERE chart_vars is not null AND chart_vars != '[]' """
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql)
        if not query_data:
            return results
        for item in query_data:
            if item.get("snapshot_id") and item.get("dashboard_id") and item.get("chart_vars"):
                key = "_".join([item.get("snapshot_id"), item.get("dashboard_id")])
                try:
                    chart_vars_list = json.loads(item.get("chart_vars"))
                except:
                    chart_vars_list = []
                results[key] |= set([single_chart_var.get("var_id", "") for single_chart_var in chart_vars_list])
        return results

    def _assign_vars(self, released_dashboard_vars_dict):
        """
        获取变量的定义
        :param released_dashboard_vars_dict:
        :return:
        """
        vars_dict = {}
        query_vars = set()
        if not released_dashboard_vars_dict:
            return vars_dict
        for collected_vars_set in released_dashboard_vars_dict.values():
            query_vars |= collected_vars_set

        sql = """SELECT * FROM dap_bi_dataset_vars WHERE id in %(distinct_query_vars)s """
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql, params={"distinct_query_vars": list(query_vars)})
        if query_data:
            for item in query_data:
                vars_dict[item.get("id")] = item
        return vars_dict

    def _get_db_value_source_dict(self, dashboard_id):
        """

        :param dashboard_id:
        :return:
        """
        results = {}
        sql = """SELECT * FROM dap_bi_dashboard_value_source WHERE dashboard_id=%(dashboard_id)s """
        params = {"dashboard_id": dashboard_id}
        with get_tenant_db(self.tenant_code) as db:
            query_data = db.query(sql, params)
        if not query_data:
            return results
        for item in query_data:
            value_source = item.get("value_source")
            value_identifier = item.get("value_identifier")
            key = "_".join([value_source, value_identifier])
            results[key] = item.get("id")
        return results

    def _update_var_value_sources(self, data, snapshot_id, dashboard_id):
        """
        更新数据
        :param data:
        :return:
        """
        return update_data(
            self.tenant_code,
            "dap_bi_dashboard_released_snapshot_dashboard",
            {"var_value_sources": json.dumps(data)},
            {"snapshot_id": snapshot_id, "id": dashboard_id},
        )

    def _get_var_value_source_dict(self, collected_vars_set, vars_dict, dashboard_id):
        """
        拼装数据
        :param collected_vars_set:
        :param vars_dict:
        :param dashboard_id:
        :return:
        """
        var_value_source_dict = {}
        # 获取db中已存在的取值来源
        db_value_source_dict = self._get_db_value_source_dict(dashboard_id)
        for var_id in collected_vars_set:
            var_info = vars_dict.get(var_id, {})
            value_source = var_info.get("value_source", "")
            value_identifier = var_info.get("value_identifier", "")
            if not value_source or value_source == "userdefined":
                continue
            if value_source not in var_value_source_dict:
                key = "_".join([value_source, value_identifier])
                db_value_source_id = db_value_source_dict.get(key) if db_value_source_dict else None
                var_value_source_dict[value_source] = {
                    "id": db_value_source_id if db_value_source_id else "",
                    "value_source_name": value_identifier,
                    "value_source": value_source,
                    "value_identifier": value_identifier,
                    "relations": [var_id],
                }
            else:
                var_value_source_dict[value_source]["relations"].append(var_id)
        return var_value_source_dict

    def _op_upgrade(self, released_dashboard_vars_dict, vars_dict):
        """
        执行升级
        :param released_dashboard_vars_dict:
        :param vars_dict:
        :return:
        """
        for union_key, collected_vars_set in released_dashboard_vars_dict.items():
            if not collected_vars_set:
                continue
            key_list = union_key.split("_") if union_key else []
            if len(key_list) != 2:
                continue
            snapshot_id, dashboard_id = key_list[0], key_list[1]
            var_value_source_dict = self._get_var_value_source_dict(collected_vars_set, vars_dict, dashboard_id)
            # 转换结构
            var_value_source_list = list(var_value_source_dict.values())
            self._update_var_value_sources(var_value_source_list, snapshot_id, dashboard_id)
