#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from components.dmp_api import DMPAPI

logger = logging.getLogger(__name__)


class RefreshDatasetIsComplex(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_dataset()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_dataset(self):
        logging.exception("开始调用dmp openapi 接口")
        dmp_api = DMPAPI(self.tenant_code)
        res = dmp_api.refresh_dataset_is_complex()
        logging.exception("调用dmp openapi结束， res: {}".format(res))

