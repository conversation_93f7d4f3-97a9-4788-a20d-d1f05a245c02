#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
删除DMP平台不活跃用户的token

eg:
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=clean_expire_user_command
"""

# ---------------- 标准模块 ----------------
import datetime
import logging

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.redis import RedisCache


logger = logging.getLogger(__name__)


class CleanExpireUserCommand(UpgradeCommand):
    """
    删除过期token
    """

    def exec(self):
        conn = RedisCache()
        logger.exception("=============开始删除登录时间大于90天的key=============")
        now_ts = int(datetime.datetime.now().timestamp())
        pattern = 'logindmp:jwt:token:*'
        for key in conn._connection.scan_iter(match=pattern, count=1000):
            ttl = conn.ttl(key)
            if ttl >= 3 * 30 * 24 * 3600 or ttl < 0: # 如果大于90天, 设置为90天
                conn.expire(key, 3 * 30 * 24 * 3600)
            raw = conn.get(key)
            try:
                data = eval(raw)
            except Exception:
                logger.warning(f"跳过token<{key}>, 解析失败")
                continue
            if data and isinstance(data.get('_flag'), int):
                flag = data.get('_flag')
                # 如果token登录时间超过90天, 清掉token
                if now_ts - flag >= 3 * 30 * 24 * 3600:
                    conn.delete(key)
        logger.exception("=============>完成缓存内容删除=============>")

        return True
