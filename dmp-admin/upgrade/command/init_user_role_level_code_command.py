#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021-8-18 10:31:19
# <AUTHOR> lulei
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db


logger = logging.getLogger(__name__)


class InitUserRoleLevelCodeCommand(UpgradeCommand):
    """
    用户角色层级level_code初始化
    """
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._init_user_role_level_code()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _init_user_role_level_code(self):
        user_role_list = self._get_user_role_list()
        if not user_role_list:
            logging.info("暂无需要初始化的角色列表")
            return False

        role_group = dict()
        for role in user_role_list:
            user_group_id = role.get("role_group_id")
            if user_group_id not in role_group and not role_group.get(user_group_id):
                role_group[user_group_id] = [role]
            else:
                role_group[user_group_id].append(role)
        if not role_group:
            logging.info("角色的分组数据为空")
            return False
        self._update_user_role(role_group)

    def _update_user_role(self, role_group):
        """
        更新用户角色的level_code
        :param role_group:
        :return:
        """
        for group_id, user_role_list in role_group.items():
            if group_id and user_role_list:
                i = 1
                for item in user_role_list:
                    level_code = (4 - len(str(i))) * '0' + str(i) + "-"
                    self._update_user_role_data(level_code, item.get("id"))
                    i += 1
                # 更新增加序列
                self._update_level_sequence(group_id, i)
                logging.info(f"分组id：{group_id}，初始化角色数：{i}")

    def _update_level_sequence(self, attach_identify, max_sequence):
        """
        更新user_role 层级序列
        :param attach_identify:
        :param max_sequence:
        :return:
        """
        with get_tenant_db(self.tenant_code) as db:
            sql = f"""
                    SELECT * FROM `dap_bi_level_sequence` 
                    where table_name = 'dap_p_user_role' and level_id = '' and attach_identify = '{attach_identify}'
                    """
            level_rs = db.query_one(sql)
            if not level_rs:
                data = {
                    "table_name": "dap_p_user_role",
                    "level_id": "",
                    "attach_identify": attach_identify,
                    "max_sequence": max_sequence,
                }
                db.insert("dap_bi_level_sequence", data)

    def _update_user_role_data(self, level_code, data_id):
        with get_tenant_db(self.tenant_code) as db:
            sql = """UPDATE dap_p_user_role SET level_code=%(level_code)s WHERE id = %(id)s"""
            return db.exec_sql(sql, {"level_code": level_code, "id": data_id})

    def _get_user_role_list(self):
        with get_tenant_db(self.tenant_code) as db:
            sql = """
                    SELECT ur.* FROM `dap_p_user_role` as ur inner join `dap_p_user_role_group` urg 
                    on ur.role_group_id = urg.id
                    where ur.parent_id = '' and ur.level_code = ''
                    order by urg.created_on asc,ur.created_on asc
                    """
            return db.query(sql)
