import logging
import traceback

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)

"""
根据指定的报表id映射
挂载在erp历史报表（1.0）升级至复杂报表（2.0）
"""


class OldErpUpgrade1To2(UpgradeCommand):
    id_templ = '11111111-0000-0000-0000-0000000'

    def ref_maps(self):
        ref_text = """
2bd4e7b0-0fad-459b-b05f-e89e7bf118a2	2bd4e7b0-0fad-459b-b05f-e89e7bf118a2
e040a7f4-aa23-4dc4-b0cb-3c4b077e32ef	e040a7f4-aa23-4dc4-b0cb-3c4b077e32ef
3d432d6d-8d4e-4a34-a386-f427909ea398	3d432d6d-8d4e-4a34-a386-f427909ea398
bd456777-1751-467d-b939-eee3583860df	bd456777-1751-467d-b939-eee3583860df
b4074e6d-aa15-4f00-86a3-a328ed179049	b4074e6d-aa15-4f00-86a3-a328ed179049
e6108bbc-f65e-4fdd-bc7b-be7673e98a23	e6108bbc-f65e-4fdd-bc7b-be7673e98a23
5910d23c-e31b-4476-9d97-ffcb9b2f6e3c	5910d23c-e31b-4476-9d97-ffcb9b2f6e3c
83488b8c-7256-4c68-9a75-0805cbc17e37	83488b8c-7256-4c68-9a75-0805cbc17e37
1ab3f64a-cfab-4bca-8d04-62b2f4980b02	1ab3f64a-cfab-4bca-8d04-62b2f4980b02
6a19fd7f-7280-4a4a-b739-f64bcfbbc8b6	6a19fd7f-7280-4a4a-b739-f64bcfbbc8b6
5b64180b-f8b8-49bf-9ba6-302e89cf8820	5b64180b-f8b8-49bf-9ba6-302e89cf8820
12d7b71a-faca-4947-8885-42fad31856af	12d7b71a-faca-4947-8885-42fad31856af
bf57dbb3-b9db-4317-a88d-074c3bb08a2b	bf57dbb3-b9db-4317-a88d-074c3bb08a2b
ea5a0b0c-e4a1-4b47-98f0-2a1149d8369d	ea5a0b0c-e4a1-4b47-98f0-2a1149d8369d
d886576e-c8ea-44d0-b1e9-f6a57d0abbda	d886576e-c8ea-44d0-b1e9-f6a57d0abbda
4ae8b7e2-e50f-419a-8fdf-1b036db8a5d5	4ae8b7e2-e50f-419a-8fdf-1b036db8a5d5
1fbbd0fc-9e94-4b07-8a7f-6f90d02f28d3	1fbbd0fc-9e94-4b07-8a7f-6f90d02f28d3
78b55200-3917-4398-8000-ac2220345fdb	78b55200-3917-4398-8000-ac2220345fdb
666a5ce2-18c1-4acf-889e-89c8b7ee4de2	666a5ce2-18c1-4acf-889e-89c8b7ee4de2
"""
        return {r.split('\t')[0]: r.split('\t')[1] for r in ref_text.split('\n') if r}

    def get_all_relate_functions(self):
        sql = f'select  * from `dap_bi_function` where application_id like "{OldErpUpgrade1To2.id_templ}%"'
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql) or []

    def exec(self):

        with get_tenant_db(self.tenant_code) as db:
            try:
                db.begin_transaction()

                ref_maps = self.ref_maps()
                # 1. 先获取所有相关的菜单
                relate_functions = self.get_all_relate_functions()
                logger.error(f'开始处理，总共{len(relate_functions)}个')
                for function in relate_functions:
                    report_type = function.get('report_type')
                    report_id_1_0 = function.get('url')  # 1.0 的报表id
                    function_id = function.get('id')
                    function_name = function.get('name') or ''

                    if report_type == 5 and report_id_1_0 in ref_maps:
                        # 2. 找到说明要替换
                        report_id_2_0 = ref_maps[report_id_1_0]  # 2.0 的报表id
                        if function_id:
                            self.update_data(
                                'dap_bi_function', condition={'id': function_id},
                                data={'url': report_id_2_0, 'report_type': 4}
                            )
                            logger.info(
                                f'完成1.0菜单升级2.0： id: {function_id}, name: {function_name}, '
                                f'1.0: {report_id_1_0}, 2.0： {report_id_2_0}'
                            )
                db.conn and db.commit()
            except Exception as e:
                logger.error(f'1.0菜单升级2.0失败，详细原因: {traceback.format_exc()}')
                db.conn and db.rollback()
                raise RuntimeError(f"1.0菜单升级2.0失败，错误原因: {e}") from e
            finally:
                logger.error('结束1.0菜单升级2.0失败')
                db.conn and db.rollback()

    def update_data(self, table_name, data, condition, commit=False, with_none=False):
        """
        更新数据
        """
        if not data:
            return 0
        with get_tenant_db(self.tenant_code) as db:
            return db.update(table_name, data, condition, commit=commit, with_none=with_none)
