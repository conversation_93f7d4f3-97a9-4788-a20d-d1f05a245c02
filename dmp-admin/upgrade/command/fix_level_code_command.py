#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    dashboard_list = []
    id_children_map = {}
    id_level_code_map = {}
    wrong_id_list = []

    wrong_id_children_map = {
        "id": []
    }
    for dash in dashboard_list:
        if not dash.get("parent_id"):
            continue
        if not wrong_id_children_map[dash.get("parent_id"]:
            wrong_id_children_map[dash.get("parent_id") = []
        if not dash.get("level_code").startswith(id_level_code_map.get(dash.get("parent_id"))):
            wrong_id_children_map[dash.get("parent_id").append(dash.get("id"))


    for parent_id, children in wrong_id_children_map:
        parent_level_code = get_level_code(parent_id)
        update_level_code(children)


"""


"""
图表备注数据升级
注：可重复执行

eg.
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=tooltips_config_upgrade_command&type=include&tenants=test
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db


logger = logging.getLogger(__name__)


class FixLevelCodeCommand(UpgradeCommand):

    def _get_dashboard_list(self):
        sql = f"SELECT id, parent_id, level_code FROM dap_bi_dashboard"
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql)

    def _get_level_code(self, dashboard_id):
        sql = f"SELECT level_code FROM dap_bi_dashboard WHERE id=%(dashboard_id)s"
        parmas = {"dashboard_id": dashboard_id}
        with get_tenant_db(self.tenant_code) as db:
            return db.query_one(sql, parmas).get("level_code") or ""

    @staticmethod
    def _get_id_children_map(dashboard_list):
        id_children_map = {dash.get("id"): [] for dash in dashboard_list}
        for dash in dashboard_list:
            if dash.get("parent_id") and isinstance(id_children_map.get(dash.get("parent_id")), list):
                id_children_map[dash.get("parent_id")] = dash.get("id")

    def _update_level_code(self, dashboard_id, new_prefix_level_code):
        sql = (
            'UPDATE dap_bi_dashboard set `level_code`=replace(`level_code`,left(level_code, %(length)s),%(new_level_code)s)'
            'WHERE `parent_id` = %(dashboard_id)s'
        )
        params = {"length": len(new_prefix_level_code), "new_level_code": new_prefix_level_code, "dashboard_id": dashboard_id}
        with get_tenant_db(self.tenant_code) as db:
            return db.exec_sql(sql, params)

    def exec(self):  # NOSONAR
        if not self.tenant_code:
            return True
        has_wrong_level = True
        while has_wrong_level:
            dashboard_list = self._get_dashboard_list()
            id_level_code_map = {dash["id"]: dash.get("level_code") for dash in dashboard_list}
            # id_children_map = self._get_id_children_map(dashboard_list)

            wrong_id_children_map = {}
            for dash in dashboard_list:
                if not dash.get("parent_id"):
                    continue
                if dash.get("parent_id") not in id_level_code_map:
                    continue
                if not wrong_id_children_map.get(dash.get("parent_id")):
                    wrong_id_children_map[dash.get("parent_id")] = []
                if not dash.get("level_code").startswith(id_level_code_map.get(dash.get("parent_id"))):
                    wrong_id_children_map[dash.get("parent_id")].append(dash.get("id"))

            if not self._has_wrong(wrong_id_children_map):
                break
            for parent_id, children in wrong_id_children_map.items():
                if children:
                    parent_level_code = self._get_level_code(parent_id)
                    self._update_level_code(parent_id, parent_level_code)

    @staticmethod
    def _has_wrong(wrong_id_children_map):
        for k, v in wrong_id_children_map.items():
            if v:
                return True
        return False





