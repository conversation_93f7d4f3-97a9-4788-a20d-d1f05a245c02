#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from base.db_wrap import DbWrap

logger = logging.getLogger(__name__)


class IndicatorFieldGroupUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_field_group()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_field_group(self):
        db = DbWrap(self.tenant_code)
        dataset_ids = db.get_column('dap_bi_dataset', {'type': 'INDICATOR'}, ['id'])
        if dataset_ids:
            db.delete_data('dap_bi_dataset_field_group', {'dataset_id': dataset_ids})
            db.delete_data('dap_bi_dataset_field_group_relation', {'dataset_id': dataset_ids})


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)

    command = IndicatorFieldGroupUpgradeCommand(tenant_code="test")
    command._update_field_group()
