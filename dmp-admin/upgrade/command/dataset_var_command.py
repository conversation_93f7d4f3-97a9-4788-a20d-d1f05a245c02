#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/9/27 14:31
# <AUTHOR> caoxl
# @File     : dataset_var_command.py
import logging
import re
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db


logger = logging.getLogger(__name__)


class DatasetVarCommand(UpgradeCommand):
    """
    数据集变量占位符升级 由{:dataset_var_id}升级为${dataset_var_id}
    """

    def exec(self) -> bool:
        logger.exception('''租户 {tenant_code} 开始更新占位符...'''.format(tenant_code=self.tenant_code))
        include_var_fields = self._get_include_var_fields()
        if not include_var_fields:
            logger.exception('''租户 {tenant_code} 没有高级字段引用变量， 退出!'''.format(tenant_code=self.tenant_code))
            return False
        field_ids = set()
        for include_var_field in include_var_fields:
            field_ids.add(include_var_field.get('field_id'))
        fields = self._get_dataset_fields(list(field_ids))
        if not fields:
            logger.exception('''租户 {tenant_code} 无法找到相关字段， 退出!'''.format(tenant_code=self.tenant_code))
            return False
        count = 0
        for field in fields:
            field_id = field.get('id')
            expression_advance = field.get('expression_advance')
            rexp = re.compile(r'\{\:(?P<var_id>[a-z|A-Z|0-9|-]+)\}')
            new_expression_advance = rexp.sub(lambda item: '${' + item.group('var_id') + '}', expression_advance)
            self._update_expression_advance(field_id, new_expression_advance)
            logger.exception(
                '''租户 {tenant_code} 高级字段 {field_id} 表达式由 {expression_advance} 更新为 {new_expression_advance}!'''.format(
                    tenant_code=self.tenant_code,
                    field_id=field_id,
                    expression_advance=expression_advance,
                    new_expression_advance=new_expression_advance,
                )
            )
            count += 1
        logger.exception(
            '''租户 {tenant_code} 变量占位符更新成功，共更新 {count} 个高级字段!'''.format(tenant_code=self.tenant_code, count=count)
        )
        return True

    def _get_include_var_fields(self) -> list:
        sql = """SELECT id,dataset_id,field_id,var_id FROM dap_bi_dataset_field_include_vars"""
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql)

    def _get_dataset_fields(self, field_ids: list) -> list:
        field_placeholder = []
        params = {}
        count = 0
        for field_id in field_ids:
            holder = "field_" + str(count)
            field_placeholder.append("%(" + holder + ")s")
            params[holder] = field_id
            count += 1
        sql = """SELECT id,expression_advance FROM dap_bi_dataset_field WHERE `id` IN ({field_placeholder})""".format(
            field_placeholder=",".join(field_placeholder)
        )
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql, params)

    def _update_expression_advance(self, field_id: str, new_expression_advance: str) -> bool:
        with get_tenant_db(self.tenant_code) as db:
            return db.update('dap_bi_dataset_field', {'expression_advance': new_expression_advance}, {'id': field_id})
