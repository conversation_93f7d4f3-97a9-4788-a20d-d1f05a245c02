#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/4 11:33
# <AUTHOR> caoxl
# @File     : column_display_config_upgrade_command.py
import json
import logging
from collections import defaultdict

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class ColumnDisplayConfigUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        logger.exception("开始更新预览数据")
        preview_update_charts = self._get_update_data(self._get_preview_dashboard_charts())
        self._update_charts(preview_update_charts, "dap_bi_dashboard_chart")
        logger.exception("更新预览数据 {count} 条".format(count=len(preview_update_charts)))
        logger.exception("开始更新发布数据")
        release_update_charts = self._get_update_data(self._get_release_dashboard_charts())
        self._update_charts(release_update_charts, "dap_bi_dashboard_released_snapshot_chart")
        logger.exception("更新发布数据 {count} 条".format(count=len(release_update_charts)))
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _sort_dims_or_nums(self, items: list) -> list:
        if not items:
            return items
        items = sorted(items, key=lambda x: x["rank"])
        return items

    def _get_release_dashboard_charts(self) -> list:
        sql = """SELECT `id`,`nums`,`dims`,`source`,`config` FROM `dap_bi_dashboard_released_snapshot_chart` 
                where `data_logic_type_code`='column'"""
        with get_tenant_db(self.tenant_code) as db:
            charts = db.query(sql)
            for chart in charts:
                try:
                    chart["nums"] = self._sort_dims_or_nums(json.loads(chart.get("nums")))
                    chart["dims"] = self._sort_dims_or_nums(json.loads(chart.get("dims")))
                except Exception as e:
                    logging.exception(
                        "发布态单图 {chart_id} dim或num无法解析，错误信息 {msg}".format(chart_id=chart["id"], msg=str(e))
                    )
            return charts

    def _get_preview_dashboard_charts(self) -> list:
        sql = """SELECT `id`,`source`,`config` FROM `dap_bi_dashboard_chart` 
                        where `data_logic_type_code`='column'"""
        with get_tenant_db(self.tenant_code) as db:
            charts = db.query(sql)
        if not charts:
            return []
        chart_ids = [chart["id"] for chart in charts]
        dims_map = self._get_chart_dim_or_num_map(self._get_preview_charts_dims(chart_ids))
        nums_map = self._get_chart_dim_or_num_map(self._get_preview_charts_nums(chart_ids))
        for chart in charts:
            chart_id = chart["id"]
            chart["dims"] = self._sort_dims_or_nums(dims_map.get(chart_id, []))
            chart["nums"] = self._sort_dims_or_nums(nums_map.get(chart_id, []))
        return charts

    def _get_chart_dim_or_num_map(self, data: list) -> dict:
        dim_or_num_map = defaultdict(list)
        for item in data:
            dim_or_num_map[item["dashboard_chart_id"]].append(item)
        return dict(dim_or_num_map)

    def _get_preview_charts_dims(self, chart_ids: list):
        sql = """SELECT * FROM `dap_bi_dashboard_chart_dim` WHERE `dashboard_chart_id` IN %(chart_id_list)s"""
        params = {"chart_id_list": chart_ids}
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql, params=params)

    def _get_preview_charts_nums(self, chart_ids: list):
        sql = """SELECT * FROM `dap_bi_dashboard_chart_num` WHERE `dashboard_chart_id` IN %(chart_id_list)s"""
        params = {"chart_id_list": chart_ids}
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql, params=params)

    def _get_update_data(self, dashboard_charts: list):
        update_charts = []
        for dashboard_chart in dashboard_charts:
            if not dashboard_chart.get("config"):
                continue
            try:
                config = json.loads(dashboard_chart["config"])
            except Exception as e:
                logger.exception(
                    "解析单图 {chart_id} 配置失败， 错误信息 {msg}".format(chart_id=dashboard_chart["chart_id"], msg=str(e))
                )
                continue
            # 查找并更新配置
            new_config, need_update = self._find_and_update_config(config, dashboard_chart)
            if need_update:
                dashboard_chart["config"] = json.dumps(new_config)
                update_charts.append(dashboard_chart)
        return update_charts

    def _find_and_update_config(self, config: list, dashboard_chart: dict) -> [list, bool]:
        need_update = False
        for cfg_item in config:
            if isinstance(cfg_item, dict) and cfg_item.get("field") == "column":
                if not isinstance(cfg_item.get("items"), list):
                    continue
                for inner_cfg_item in cfg_item["items"]:
                    if inner_cfg_item.get("field") == "colsConfig" and isinstance(inner_cfg_item.get("data"), dict):
                        logging.exception(
                            "单图 {chart_id} 有老配置项，配置为 {old_conf} ".format(
                                chart_id=dashboard_chart["id"], old_conf=json.dumps(inner_cfg_item["data"])
                            )
                        )
                        need_update = True
                        inner_cfg_item["data"] = self._build_column_display_config(
                            dashboard_chart, inner_cfg_item["data"]
                        )
                        logging.exception(
                            "单图 {chart_id} 更新后配置为 {new_conf} ".format(
                                chart_id=dashboard_chart["id"], new_conf=json.dumps(inner_cfg_item["data"])
                            )
                        )
        return config, need_update

    def _build_column_display_config(self, chart: dict, original_display_conf: dict) -> list:
        new_conf = []
        if len(original_display_conf) == 0:
            return new_conf
        count = 0
        new_conf, count = self._get_item_new_conf(count, chart, original_display_conf, new_conf, "dim")
        new_conf, _ = self._get_item_new_conf(count, chart, original_display_conf, new_conf, "num")
        return new_conf

    def _get_item_new_conf(self, count: int, chart: dict, original_display_conf: dict, new_conf: list, type: str):
        items = chart["dims"] if type == "dim" else chart["nums"]
        field_ids = [item.get(type) for item in items]
        if not field_ids:
            return new_conf, count
        dataset_fields_map = self._get_dataset_fields_map(field_ids)
        for item in items:
            new_conf.append(
                {
                    "dataset_id": chart["source"],
                    "dataset_field_id": item.get(type),
                    "col_type": type,
                    "col_name": dataset_fields_map.get(item.get(type), {}).get("col_name"),
                    "alias_name": item.get("alias"),
                    "order": count,
                    "rank": count,
                    "group": None,
                    "is_show": int(original_display_conf.get(item.get(type) + "_" + type, True)),
                }
            )
            count += 1
        return new_conf, count

    def _get_dataset_fields_map(self, field_ids: list) -> dict:
        sql = """SELECT `dataset_field`.id, `dataset_field`.dataset_id, `dataset_field`.alias_name,
                    `dataset_field`.col_name,`dataset_field`.origin_col_name, `dataset_field`.origin_field_type, 
                    `dataset_field`.data_type,`dataset_field`.type, `dataset`.`type` as dataset_type,
                     `dataset`.`connect_type` as dataset_connect_type
                     FROM `dap_bi_dataset_field` as `dataset_field` LEFT JOIN `dap_bi_dataset` as `dataset` ON `dataset`.`id` = `dataset_field`.`dataset_id`
                     where dataset_field.`id` in %(ids)s"""
        with get_tenant_db(self.tenant_code) as db:
            fields = db.query(sql, {'ids': field_ids})
            fields_map = {}
            for field in fields:
                if (field.get('dataset_type') == "API" or field.get('dataset_connect_type') == "直连") and field.get(
                    "origin_col_name"
                ):
                    field["col_name"] = field.get("origin_col_name")
                fields_map[field["id"]] = field
            return fields_map

    def _update_charts(self, charts: list, table_name: str):
        with get_tenant_db(self.tenant_code) as db:
            for chart in charts:
                try:
                    db.update(table_name, {"config": chart["config"]}, {"id": chart["id"]})
                    logger.error("更新单图 {chart_id} 数据成功".format(chart_id=chart["id"]))
                except Exception as e:
                    logger.error("更新单图 {chart_id} 数据失败，错误信息 {msg} ".format(chart_id=chart["id"], msg=str(e)))
