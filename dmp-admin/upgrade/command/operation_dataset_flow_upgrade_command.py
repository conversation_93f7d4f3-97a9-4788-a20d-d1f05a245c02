#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from components.dmp_api import DMPAPI

logger = logging.getLogger(__name__)


class OperationDatasetFlowUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        dmp = DMPAPI(project_code=self.tenant_code)
        res = dmp.update_operation_dataset_flow(status=1)
        logging.exception("content:{}".format(res))
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")


if __name__ == "__main__":
    dmp = DMPAPI(project_code='local')
    res = dmp.update_operation_dataset_flow(status=1)
