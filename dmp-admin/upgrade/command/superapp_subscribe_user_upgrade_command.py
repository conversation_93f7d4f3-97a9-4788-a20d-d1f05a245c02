#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import logging
import json

from upgrade.command.command import UpgradeCommand
from base.db_wrap import DbWrap

logger = logging.getLogger(__name__)

"""
超级APP，产业建管已存在简讯使用的是数见用户
需要将这些简讯使用的数见用户升级为基础数据平台用户
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=superapp_subscribe_user_upgrade_command&type=all
https://dmp-admin-test.mypaas.com.cn/api/upgrade/logs?command_name=superapp_subscribe_user_upgrade_command
"""


class SuperappSubscribeUserUpgradeCommand(UpgradeCommand):

    def __init__(self, **kwargs):
        super(SuperappSubscribeUserUpgradeCommand, self).__init__(**kwargs)
        self.db = DbWrap(self.tenant_code)

    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self.convert_subscribe_user()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def convert_subscribe_user(self):
        sql = """select * from dap_bi_dashboard_email_subscribe
where config_id in (select id from `dap_bi_third_party_app`
where third_party_id in (select id from dap_bi_third_party where app_code in (3, 4)) and app_service_type = 1);"""
        subscribe_list = self.db.db.query(sql) or []
        for subscribe in subscribe_list:
            self._update_subscribe_user(subscribe)

    def _update_subscribe_user(self, subscribe):
        feed_id = subscribe.get('id')
        logger.error(f"========================== 转换简讯用户 简讯id：{feed_id} ========================== ")

        user_list = self.convert_subscribe_role_user(subscribe)
        logger.error(f"简讯待转换用户列表：{json.dumps(user_list, ensure_ascii=False)}")

        user_id_list = [item.get("id") for item in user_list]
        exist_user_rs = self._get_user_by_id(user_id_list)
        if exist_user_rs:
            account_list = [item.get("account") for item in user_list]
            erp_user = self._get_erp_user_by_account(account_list)
            new_user_list = []
            if erp_user:
                new_user_list = [{
                    "id": item.get("user_id"),
                    "account": item.get("account"),
                    "name": item.get("name")
                } for item in erp_user]
            logger.error(f"简讯转换后的用户列表：{json.dumps(new_user_list, ensure_ascii=False)}")
            if new_user_list:
                self._update_subscribe(subscribe, new_user_list)
                logger.error(f"简讯发送用户更新为erp用户")
        else:
            logger.error(f"简讯使用的非数见用户，不需要转换用户")

    def _update_subscribe(self, subscribe, new_user_list):
        feed_id = subscribe.get('id')
        # 替换后都是个人模式用户，而不是角色模式
        msg_subscribe_config_str = subscribe.get("msg_subscribe_config")
        msg_subscribe_config = json.loads(msg_subscribe_config_str)
        user_from = msg_subscribe_config.get("user_from") \
            if msg_subscribe_config.get("user_from") is not None else 0

        # 如果简讯之前是角色发送用户，则需要删除角色
        if user_from:
            logger.error(f"删除简讯角色信息")
            self.delete_mobile_subscribe_role_ids(feed_id)

        msg_subscribe_config["user_from"] = 0
        data = {"recipients": json.dumps(new_user_list, ensure_ascii=False),
                "msg_subscribe_config": json.dumps(msg_subscribe_config, ensure_ascii=False)}
        logger.error(f"更新简讯信息：{json.dumps(data, ensure_ascii=False)}")
        return self.db.db.update("dap_bi_dashboard_email_subscribe", data, {"id": feed_id})

    def convert_subscribe_role_user(self, subscribe):
        """
        获取简讯关联的角色用户信息
        :return:
        """
        msg_subscribe_config_str = subscribe.get("msg_subscribe_config")
        msg_subscribe_config = json.loads(msg_subscribe_config_str)

        # 默认都是个人模式
        user_from = msg_subscribe_config.get("user_from") \
            if msg_subscribe_config.get("user_from") is not None else 0

        logger.error(f"简讯用户模式：{user_from}")
        recipients_str = subscribe.get("recipients")
        recipients = json.loads(recipients_str)
        if user_from == 0:
            return recipients

        feed_id = subscribe.get("id")
        # 角色中的用户获取
        role_id_list = self.get_mobile_subscribe_role_ids(feed_id)
        role_count = len(role_id_list) if role_id_list else 0
        logger.error(f"简讯关联角色个数：{role_count}")
        user_id_list = self.get_subscribe_role_user_id_list(role_id_list)
        user_count = len(user_id_list) if user_id_list else 0
        logger.error(f"简讯关联角色中的用户数：{user_count}")
        if not user_id_list:
            return recipients

        # user_id查找用户
        user_list = self.get_accounts_by_user_ids(user_id_list)
        return user_list

    def get_mobile_subscribe_role_ids(self, feed_id):
        """
        获取简讯的角色id列表
        :param feed_id:
        :return:
        """
        sql = "select role_id from dap_bi_mobile_subscribe_role where email_subscribe_id = %(email_subscribe_id)s"
        return self.db.db.query_columns(sql, {"email_subscribe_id": feed_id})

    def delete_mobile_subscribe_role_ids(self, feed_id):
        """
        获取简讯的角色id列表
        :param feed_id:
        :return:
        """
        sql = "delete from dap_bi_mobile_subscribe_role where email_subscribe_id = %(email_subscribe_id)s"
        return self.db.db.exec_sql(sql, {"email_subscribe_id": feed_id})

    def get_subscribe_role_user_id_list(self, role_id_list):
        """
        获取角色下的所有user_id
        :param str role_id_list:
        :return:
        """
        user_id_list = []
        role_list = self._get_user_role_list_by_ids(role_id_list)
        if not role_list:
            return user_id_list

        exists_role_id_list = [item.get("id") for item in role_list]
        # 获取角色下的用户
        user_id_list = self._get_user_id_by_role_ids(exists_role_id_list)

        # 获取角色下的组织
        group_id_list = self._get_group_id_by_role_ids(exists_role_id_list)
        # 通过组织id获取用户
        if group_id_list:
            group_user_id_list = self._get_user_id_by_group_ids(group_id_list)
            if group_user_id_list:
                user_id_list.extend(group_user_id_list)
        # 去重
        if user_id_list:
            user_id_list = list(set(user_id_list))
        return user_id_list

    def _get_user_role_list_by_ids(self, role_id_list):
        """
        获取角色列表
        :param role_id_list:
        :return:
        """
        if not role_id_list:
            return False
        return self.db.get_list('dap_p_user_role', {'id': role_id_list}, fields=['id', 'name'])

    def _get_user_id_by_role_ids(self, role_id_list):
        """
        按角色id获取所有的user_id（用户去重）
        :param role_id_list: 角色的id
        :return:
        """
        sql = """
        select distinct b.id
        from dap_p_user b left join dap_p_user_user_role a on a.user_id=b.id
        where a.role_id in %(role_ids)s
        """
        params = {'role_ids': role_id_list}
        result = self.db.db.query_columns(sql, params)
        return result

    def _get_group_id_by_role_ids(self, role_id_list):
        """
        按角色id获取所有的组织id（组织去重）
        :param role_id_list: 角色的id
        :return:
        """
        sql = """
        select distinct b.id
        from dap_p_user_group b left join dap_p_user_group_role a on a.group_id=b.id
        where a.role_id in %(role_ids)s
        """
        params = {'role_ids': role_id_list}
        result = self.db.db.query_columns(sql, params)
        return result

    def _get_user_id_by_group_ids(self, group_id_list):
        """
        按组织id获取所有的user_id（用户去重）
        :param group_id_list: 组织的id
        :return:
        """
        sql = """
        select distinct user_id from dap_p_user_group_user as user_group_user  
        inner join dap_p_user as `user` on user_group_user.user_id = `user`.id 
        where user_group_user.group_id in %(group_ids)s
        """
        params = {'group_ids': group_id_list}
        result = self.db.db.query_columns(sql, params)
        return result

    def get_accounts_by_user_ids(self, user_ids):
        """
        根据user_ids获取accounts
        :param user_ids:
        :return:
        """
        sql = """ select id, account, name from `dap_p_user` where `id` in %(user_ids)s""".format(user_ids=user_ids)
        return self.db.db.query(sql, {'user_ids': user_ids})

    def _get_user_by_id(self, user_id_list):
        """
        根据user_id 获取数见账号
        :param user_id_list:
        :return:
        """
        if not user_id_list:
            return []
        sql = "SELECT id FROM `dap_p_user` where id in %(user_id_list)s"
        params = {"user_id_list": user_id_list}
        return self.db.db.query_columns(sql, params)

    def _get_erp_user_by_account(self, account_list):
        """
        根据渠道名称和数据账号查找erp的用户信息
        :param account_list:
        :return:
        """
        if not account_list:
            return []
        sql = "SELECT u.user_id,u.name,u.account FROM `dap_p_user_source_user` u " \
              "left join dap_bi_user_source us on u.user_source_id = us.id " \
              "where us.name = 'ERP渠道用户' and u.account in %(account_list)s"
        params = {"account_list": account_list}
        return self.db.db.query(sql, params)


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    setattr(g, 'account', 'ttt')
    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)

    command = SuperappSubscribeUserUpgradeCommand(tenant_code="uitest")
    command.convert_subscribe_user()
