#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/6/30 16:06
# <AUTHOR> caoxl
# @File     : user_active_time_upgrade_command.py
import logging
from dmplib.redis import RedisCache

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db


logger = logging.getLogger(__name__)


class UserActiveTimeUpgradeCommand(UpgradeCommand):
    """
    升级用户最后活跃时间
    """

    def exec(self):
        conn = RedisCache()
        logger.exception(msg="租户 {project_code} 开始更新所有用户最后活跃记录".format(project_code=self.tenant_code))
        user_dict = conn.hgetall("{project_code}:user_last_active".format(project_code=self.tenant_code))
        if not user_dict:
            logger.exception(msg="租户 {project_code} 无用户最后活跃记录，结束".format(project_code=self.tenant_code))
            return
        with get_tenant_db(self.tenant_code) as db:
            for userid, last_active_time in user_dict.items():
                logger.exception(msg="租户 {project_code} 用户 {userid} 最后活跃时间更新为 {last_active_time}".format(
                    project_code=self.tenant_code,
                    userid=userid,
                    last_active_time=last_active_time
                ))
                db.update("dap_p_user", {"last_active_time": last_active_time}, {"id": userid})
        logger.exception(msg="租户 {project_code} 用户最后活跃记录更新完成".format(project_code=self.tenant_code))

