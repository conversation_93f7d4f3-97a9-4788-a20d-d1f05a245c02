#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from components.dmp_api import DMPAPI

logger = logging.getLogger(__name__)


class ApiDatasetScheduleCleanUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_api_dataset()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_api_dataset(self):
        flow_ids = []
        with get_tenant_db(self.tenant_code) as db:
            # 查询直连api数据集存在的flow记录
            sql = """
            select d.id from dataset as d inner join flow as f on f.id=d.id 
            where d.type='API' and d.connect_type = '直连' group by d.id
            """
            flows = db.query(sql)
            if flows:
                dmp = DMPAPI(self.tenant_code)
                for flow in flows:
                    # 删除调度任务
                    dmp.flow_delete_schedule(flow.get("id"))
                    flow_ids.append(flow.get("id"))
                flow_ids = ["'{}'".format(i) for i in flow_ids]
                db.exec_sql("delete from dap_bi_flow where id in ({})".format(",".join(flow_ids)))
                db.exec_sql("delete from dap_bi_instance where flow_id in ({})".format(",".join(flow_ids)))


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)
