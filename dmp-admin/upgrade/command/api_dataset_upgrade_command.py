#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class ApiDatasetUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_api_dataset()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_api_dataset(self):
        with get_tenant_db(self.tenant_code) as db:
            res = db.exec_sql(
                'update dataset set connect_type="直连" where type="API" and (connect_type is null or connect_type = "")')
            logger.info("update result: %r", res)
