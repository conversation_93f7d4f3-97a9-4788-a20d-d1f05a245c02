import logging
from base import repository
from upgrade.command.command import UpgradeCommand
# from dmplib.saas.project import get_db as get_tenant_db
from upgrade.services.dashboard_redirect_params_upgrade_service import DashboardDirectParamsUpgrade
from project.services.project_service import clear_project_cache

logger = logging.getLogger(__name__)


class DashboardRedirectParamsUpgradeReset(UpgradeCommand):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def exec(self):
        upgrader = DashboardDirectParamsUpgrade(
            tenant_code=self.tenant_code, from_dashboard_id='', upgrade_mode=''
        )
        upgrader.rolling_back_data()

        self.reset_redirect_upgrade_config()

    def reset_redirect_upgrade_config(self):
        # 将租户跳转配置改为新跳转
        repository.update_data('dap_bi_tenant_setting', {'is_new_jump': 0}, {'code': self.tenant_code})
        clear_project_cache(self.tenant_code)
