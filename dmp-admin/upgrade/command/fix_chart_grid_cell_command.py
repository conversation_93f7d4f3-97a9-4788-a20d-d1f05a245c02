#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/7/13 11:08
# <AUTHOR> wangf10
# @File     : fix_chart_grid_cell_command.py
import json
import logging
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class FixChartGridCellCommand(UpgradeCommand):
    """
    修复position
    """

    def exec(self):
        logging.exception(msg="--------------------开始更新预览数据--------------------")
        self._upgrade_preview_data()
        logging.exception(msg="--------------------结束更新预览数据--------------------")
        logging.exception(msg="--------------------开始更新发布数据--------------------")
        self._upgrade_release_data()
        logging.exception(msg="--------------------结束更新发布数据--------------------")

    def _upgrade_preview_data(self):
        sql = """select b.id,b.position from dap_bi_dashboard a,dap_bi_dashboard_chart b where a.type = 'FILE' 
        and a.platform = 'mobile' and a.id = b.dashboard_id and ifnull(b.is_highdata,'') != 1;"""
        with get_tenant_db(self.tenant_code) as db:
            chart_list = db.query(sql)
            for chart in chart_list:
                chart_id = chart.get('id')
                position = chart.get('position', None)
                if position:
                    try:
                        position = self._update_position(position)
                        sql = """UPDATE dap_bi_dashboard_chart SET position=%(position)s WHERE id = %(id)s"""
                        db.exec_sql(sql, {"position": position, "id": chart_id})
                    except Exception as e:
                        logging.exception(
                            msg="设计时单图 {chart_id} json数据解析错误，错误信息 [{msg}]".format(
                                chart_id=chart["id"], msg=str(e)
                            )
                        )
                        continue

    def _upgrade_release_data(self):
        sql = """select b.position,b.id from dap_bi_dashboard_released_snapshot_dashboard a,dap_bi_dashboard_released_snapshot_chart b 
        where a.type = 'FILE' and a.platform = 'mobile' and a.id = b.dashboard_id and ifnull(b.is_highdata,'') != 1"""
        with get_tenant_db(self.tenant_code) as db:
            chart_list = db.query(sql)
            for chart in chart_list:
                chart_id = chart.get('id')
                position = chart.get('position', None)
                if position:
                    try:
                        position = self._update_position(position)
                        sql = """UPDATE dap_bi_dashboard_released_snapshot_chart SET position=%(position)s WHERE id = %(id)s"""
                        db.exec_sql(sql, {"position": position, "id": chart_id})
                    except Exception as e:
                        logging.exception(
                            msg="运行时单图 {chart_id} json数据解析错误，错误信息 [{msg}]".format(
                                chart_id=chart["id"], msg=str(e)
                            )
                        )
                        continue

    @staticmethod
    def _update_position(position: str):  # NOSONAR
        search_key = ['col', 'row', 'size_x', 'size_y', 'header_position']
        head_key = ['x', 'y', 'w', 'h']
        position = json.loads(position)
        for s_key in search_key:
            if s_key in position.keys():
                if s_key != 'header_position':
                    position[s_key] = position[s_key] * 8
                else:
                    for h_key in head_key:
                        if h_key in position[s_key].keys():
                            position[s_key][h_key] = position[s_key][h_key] * 8
        return json.dumps(position)


# if __name__ == '__main__':
#     from dmplib.hug.context import DBContext
#     from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
#     g = _AppCtxGlobals()
#     _app_ctx_stack.push(g)
#     # inject db
#     db_ctx = DBContext()
#     db_ctx.inject(g)
#     a = FixChartGridCellCommand()
#     a.tenant_code = 'uitest'
#     a.exec()
