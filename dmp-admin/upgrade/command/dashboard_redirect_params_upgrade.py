import json
import logging
import traceback

from base import repository
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from upgrade.services.dashboard_redirect_params_upgrade_service import DashboardDirectParamsUpgrade
from project.services.project_service import clear_project_cache
from dmplib.utils.errors import UserError
from dmplib.hug import g
from dmplib.redis import conn as conn_redis

logger = logging.getLogger(__name__)


class DashboardRedirectParamsUpgrade(UpgradeCommand):
    def __init__(self, **kwargs):
        super(DashboardRedirectParamsUpgrade, self).__init__(**kwargs)

    def exec(self):
        g.upgrade_model = self.upgrade_model
        flag, extract_ids = self.extract_ids()
        # 要么指定id，要么升级全部
        if flag:
            from_dashboard_ids = extract_ids
        else:
            from_dashboard_ids = self.get_to_dashboard_ids()

        # from_dashboard_ids = {'3a011790-568c-c3e0-b686-e1f90aeebbde'}  # 参数接受全局筛选
        # from_dashboard_ids = {'39fffa99-9f4a-7f97-e951-27b932bfdb14'}  # 参数接受全局筛选
        # from_dashboard_ids = {'3a0481e7-3347-c5a6-062a-f75577505e38'}  # 参数接受全局筛选
        # from_dashboard_ids = {'3a04eedf-6633-59d7-56e6-6a623e2aa163'}  # 参数接受全局筛选
        # from_dashboard_ids = {'3a059e84-ce0a-6cf1-62ee-0d8eed31c77e'}  # 参数接受全局筛选
        # from_dashboard_ids = {'3a054bda-e8dc-ca0a-935a-e1adb5a07d51'}  # 参数接受全局筛选

        if not from_dashboard_ids:
            logger.error(f'>>>>[整体进度]没有报表，跳过该租户（{self.tenant_code}）的升级')
            return

        total = len(from_dashboard_ids)
        errors = []
        times = 0
        for idx, from_dashboard_id in enumerate(from_dashboard_ids):
            try:
                self.exec_one(from_dashboard_id)
                logger.error(f'>>>>[整体进度][{from_dashboard_id}]升级成功')
            except:
                logger.error(f'>>>>[整体进度][{from_dashboard_id}]升级失败！！原因： {traceback.format_exc()}')
                errors.append(f'>>>>[整体进度][{from_dashboard_id}]升级失败！！原因： {traceback.format_exc()}')
            logger.error(f'>>>>[整体进度][{from_dashboard_id}]升级完成进度: {idx + 1}/{total}')
            times = idx + 1
            self.record_process(f'升级完成进度: {idx + 1}/{total}')

        # 无论升级成功与否都改为新租户
        self.after_redirect_upgrade()

        if errors:
            err_str = '\n\n'.join(errors)
            err_str = f'升级完成进度: {times}/{total}, {err_str}'
            raise UserError(message=err_str)

    def record_process(self, txt):
        # 记录升级进度
        repository.update_data('dap_bi_upgrade_log', data={'execute_msg': txt}, condition={'id': self.upgrade_model.id})

    def extract_ids(self):
        # 提取传过来指定的id
        extra = {}
        if self.extra:
            try:
                extra = json.loads(self.extra)
            except:
                raise UserError(message=f'extract参数错误：{self.extra}')
        dashboard_ids = extra.get('dashboard_ids', '')
        dashboard_ids = dashboard_ids.split(';')
        return bool('dashboard_ids' in str(self.extra)), dashboard_ids

    def exec_one(self, from_dashboard_id):
        # 对于子报告的处理
        # 1. 目前该调用入口有两个，
        # 一是来来自导入，导入的的时候会有所有的子报告的id，不影响
        # 而是整个租户升级，整个租户升级，报告来自SQL查询出来的（查询出所有id），也不会有影响

        upgrade_mode = 'all'

        def upgrade_unreleased():
            self.dashboard_redirect_upgrade(from_dashboard_id, 'unreleased')

        def upgrade_released():
            self.dashboard_redirect_upgrade(from_dashboard_id, 'released')

        def upgrade_all():
            upgrade_unreleased()
            upgrade_released()

        upgrade_mapping = {
            'all': upgrade_all,
            'unreleased': upgrade_unreleased,
            'released': upgrade_released,
        }
        if upgrade_mode not in upgrade_mapping:
            raise RuntimeError(f'upgrade_mode: {upgrade_mode} error')

        upgrade_mapping[upgrade_mode]()

    def after_redirect_upgrade(self):
        # 将租户跳转配置改为新跳转
        repository.update_data('dap_bi_tenant_setting', {'is_new_jump': 1}, {'code': self.tenant_code})
        clear_project_cache(self.tenant_code)  # 清除租户缓存
        self.clear_dashboard_metadata_cache(self.tenant_code)  # 清除元数据缓存

    def clear_dashboard_metadata_cache(self, code):
        logger.error('开始清除元数据缓存')
        conn = conn_redis()._connection
        search_keys = [
            f'{code}*:dashboard:*',
            f'{code}*:dashboard_release_info:*',
            f'{code}*:dashboard_release_data:*',
        ]
        for search_key in search_keys:
            dashboard_keys = conn.keys(search_key) or []
            dashboard_keys = [d if not isinstance(d, bytes) else d.decode() for d in dashboard_keys if d]
            logger.error(f'{code}找到 <{search_key}> keys: {dashboard_keys}')
            if dashboard_keys:
                for key in dashboard_keys:
                    conn.delete(key)

    def dashboard_redirect_upgrade(self, dashboard_id, upgrade_mode):
        upgrader = DashboardDirectParamsUpgrade(
            tenant_code=self.tenant_code, from_dashboard_id=dashboard_id, upgrade_mode=upgrade_mode
        )
        upgrader.do_upgrade()

    def get_to_dashboard_ids(self) -> set:
        """
        获取所有配置了跳转的起跳报告id
        return {'id2','id3'}
        """
        sql = """select distinct dashboard_id  from dap_bi_dashboard_jump_config  where target_type = 'dashboard'"""
        with get_tenant_db(self.tenant_code) as db:
            data = db.query(sql, params={}) or []
            result = {i['dashboard_id'] for i in data}
            return result
