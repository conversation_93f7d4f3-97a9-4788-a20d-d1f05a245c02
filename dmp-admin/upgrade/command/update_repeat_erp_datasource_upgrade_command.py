#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.redis import RedisCache
from dmplib.hug import g
from dmplib import config

logger = logging.getLogger(__name__)

ERP_SOURCE_ID = "c5eea4aa-25b2-4ba1-b16c-4bbaedc5ff9a"
DATA_SOURCE_META = "data_source:"


class UpdateRepeatErpDatasourceUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_erp_datasource()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def flush_redis(self, data_source_ids):
        cache = RedisCache()

        def _del_key(math_key):
            _, match_keys = cache.scan(match=math_key)
            for key in match_keys:
                cache.delete(key)

        dataset_prefix = '*{project_code}:{cache_key}:{object_name}*'.format(
            project_code=self.tenant_code,
            cache_key=config.get('Cache.released_dashboard_metadata_cache_key', 'dmp'),
            object_name="dataset",
        )

        # 清空数据集缓存
        _del_key(dataset_prefix)

        # 清空数据源缓存
        for data_source_id in data_source_ids:
            _key = '%s:%s' % (DATA_SOURCE_META, data_source_id)
            cache.del_data(_key)

    def _update_erp_datasource(self):
        with get_tenant_db(self.tenant_code) as db:
            ids = db.query_columns("select `id` from dap_m_data_source where app_level_code='1000.1401'") or []
            # if ERP_SOURCE_ID in ids and len(ids) > 1:
            if ids:
                try:
                    ids.remove(ERP_SOURCE_ID)
                except:
                    if len(ids) == 1:
                        db.exec_sql(sql=f"update dap_m_data_source set `id`='{ERP_SOURCE_ID}' where `id`='{ids[0]}'")
                    else:
                        return
                for _id in ids:
                    update_sql = f"""
                    update dataset 
                    set 
                        `content` = REPLACE(`content`, '{_id}', '{ERP_SOURCE_ID}')
                    where `content` like "%{_id}%"
                    """
                    db.exec_sql(update_sql)

                    update_kw = f"""
                    update `dap_bi_keyword` 
                    set `datasource_id` = '{ERP_SOURCE_ID}' 
                    where `datasource_id` = '{_id}'
                    """
                    db.exec_sql(update_kw)

                    db.delete("dap_m_data_source", {"id": _id})

                # 清空数据集、数据源缓存
                g.code = self.tenant_code
                self.flush_redis(ids)

