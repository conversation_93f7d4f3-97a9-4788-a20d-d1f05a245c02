# -*- coding: utf-8 -*-
# @Time : 2022/1/10 15:00
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : dataset_table_used_command.py
# @Project : dmp-admin
import logging
import json
from dmplib.utils.strings import seq_id
import traceback

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from components.sqlhelper import get_table_names

logger = logging.getLogger(__name__)


class DatasetTableUsedCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self.update_dataset_used_table()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def update_dataset_used_table(self):
        with get_tenant_db(self.tenant_code) as db:
            dataset_list = db.query("select * from dataset where edit_mode in ('sql', 'relation')")
            if not dataset_list:
                return
            for dataset in dataset_list:
                self.save_used_table(dataset, db)

    @staticmethod
    def save_used_table(dataset, db):  # NOSONAR
        edit_mode = dataset.get("edit_mode")
        table_list = list()
        try:
            content = dataset.get("content")
            if not isinstance(content, dict):
                content = json.loads(content) if content else dict()
            if content:
                if edit_mode == "sql":
                    table_list = get_table_names(content.get("sql")) if content.get("sql") else []
                elif edit_mode == "relation":
                    relation_content = content.get("relation_content", {}).get('nodeDataArray', [])
                    for relation in relation_content:
                        if relation.get('name'):
                            table_list.append(relation.get('name'))
                if table_list:
                    data = list()
                    for table in table_list:
                        if not table:
                            continue
                        row = dict()
                        row['id'] = seq_id()
                        row['dataset_id'] = dataset.get('id', None)
                        row['table_name'] = table.lower().replace('[', '').replace(']', '').replace('`', '').replace('\'',
                                                                                                                     '')
                        data.append(row)
                    # 删除原有数据中使用的表
                    # repository.delete_data('dap_bi_dataset_used_table', {'dataset_id': dataset.get('id', None)})
                    db.delete('dap_bi_dataset_used_table', {'dataset_id': dataset.get('id', None)})
                    # 插入使用的表
                    if data:
                        # repository.add_list_data('dap_bi_dataset_used_table', data, list(data[0].keys()))
                        db.insert_multi_data("dap_bi_dataset_used_table", data, list(data[0].keys()))
        except Exception as e:
            logger.error(str(e))
            logger.exception(traceback.format_exc())
            return False
        return True


if __name__ == '__main__':
    com = DatasetTableUsedCommand()
    com.tenant_code = "uitest"
    com.exec()