#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import copy
import logging

import traceback

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from issue_dashboard.services.level_sequence_service import generate_level_code
from issue_dashboard.models import DashboardLevelSequenceModel
from dmplib.utils.strings import seq_id

logger = logging.getLogger(__name__)


class SplitSelfReportLevelCode(UpgradeCommand):
    cached = dict()
    user_level_cache = dict()
    to_delete_folder_ids = set()
    new_generate_folder_ids = set()
    data_op_tables = 'dap_bi_dashboard'
    data_op_tables_suffix = 'for_self_level_code_backup_230325'

    def exec(self):
        logging.error(f"++++-----------开始处理<{self.tenant_code}>自助报表文件夹level_code-------------++++")

        try:

            db = get_tenant_db(self.tenant_code)
            db.begin_transaction()

            # 0. 备份数据表
            self.backing_up_data()

            # # 1. 标记所有的非叶子节点的目录
            # to_delete_folders = self.mark_folder_to_delete()
            # logger.error(f'to_delete_folders: {[f.get("id") for f in to_delete_folders]}')

            # 2. 获取所有要处理得报告或者文件夹
            dashboards = self.get_dashboard_list()

            # 3. 处理所所有的报表
            for dashboard in dashboards:
                self.deal_one_total_level_code(dashboard)

            # 4. 清理删除旧的层级文件夹
            self.delete_old_folders()

            db.commit()

        except:
            if db:
                db.rollback()
            message = f'{self.tenant_code}主流程异常：{traceback.format_exc()}'
            logger.error(message)
            return False

        logging.error(f"++++-----------结束处理<{self.tenant_code}>自助报表文件夹level_code-------------++++")

    def delete_old_folders(self):
        logger.error(f'旧的文件夹id: {list(self.to_delete_folder_ids)}, 新生成的文件夹id： {self.new_generate_folder_ids}')
        for folder_id in self.to_delete_folder_ids:
            logger.error(f'【删除】 开始删除旧文件夹：{folder_id}')
            self.exec_sql('delete from dap_bi_dashboard where id = %(id)s', params={'id': folder_id}, commit=False)
        logger.error(f'【删除】 刪除旧文件夹完成')

    def is_table_exist(self, table_name):
        try:
            sql = f'select * from {table_name} limit 1'
            self.exec_sql(sql, commit=False)
            return True
        except Exception as e:
            if '1146' in str(e):
                return False
            else:
                logger.error(f'检查表是否存在，意外的情况：{traceback.format_exc()}')
                return False

    def exec_sql(self, sql, params=None, commit=False):
        with get_tenant_db(self.tenant_code) as db:
            affect_row = db._execute(sql, params).rowcount
            if not db.transaction and commit:
                db.commit()
            return affect_row

    def backing_up_data(self):
        # 备份数据
        tables = [self.data_op_tables]
        suffix = self.data_op_tables_suffix
        with get_tenant_db(self.tenant_code) as db:
            db.begin_transaction()
            for table in tables:
                backup_table = f'{table}_{suffix}'
                if not self.is_table_exist(backup_table):
                    # 表不存在，创建备份表
                    sql = f'create table {backup_table} like {table}'
                    self.exec_sql(sql, commit=False)
                    sql = f'insert into {backup_table} select * from {table}'
                    self.exec_sql(sql, commit=False)
            db.commit()

    #     def mark_folder_to_delete(self):
    #         sql = """
    # SELECT * from dashboard as dd WHERE  type = 'FOLDER' and application_type = 1 and (
    # SELECT id from dashboard  as idd WHERE dd.id = idd.parent_id limit 1
    # ) is not null
    #         """
    #         with get_tenant_db(self.tenant_code) as db:
    #             folders = db.query(sql, {}) or []
    #         return folders

    def get_all_parent_level_code(self, level_code):
        # 获取所有的父级level_code
        p_codes = level_code.strip('-').split('-')
        p_level_codes = [p_codes[0:i + 1] for i in range(len(p_codes))][:-1]  # 不算自己
        p_level_codes = ['-'.join(i) + '-' for i in p_level_codes]
        return p_level_codes

    def deal_one_total_level_code(self, dashboard):
        # 1. 收集所有父级level_code信息
        level_code = dashboard.get('level_code')
        current_created_by = dashboard.get('created_by') or ''
        if not level_code or not current_created_by:
            return

        all_parent_level_code = self.get_all_parent_level_code(level_code)
        if not all_parent_level_code:
            # 没有父级目录，处于根目录
            logger.error(f'用户【{current_created_by}】的<{dashboard.get("name")}><{dashboard.get("id")}>当前处于根目录，不处理，跳过')
            return

        level_code_map = {}
        for p_code in all_parent_level_code:
            p_folder_data = self.get_dashboard_by_level_code(p_code)
            if not p_folder_data:
                continue
            level_code_map[p_code] = p_folder_data

        # 2. 重新一份自己创建的生成这个完整的level_code
        new_folder_id, last_level_code = self.create_new_total_level_code(
            current_created_by, all_parent_level_code, level_code_map
        )

        # 3. 将这个文件挂载到新的目录下
        if new_folder_id and last_level_code:
            # 新的报告父级目录创建成功
            with get_tenant_db(self.tenant_code) as db:
                # 3.1 重新生成新目录下的level_code
                operate_model = DashboardLevelSequenceModel(level_id=new_folder_id)
                new_level_code = generate_level_code(operate_model, conn=db)
                # 3.2 将文件移动到新的目录（全是自己的创建的）下面
                data = {'parent_id': new_folder_id, 'level_code': new_level_code}
                dashboard_data = copy.deepcopy(dashboard)
                dashboard_data.update(data)
                db.update('dap_bi_dashboard', condition={'id': dashboard.get('id')}, data=dashboard_data, commit=False)
                if dashboard.get('type') == 'FILE':
                    logger.error(
                        f'【移动文件】 将用户【{current_created_by}】的报告<{dashboard.get("name")}><{dashboard.get("id")}>移动到新父级文件夹<{new_folder_id}>下面')
                else:
                    logger.error(
                        f'【移动文件】 将用户【{current_created_by}】的文件夹<{dashboard.get("name")}><{dashboard.get("id")}>移动到新父级文件夹<{new_folder_id}>下面')

        else:
            logger.error(f'【{current_created_by}】未知的场景: {all_parent_level_code}, level_code_map: {level_code_map}')
        # return

    def create_new_total_level_code(self, current_created_by, all_parent_level_code, level_code_map):
        if not level_code_map:
            return '', ''
        # 从最顶层的开始创建起
        with get_tenant_db(self.tenant_code) as db:
            new_folder_id = ''  # 当前的id
            last_level_code = ''  # 当前的level_code
            last_folder_id = ''  # 父级id
            for idx, p_code in enumerate(all_parent_level_code):
                # 0. 查询当前这个用户这个层级下是否创建过这个层级文件夹
                new_level_code = self.get_user_level_cache_data(current_created_by, p_code)
                if new_level_code:
                    last_level_code = new_level_code
                    new_folder_data = self.get_user_level_cache_data(current_created_by, f'folder-{new_level_code}')
                    new_folder_id = new_folder_data.get('id', '')
                    logger.error(
                        f'【创建文件夹】 【{current_created_by}】使用之前已经创建的文件夹<{new_folder_data.get("name")}><{p_code}:{last_level_code}><{new_folder_id}>')
                else:
                    # 1. 新生成一个文件夹w
                    new_folder_id = seq_id()
                    data = level_code_map.get(p_code)
                    if not data:
                        return '', ''
                    new_folder_data = copy.deepcopy(data)
                    # 把创建过的原来对应的文件夹添加到待删除
                    self.to_delete_folder_ids.add(new_folder_data.get('id', ''))
                    self.new_generate_folder_ids.add(new_folder_id)
                    new_folder_data['id'] = new_folder_id
                    new_folder_data['created_by'] = current_created_by
                    new_folder_data['modified_by'] = current_created_by
                    new_folder_data['level_code'] = last_level_code
                    new_folder_data['application_type'] = 1
                    new_folder_data['parent_id'] = last_folder_id
                    db.insert('dap_bi_dashboard', new_folder_data, commit=False)

                    # 2. 生成level_code
                    operate_model = DashboardLevelSequenceModel(level_id=last_folder_id)
                    last_level_code = generate_level_code(operate_model, conn=db)

                    # 3. 修正层级
                    new_folder_data['level_code'] = last_level_code
                    db.update(
                        'dap_bi_dashboard', condition={'id': new_folder_id}, data={'level_code': last_level_code},
                        commit=False
                    )
                    logger.error(
                        f'【创建文件夹】 为用户【{current_created_by}】创建新的的文件夹<{new_folder_data.get("name")}><{p_code}:{last_level_code}><{new_folder_id}>')

                    # 4. 缓存映射关系
                    self.add_user_level_cache_data(current_created_by, p_code, last_level_code, new_folder_data)

                # 更新父级id
                last_folder_id = new_folder_id
        return new_folder_id, last_level_code

    def get_user_level_cache_data(self, current_created_by, key):
        return self.user_level_cache.get(current_created_by, {}).get(key)

    def add_user_level_cache_data(self, current_created_by, old_level, new_level, new_data):
        if current_created_by not in self.user_level_cache:
            self.user_level_cache[current_created_by] = {}
        # 存储映射关系
        self.user_level_cache[current_created_by][old_level] = new_level
        # 存储新创建的文件夹信息
        self.user_level_cache[current_created_by][f'folder-{new_level}'] = new_data

    def get_dashboard_by_id(self, id):
        file_sql = "select * from dap_bi_dashboard where id = %(id)s and application_type = 1 LIMIT 1"
        with get_tenant_db(self.tenant_code) as db:
            dashboard = db.query_one(file_sql, {'id': id}) or {}
        return dashboard

    def get_dashboard_by_level_code(self, level_code):
        file_sql = "select * from dap_bi_dashboard where level_code = %(level_code)s and application_type = 1 LIMIT 1"
        with get_tenant_db(self.tenant_code) as db:
            dashboard = db.query_one(file_sql, {'level_code': level_code}) or {}
        return dashboard

    def get_dashboard_list(self):
        # a. 叶子节点的文件
        file_sql = "select * from dap_bi_dashboard where type = 'FILE' and application_type = 1"
        with get_tenant_db(self.tenant_code) as db:
            dashboard_files = db.query(file_sql) or []

        # b. 叶子节点的空目录
        folder_sql = """
    SELECT * from dap_bi_dashboard as dd WHERE  type = 'FOLDER' and application_type = 1 and (
    SELECT id from dap_bi_dashboard  as idd WHERE dd.id = idd.parent_id and application_type = 1 limit 1
    ) is null
            """
        with get_tenant_db(self.tenant_code) as db:
            folders = db.query(folder_sql, {}) or []
        return dashboard_files + folders
