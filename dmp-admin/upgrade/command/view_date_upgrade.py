import logging
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class ViewDateUpgrade(UpgradeCommand):

    def exec(self):
        sql = """
        SELECT * FROM dap_bi_dashboard_chart AS C
        JOIN dap_bi_dashboard_filter_chart AS DFC
            ON C.id = DFC.chart_id
        WHERE C.chart_code="date_interval_filter" AND DFC.filter_type=1
        """
        with get_tenant_db(self.tenant_code) as db:
            rows = db.query(sql)
            if rows:
                logger.error("%s has %d ----------------------------------can not upgrade", self.tenant_code, len(rows))
            db.end()
