#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging
import json

from upgrade.command.command import UpgradeCommand

logger = logging.getLogger(__name__)


class PulsarCodeMapUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_project_to_shuxin()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_project_to_shuxin(self):
        try:
            from dmplib.saas.project import get_db as get_saas_db
            from issue_dashboard.services.dataset_service import update_project_to_shuxin

            with get_saas_db(self.tenant_code) as saas_db:
                data_source = saas_db.query_one("select * from dap_m_data_source where type='MysoftShuXin' order by modified_on desc")
                if not data_source:
                    return

                res = update_project_to_shuxin(data_source, self.tenant_code)
                if not res:
                    raise ValueError()

            logger.info("update success")
        except Exception as e:
            logger.error(f"update project_to_shuxin error: {e}")


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)
    g.account = 'celery'

    command = PulsarCodeMapUpgradeCommand(tenant_code="indicator_test")
    command._update_project_to_shuxin()
