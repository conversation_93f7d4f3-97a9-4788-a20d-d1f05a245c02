#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图表备注数据升级
注：可重复执行

eg.
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=tooltips_config_upgrade_command&type=include&tenants=test
"""
import copy
import json
# ---------------- 标准模块 ----------------
import logging
import traceback

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


class InitLargeScreenAuth(UpgradeCommand):

    def exec(self):
        if not self.tenant_code:
            return True

        errors1 = self.deal_top_permissions()
        errors2 = self.deal_folder_all_selected_permissions()

        if not errors1 or not errors2:
            message = {
                '顶部的权限复制错误': errors1, '文件夹全选权限复制错误': errors2
            }
            raise UserError(message=json.dumps(message, ensure_ascii=False))

    def deal_top_permissions(self):
        """
        仪表板顶部权限复制到酷炫大屏
        """
        permissions = self.get_top_permissions()
        errors = []
        for permission in permissions:
            db = get_tenant_db(self.tenant_code)
            try:
                db.begin_transaction()
                # 开始复制权限
                screen_permission = copy.deepcopy(permission)
                screen_permission['func_code'] = 'large_screen'
                screen_permission.pop('created_on', None)
                screen_permission.pop('modified_on', None)
                screen_permission.pop('created_by', None)
                screen_permission.pop('modified_by', None)

                is_existed = self.get_data(
                    'dap_p_user_role_func', conditions=screen_permission.copy(), multi_row=False,
                    db=db, fields=['func_code']
                )
                if not is_existed:
                    db.insert('dap_p_user_role_func', data=screen_permission.copy())
                db.commit()
            except:
                if db:
                    db.rollback()
                message = f'处理权限失败： {permission}，原因：{traceback.format_exc()}'
                errors.append(message)
                logger.error(message)
        return errors

    def deal_folder_all_selected_permissions(self):
        """
        仪表板权限文件夹权限复制到大屏
        """
        permissions = self.get_folder_all_selected_permissions()
        errors = []
        for permission in permissions:
            db = get_tenant_db(self.tenant_code)
            try:
                db.begin_transaction()
                # 开始复制权限
                screen_permission = copy.deepcopy(permission)
                screen_permission['data_type'] = 'large_screen'
                screen_permission.pop('created_on', None)
                screen_permission.pop('modified_on', None)
                screen_permission.pop('created_by', None)
                screen_permission.pop('modified_by', None)

                is_existed = self.get_data(
                    'dap_p_user_role_all_data_permission', conditions=screen_permission.copy(), multi_row=False,
                    db=db, fields=['data_type']
                )
                if not is_existed:
                    db.insert('dap_p_user_role_all_data_permission', data=screen_permission.copy())
                db.commit()
            except:
                if db:
                    db.rollback()
                message = f'处理权限失败： {permission}，原因：{traceback.format_exc()}'
                errors.append(message)
                logger.error(message)
        return errors

    def get_data(self, table_name, conditions, fields, multi_row=None, order_by=None, db=None):
        from base.repository import _judge_operator
        sql = 'SELECT {col} FROM {table_name} ' \
              ''.format(col='`' + '`,`'.join(fields) + '`' if fields else '*',
                        table_name=table_name)
        if conditions and isinstance(conditions, dict):
            sql += 'WHERE ' + ' AND '.join([_judge_operator(key, val) for key, val in conditions.items()])
        if order_by and isinstance(order_by, list):
            sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
        if multi_row:
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)

    def get_top_permissions(self):
        sql = '''select * from dap_p_user_role_func where func_code = %s'''
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql, ('data-report',))

    def get_folder_all_selected_permissions(self):
        sql = '''select * from dap_p_user_role_all_data_permission where data_type = %s'''
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql, ('dashboard',))
