#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
import time

logger = logging.getLogger(__name__)

# 共享表
SHARE_TABLE = "{}_share"

# 暂存表
TMP_TABLE = "{}_tmp"


class FillingAddColumnUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_filling()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_filling(self):
        with get_tenant_db(self.tenant_code) as db:
            # 查询直连api数据集存在的flow记录
            sql = """
            select * from dap_bi_filling_template where status!=0
            """
            filling_template_list = db.query(sql)
            for filling_template in filling_template_list:
                result_table = filling_template.get('table_name')
                share_table = SHARE_TABLE.format(result_table)
                tmp_table = TMP_TABLE.format(result_table)
                try:
                    with get_tenant_db(self.tenant_code, "data") as data_db:
                        try:
                            data_db.exec_sql(
                                f"alter table {tmp_table} change column reviewer reviewer varchar(256) default NULL")
                            data_db.exec_sql(
                                f"alter table {share_table} change column reviewer reviewer varchar(256) default NULL")
                            data_db.exec_sql(
                                f"alter table {result_table} change column reviewer reviewer varchar(256) default NULL")
                        except Exception as e:
                            print(e)
                        try:
                            data_db.exec_sql(
                                f"alter table {tmp_table} add column reviewer varchar(256) default NULL")
                            data_db.exec_sql(
                                f"alter table {share_table} add column reviewer varchar(256) default NULL")
                            data_db.exec_sql(
                                f"alter table {result_table} add column reviewer varchar(256) default NULL")
                            data_db.exec_sql(
                                f"alter table {tmp_table} add column review_time datetime")
                            data_db.exec_sql(
                                f"alter table {share_table} add column review_time datetime")
                            data_db.exec_sql(
                                f"alter table {result_table} add column review_time datetime")
                        except Exception as e:
                            print(e)

                        # data_db.exec_sql(f"alter table {share_table} add column cur_fill_user_name varchar(128) not null default ''")
                        # data_db.exec_sql(f"alter table {result_table} add column cur_fill_user_name varchar(128) not null default ''")
                        # data_db.exec_sql(f"alter table {tmp_table} add column cur_fill_user_name varchar(128) not null default ''")
                        data_db.exec_sql(f"alter table {share_table} change column status status char(36)  default ''")
                        data_db.exec_sql(f"alter table {result_table} change column status status  char(36) default ''")
                        data_db.exec_sql(f"alter table {tmp_table} change column status status  char(36)  default ''")
                        data_db.exec_sql(f"alter table {share_table} change column remark remark varchar(256) default ''")
                        data_db.exec_sql(f"alter table {result_table} change column remark remark varchar(256) default ''")
                        data_db.exec_sql(f"alter table {tmp_table} change column remark remark varchar(256) default ''")
                except Exception as e:
                    print(e)
                try:
                    if filling_template.get("is_needaudit") in [0, "0"]:
                        db.update("dap_bi_filling_inputbatch", {"status": 0}, {"template_id": filling_template.get("id")})
                except Exception as e:
                    print(e)


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)
