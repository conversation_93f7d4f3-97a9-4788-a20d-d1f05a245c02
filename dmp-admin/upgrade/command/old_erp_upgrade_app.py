import copy
import json
import logging
import traceback

from dmplib.utils.model import BaseModel
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from components.dmp_api import DMPAPI
from issue_dashboard.services import level_sequence_service
from dmplib.utils.strings import seq_id
from base import repository

logger = logging.getLogger(__name__)


class LevelSequenceBaseModel(BaseModel):
    def __init__(self, **kwargs):
        self.table_name = ''
        self.table_level_id_field = 'id'
        self.table_level_code_field = 'level_code'
        self.level_id = ''
        self.attach_identify = ''
        self.unit_code_length = 4
        super().__init__(**kwargs)


class FunctionLevelSequenceModel(LevelSequenceBaseModel):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.table_name = 'dap_bi_function'


class OldErpUpgradeApp(UpgradeCommand):
    id_templ = '11111111-0000-0000-0000-000000000000'
    id_map = {}

    def build_record_tree(self, records):
        records = copy.deepcopy(records)
        data_mapping = {r.get('id'): r for r in records}
        result = []
        for r in records:
            parent_id = r.get('parentId')
            if not parent_id or r.get('id') == parent_id:
                result.append(r)
            else:
                parent_r = data_mapping.get(parent_id)
                if not parent_r:
                    continue
                if not parent_r.get('sub'):
                    parent_r['sub'] = []
                parent_r['sub'].append(r)
        return result

    def search_node_file(self, result: list, funcs):
        # 找到每个叶子结点报告
        for func in funcs:
            sub = func.get('sub', [])
            if not sub and func.get('type') == 'FILE':
                # 叶子节点的报告
                result.append(func)
            else:
                # 继续找
                self.search_node_file(result, sub)

    def order_one_application_funcs(self, funcs):
        # 门户下面的菜单排序
        for func in funcs:
            sub = func.get('sub', [])
            if sub:
                sub = sorted(sub, key=lambda x: x.get('name'), reverse=False)
                func['sub'] = sub
                self.order_one_application_funcs(sub)

    # 递归收集所有父级菜单信息
    def _collect_all_parent(self, func: dict, filter_data: list, funcs_map: dict):
        parent_id = func.get('parentId')
        if not parent_id:
            return
        parent_func = funcs_map.get(parent_id)
        if parent_func and parent_func not in filter_data:
            filter_data.append(parent_func)
            self._collect_all_parent(parent_func, filter_data, funcs_map)

    def generate_id_map(self, data):
        # 提前生成所有新老id的map
        id_set = set()
        for d in data:
            id = d.get('id', '')
            parent_id = d.get('parentId', '')
            if id:
                id_set.add(id)
            if parent_id:
                id_set.add(parent_id)
        self.id_map = {old_id: seq_id() for old_id in id_set}

    def exec(self):
        # {
        #   'id': 'c6cb2399-99ad-433a-af5f-75ab1f2fd757',
        #   'parentId': None, 'name': '销售系统',
        #   'type': 'FOLDER', 'isPublish': True, 'levelCode': '0011'
        # }
        data = self.get_old_report_data()
        logger.error(f'开始处理，总共{len(data)}个')
        self.generate_id_map(data)
        report_map = copy.deepcopy({i.get('id'): i for i in data})
        origin_data = copy.deepcopy(data)
        full_tree = self.build_record_tree(records=data)
        # 1. 移除所有子报表
        data = [i for i in data if (report_map.get(i.get('parentId')) or {}).get('type') != 'FILE']

        # 2. 移除所有叶子节点的空目录
        node_files = []
        # 2.1 所有叶子节点的报告
        self.search_node_file(result=node_files, funcs=data)
        # 2.2 根据每个叶子节点报告往上构建目录
        filter_data = []
        for file in node_files:
            # 先找父级信息
            self._collect_all_parent(file, filter_data, report_map)
            # 再添加报告自己
            filter_data.append(file)

        # 将过滤后的数据按照原始的数据顺序重新排序
        new_filter_data = self.sort_as_origin_order(origin_data, filter_data)

        # 3. 构建非空叶子节点目录的树
        no_empty_no_child_tree = self.build_record_tree(records=new_filter_data)

        with get_tenant_db(self.tenant_code) as db:
            try:
                db.begin_transaction()
                # 4. 处理数据
                curr = 0
                skip = 0
                done = 0
                total = len(no_empty_no_child_tree)
                for application in no_empty_no_child_tree:

                    self.order_one_application_funcs(application.get('sub', []))

                    # 4.1. 获取门户信息
                    raw_app_id = application.get('id', '')
                    parent_id = application.get('parentId', '')
                    level_code = application.get('levelCode', '')  # erp业务标记
                    logger.error(f'处理id：{raw_app_id}')
                    curr += 1
                    if not raw_app_id or not level_code or parent_id:
                        logger.error(f'不是根目录的门户数据：{application}')
                        skip += 1
                        continue

                    # if application.get('name') != '销售系统':
                    #     continue
                    self.deal_one_application(application, report_map, db, full_tree)
                    done += 1

                    self.record_process(f'升级完成进度: {curr}/{total}, 成功：{done}, 跳过：{skip}')

                db.conn and db.commit()
            except Exception as e:
                logger.error(f'升级erp历史门户数据，详细原因: {traceback.format_exc()}')
                db.conn and db.rollback()
                raise RuntimeError(f"升级erp历史门户数据，错误原因: {e}") from e
            finally:
                logger.error('结束升级erp历史门户数据')
                db.conn and db.rollback()

    def sort_as_origin_order(self, origin_data, filter_data):
        result = []
        for data in origin_data:
            if data in filter_data:
                result.append(data)
        return result

    def deal_one_application(self, application, report_map, db, full_tree):
        self.delete_existed_data(application)
        # return

        # 4.2 门户下菜单信息
        sud_funcs_data = application.get('sub') or []
        # 4.2 平铺展开门户的所有菜单和报告
        sub_tiling_data = self.tiling_level(sud_funcs_data, report_map)

        # 4.3 插入数据
        # 4.3.1 门户数据
        actual_application = self.add_application(app_data=application)
        actual_app_id = actual_application.get('id', '')
        # 4.3.1 菜单数据
        for func in sub_tiling_data:
            try:
                old_func_id = func.get('id', '')
                # 处理这个叶子节点报表的所有子报表
                # 平铺同一级别
                if func.get('type') == 'FILE':
                    sub_files = self.get_sub_files(full_tree, old_func_id).get('sub') or []
                    # self.order_one_application_funcs(sub_files)
                    if sub_files:
                        # 有子报告, 平铺所有报告以及子报告
                        tiling_dashboards = self.tiling_level(sub_files, report_map)
                        # 先创建一个父级菜单，名字使用父级报告的
                        p_func = copy.deepcopy(func)
                        p_func['type'] = 'FOLDER'
                        p_app = self.add_menu_function(actual_app_id, p_func, db)
                        tiling_dashboards = sorted(tiling_dashboards, key=lambda x: x.get('name'), reverse=False)
                        for dashboard in [func] + tiling_dashboards:  # 主报告+子报告
                            # 父子报表平铺展示
                            c_dashboard = copy.deepcopy(dashboard)
                            c_dashboard['parentId'] = p_app.get('id', '')
                            self.add_menu_function(
                                actual_app_id, c_dashboard, db, mode='add_multi_file'
                            )
                    else:
                        # 没有子报告，创建一个菜单与报告id一样
                        s_func = copy.deepcopy(func)
                        s_func['id'] = old_func_id
                        self.add_menu_function(actual_app_id, func, db, mode='add_one_file')
                elif func.get('type') == 'FOLDER':
                    # 是文件夹, 创建菜单
                    self.add_menu_function(actual_app_id, func, db)
                else:
                    logger.error(f'未知的文件类型不处理： {func}')
            except:
                logger.error(f'未知的升级异常，application：{func}， 错误：{traceback.format_exc()}')

        # raise

    def record_process(self, txt):
        # 记录升级进度
        repository.update_data('dap_bi_upgrade_log', data={'execute_msg': txt}, condition={'id': self.upgrade_model.id})

    def add_menu_function(self, app_id, func, conn, mode='add_func'):
        # 添加报表时候，菜单id就是报告id
        if mode == 'add_func':  # 单菜单
            func_id = func.get('id', '')
            func_id = self.get_new_id(func_id)
            parent_id = func.get('parentId', '')
            parent_id = self.get_new_id(parent_id)
        elif mode == 'add_one_file':  # 只有父报告
            func_id = func.get('id', '')
            parent_id = func.get('parentId', '')
            parent_id = self.get_new_id(parent_id)
        elif mode == 'add_multi_file':  # 父子报告
            func_id = func.get('id', '')
            parent_id = func.get('parentId', '')
        else:
            raise Exception(f'错误的mode：{mode}')

        func_name = func.get('name', '')

        if not func_id:
            logger.error(f'空的菜单id, 跳过')
            return {}

        level_code = self.generate_level_code(parent_id, application_id=app_id, conn=conn)
        parent_id = '' if level_code.count('-') == 1 else parent_id  # 最外层菜单parent_id为''
        func_data = {
            "id": func_id,
            "name": func_name,
            "parent_id": parent_id,
            "level_code": level_code,
            "icon": "dmpicon-column",
            "icon_url": "",
            "url": "",
            "target": "",
            "application_id": app_id,
            "created_by": self.tenant_code,
            "modified_by": self.tenant_code,
            "report_type": 0  # 5: 老erp报表
        }
        if func.get('type') == 'FILE':
            func_data['report_type'] = 5
            func_data['url'] = func_id
        self.add_data('dap_bi_function', func_data)
        return func_data

    def delete_existed_data(self, app_data):
        app_id = self.get_app_id(app_data)
        self.delete('dap_bi_application', data={'id': app_id})
        self.delete('dap_bi_function', data={'application_id': app_id})
        logger.error(f'删除了门户<{app_id}>的相关数据')

    def get_new_id(self, old_id):
        new_id = self.id_map.get(old_id)
        if not new_id:
            raise Exception(f'出现了未知的erp报表id: {old_id}')
        else:
            return new_id

    def get_sub_files(self, full_tree, file_id):
        # 获取父级级子级报告树
        result = []

        def find(data, file_id):
            for func in data:
                if func.get('id') == file_id:
                    result.append(func)
                else:
                    subs = func.get('sub') or []
                    find(subs, file_id)

        find(full_tree, file_id)
        return result[0] if result else {}

    def get_app_id(self, app_data):
        code = app_data.get('levelCode', '')
        app_id = f'{self.id_templ[:-4]}{code}'
        return app_id

    def add_application(self, app_data):
        # 添加门户数据
        app_id = self.get_app_id(app_data=app_data)
        rank = self.get_application_rank()

        data = {
            "id": app_id,
            "name": app_data.get('name') or '',
            "platform": "pc",
            "nav_type": 0,
            "menu_display_type": 1,  # 自动展开
            "description": "",
            "icon": "",
            "url": "",
            "target": "",
            "is_buildin": 0,
            "is_system": 1,  # 是否是系统门户
            "enable": 1,  # 是已发布状态
            "type_access_released": 3,  # 第三方发布状态
            "created_by": self.tenant_code,
            "modified_by": self.tenant_code,
            "use_guide": 0,
            "theme": "浅色",
            "collapse": 1,
            "is_show_banner": 1,
            "is_cache": 1,
            "rank": rank,
            "common_config": "{\"navLayoutType\":1}",
            "user_defined_style": "{\"width_mode\":\"large\"}",
        }
        self.add_data('application', data)
        return data

    def get_application_rank(self):
        with get_tenant_db(self.tenant_code) as db:
            max_rank = db.query_scalar('select max(`rank`) from application') or 0
            return max_rank + 1

    def generate_level_code(self, parent_id, application_id, conn):
        """

        :param app_menu.models.FunctionModel model:
        :return:
        """
        return level_sequence_service.generate_level_code(
            FunctionLevelSequenceModel(level_id=parent_id, attach_identify=application_id), conn=conn
        )

    def add_data(self, table_name, data, commit=False):
        """
        添加数据
        """
        if not data:
            return False
        with get_tenant_db(self.tenant_code) as db:
            return db.insert(table_name, data, commit=commit) == 1

    def delete(self, table_name, data, commit=False):
        """
        添加数据
        """
        if not data:
            return False
        with get_tenant_db(self.tenant_code) as db:
            return db.delete(table_name, data, commit=commit) == 1

    def tiling_level(self, data, report_map):
        if not data:
            return []
        # 平铺层级
        result = []
        data = copy.deepcopy(data)

        def find(result, func_datas):
            for func in func_datas:
                func_data = report_map.get(func.get('id'))
                if func_data and func_data not in result:
                    result.append(func_data)
                subs = func.pop('sub', [])
                find(result, subs)

        find(result, data)
        return result

    def get_old_report_data(self):
        data = DMPAPI(self.tenant_code).get_old_erp_report_list()
#         data = """{
#     "result": true,
#     "msg": "ok",
#     "data": [
#         {
#             "id": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "parentId": null,
#             "name": "销售系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0011"
#         },
#         {
#             "id": "c8b01253-9ac5-46c0-ab8f-eae7af3035a7",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "销售异常",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "交易统计",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "464a5ff8-792b-4fbe-a1f0-4958e8cccaa3",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "回款分析",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e40fd5fe-b634-4e30-a914-28f62d7794c0",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "套打报表",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "762f59ed-03bc-48b3-9c2c-74cc2ef4ebb5",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "财务统计",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "95703ec5-ca45-4ad0-8544-5209b39e95ea",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "存量统计",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e85129ad-3d8f-42e7-857f-cbad9aa4d497",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "售后统计",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "ed8f8b2a-be3c-4295-addf-486d35aa8491",
#             "parentId": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
#             "name": "重庆区域",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "036e94db-e56d-44bf-b0a7-ea875f3b1beb",
#             "parentId": null,
#             "name": "佣金系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0080"
#         },
#         {
#             "id": "87b2861e-ceb1-495f-8d6c-322bfdb3bf05",
#             "parentId": "036e94db-e56d-44bf-b0a7-ea875f3b1beb",
#             "name": "佣金统计",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "68868161-74b6-4ceb-875d-f9fa478ba2d8",
#             "parentId": null,
#             "name": "计划系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0202"
#         },
#         {
#             "id": "3a6ec64a-8972-41be-97cf-94fe30f6c7cd",
#             "parentId": null,
#             "name": "采招系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0220"
#         },
#         {
#             "id": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "parentId": "3a6ec64a-8972-41be-97cf-94fe30f6c7cd",
#             "name": "供应商管理",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "4cb85e84-129c-4d8a-b075-bb3b95a5fc2f",
#             "parentId": "3a6ec64a-8972-41be-97cf-94fe30f6c7cd",
#             "name": "履约评估",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "53ff0ff0-0680-42e8-8b18-4aceec640e2f",
#             "parentId": "3a6ec64a-8972-41be-97cf-94fe30f6c7cd",
#             "name": "采购管理",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "2fc03e5a-e640-4841-a0ea-cb8ff640f86a",
#             "parentId": "3a6ec64a-8972-41be-97cf-94fe30f6c7cd",
#             "name": "战略采购",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "23edfa5b-f544-4257-a111-4af04c4b64db",
#             "parentId": null,
#             "name": "材料供应链",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0221"
#         },
#         {
#             "id": "99ca5949-1ace-42d3-9cff-44f85bd83457",
#             "parentId": null,
#             "name": "主数据系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0301"
#         },
#         {
#             "id": "c077ea72-3e80-4228-937a-32196d7f2f1e",
#             "parentId": "99ca5949-1ace-42d3-9cff-44f85bd83457",
#             "name": "项目域",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "1772df0f-1eda-4397-aaeb-dcd1ec2ce37b",
#             "parentId": null,
#             "name": "投资收益系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "0801"
#         },
#         {
#             "id": "1a6565d6-8ee6-46bc-9fee-d320c3418f5f",
#             "parentId": "1772df0f-1eda-4397-aaeb-dcd1ec2ce37b",
#             "name": "项目编制状态统计报表",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "cdde3694-a35a-4167-ad45-00934d846f33",
#             "parentId": "1772df0f-1eda-4397-aaeb-dcd1ec2ce37b",
#             "name": "Excel套表导出记录查询",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a768a85f-ba7b-4392-8d51-fc5738debc0c",
#             "parentId": null,
#             "name": "工作流",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "1001"
#         },
#         {
#             "id": "fc832b70-744d-42c5-a1be-daf6c05f2f5d",
#             "parentId": null,
#             "name": "费用系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "1206"
#         },
#         {
#             "id": "7b6c821e-a619-4576-aba6-c655e069935f",
#             "parentId": "fc832b70-744d-42c5-a1be-daf6c05f2f5d",
#             "name": "屏幕报表",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "parentId": "fc832b70-744d-42c5-a1be-daf6c05f2f5d",
#             "name": "统计报表",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "parentId": null,
#             "name": "成本系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "3201"
#         },
#         {
#             "id": "2c76005f-1e6c-48d7-b640-f845b504e770",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "屏幕报表",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "2e50c99c-eea0-499a-9fa7-7da6be58ccab",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "成本概览及监控",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "9fc53028-4508-4bee-b707-7bcb1262767f",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "资金计划",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "ac3652bd-1ec9-4c88-9714-126db87516d9",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "合同执行",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "000b825b-e097-4cbe-82ea-5d33136dd4b6",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "合同付款",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "6fb472f7-200a-43b5-abdb-3d5b5f187a93",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "成本执行及分析",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5c27d90a-9e49-4e36-a592-994a793b735f",
#             "parentId": "f9ab318d-cf73-11ec-81d4-0242ac100003",
#             "name": "合同结算",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "caf833f9-cf73-11ec-81d4-0242ac100003",
#             "parentId": null,
#             "name": "计划系统",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "3202"
#         },
#         {
#             "id": "6e2fc186-59c9-4902-bde9-26f5ab9a5b64",
#             "parentId": "caf833f9-cf73-11ec-81d4-0242ac100003",
#             "name": "异常监控",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "654fdd09-f647-4e07-8105-ff6b6e1e0d5e",
#             "parentId": "caf833f9-cf73-11ec-81d4-0242ac100003",
#             "name": "工作进展",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "bc0abc1c-3b90-4aba-8fc9-23e7ab7a0d98",
#             "parentId": "caf833f9-cf73-11ec-81d4-0242ac100003",
#             "name": "工期对比",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5161a368-2b81-4e1c-82d3-b97a2ff53eee",
#             "parentId": "caf833f9-cf73-11ec-81d4-0242ac100003",
#             "name": "达成率分析",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "6772e119-1b21-4e59-96d6-b6f6b7952f75",
#             "parentId": null,
#             "name": "动态货值",
#             "type": "FOLDER",
#             "isPublish": true,
#             "levelCode": "6002"
#         },
#         {
#             "id": "02a0c5c8-979a-47e4-a3d9-1c90a2b50c66",
#             "parentId": "fc832b70-744d-42c5-a1be-daf6c05f2f5d",
#             "name": "支付口径费用明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "0871a05a-d1a6-4cc5-a441-97c74c11035e",
#             "parentId": "53ff0ff0-0680-42e8-8b18-4aceec640e2f",
#             "name": "14 集中采购计划分析报表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "0ef29f8d-8664-4f5b-8a91-5932c1deb8b6",
#             "parentId": "4cb85e84-129c-4d8a-b075-bb3b95a5fc2f",
#             "name": "08 战采协议履约评估执行分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "12d7b71a-faca-4947-8885-42fad31856af",
#             "parentId": "e85129ad-3d8f-42e7-857f-cbad9aa4d497",
#             "name": "12 项目按揭办理统计表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "12f6e4e4-2146-42b9-ae27-b039e5e6eef3",
#             "parentId": "6e2fc186-59c9-4902-bde9-26f5ab9a5b64",
#             "name": "延迟汇报明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "17d74f34-4f41-4fda-87e7-5444946ed594",
#             "parentId": "6e2fc186-59c9-4902-bde9-26f5ab9a5b64",
#             "name": "延迟汇报统计",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "1ab3f64a-cfab-4bca-8d04-62b2f4980b02",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "09 退房明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "1fa84633-cfa0-47ea-8cbf-a193b3efc952",
#             "parentId": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "name": "03 正式供应商储备分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "1fbbd0fc-9e94-4b07-8a7f-6f90d02f28d3",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "17 签约回款完成情况",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "20299669-18b2-4c3e-ae73-dec1242ec3ab",
#             "parentId": "000b825b-e097-4cbe-82ea-5d33136dd4b6",
#             "name": "11 供应商付款汇总表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "20618b53-793e-4f71-904f-31f3aada127a",
#             "parentId": "fc832b70-744d-42c5-a1be-daf6c05f2f5d",
#             "name": "发生口径费用明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "215fce41-6828-4032-9c82-24f8f921eadd",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0301认购换房明细表",
#             "type": "FILE",
#             "isPublish": false,
#             "levelCode": null
#         },
#         {
#             "id": "23ac935c-1926-4ec1-871e-01310b0b6ef8",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0307延期签约明细表",
#             "type": "FILE",
#             "isPublish": false,
#             "levelCode": null
#         },
#         {
#             "id": "286dab13-197c-4352-b86b-9aede3e26327",
#             "parentId": "2e50c99c-eea0-499a-9fa7-7da6be58ccab",
#             "name": "06 合同台账表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "2bd4e7b0-0fad-459b-b05f-e89e7bf118a2",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "01 销售汇总表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "2e92fbe4-6d07-498e-9767-9c36dd24dd82",
#             "parentId": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "name": "01 供应商综合分析报表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "2f718b54-7e54-4a9c-ae03-168f4cef6bee",
#             "parentId": "000b825b-e097-4cbe-82ea-5d33136dd4b6",
#             "name": "09 项目支付情况汇总表（科目维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "34ab26b3-5585-4392-a7ba-4e2836dd7875",
#             "parentId": "bc0abc1c-3b90-4aba-8fc9-23e7ab7a0d98",
#             "name": "项目节点计划实际工期对比",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "35c99fed-e7da-46c0-9251-ed908d3762ec",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "非合同台账",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "3d432d6d-8d4e-4a34-a386-f427909ea398",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "03 签约明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "3e39e649-4252-459a-9d25-b835e07e1587",
#             "parentId": "a57daa09-4a36-4d25-af88-6ac0b634e86c",
#             "name": "01.1 项目动态成本明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "parentId": "ed8f8b2a-be3c-4295-addf-486d35aa8491",
#             "name": "YX-JY03变更汇总表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "451034f5-8feb-4e5e-a4f7-c7070b7856a6",
#             "parentId": "7150b004-181b-45d4-bc85-c99488e181db",
#             "name": "07.1 合同变更台账表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "457b43e1-6249-417d-b93e-b447735f0155",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0306延期付款明细表",
#             "type": "FILE",
#             "isPublish": false,
#             "levelCode": null
#         },
#         {
#             "id": "474c355d-1c86-4dff-b321-e8f19859ab38",
#             "parentId": "2e50c99c-eea0-499a-9fa7-7da6be58ccab",
#             "name": "05 核算对象可售成本－目标与动态对比表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "482b13d2-28f7-4b78-b628-73483371dcbc",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "供应商合作排名",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "48f0fb68-2d3f-4ea7-bc22-659e5d6a8b58",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "项目营销费效分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "4ae8b7e2-e50f-419a-8fdf-1b036db8a5d5",
#             "parentId": "762f59ed-03bc-48b3-9c2c-74cc2ef4ebb5",
#             "name": "16 应收款明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "4eabc0b5-53a3-484e-a402-782246763b21",
#             "parentId": "a33a1d67-0f4c-44ea-9b18-282db1271c4c",
#             "name": "10.1 合同执行与付款明细表（合同维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "51d56d00-ccfc-47a5-ae4f-600e99e28ba9",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0305价格变更明细表",
#             "type": "FILE",
#             "isPublish": false,
#             "levelCode": null
#         },
#         {
#             "id": "525b5bae-b0b9-47bc-95d6-f200bb21f889",
#             "parentId": "fa8c36b0-df95-44b1-8bda-6bbeda00a604",
#             "name": "03.1 成本对比分析明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "556d0fd6-d16f-4805-84cd-ff11b42f3379",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "项目全盘营销费效比统计表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5910d23c-e31b-4476-9d97-ffcb9b2f6e3c",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "07 认购未签约明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5962e9f9-42b8-47f0-95d9-03fc1f646bdd",
#             "parentId": "2f718b54-7e54-4a9c-ae03-168f4cef6bee",
#             "name": "09.1 合同执行与付款明细表（科目维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5b64180b-f8b8-49bf-9ba6-302e89cf8820",
#             "parentId": "95703ec5-ca45-4ad0-8544-5209b39e95ea",
#             "name": "11 房源台账",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5dfef107-70aa-42aa-a98e-6f84ca4d10aa",
#             "parentId": "2fc03e5a-e640-4841-a0ea-cb8ff640f86a",
#             "name": "13 战采计划分析报表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "5eca1158-ac45-440a-b62e-3c9feca3ed4e",
#             "parentId": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "name": "06 沉寂供应商分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "62596bee-eb91-4785-b957-c51479d1600a",
#             "parentId": "6fb472f7-200a-43b5-abdb-3d5b5f187a93",
#             "name": "02 项目成本异常监控表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "666a5ce2-18c1-4acf-889e-89c8b7ee4de2",
#             "parentId": "78b55200-3917-4398-8000-ac2220345fdb",
#             "name": "18 房间回款节点办理明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "6a19fd7f-7280-4a4a-b739-f64bcfbbc8b6",
#             "parentId": "95703ec5-ca45-4ad0-8544-5209b39e95ea",
#             "name": "10 可售资源统计－按面积段",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "6acce6cc-afcf-4e46-8e15-043900b9ebf6",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "发票及付款差额台账",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "7150b004-181b-45d4-bc85-c99488e181db",
#             "parentId": "ac3652bd-1ec9-4c88-9714-126db87516d9",
#             "name": "07 合同变更汇总表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "71d2ec33-9a11-48d3-ad1a-649dc3a5508a",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0303签约退房明细表",
#             "type": "FILE",
#             "isPublish": false,
#             "levelCode": null
#         },
#         {
#             "id": "75075994-d2ae-4f60-9ddc-1e467a8254b6",
#             "parentId": "905c348d-9475-406e-8feb-e26e23d29fc6",
#             "name": "13.1 资金计划执行明细表（部门维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "7570d312-6f53-4137-a45d-6a810421a7e5",
#             "parentId": "53ff0ff0-0680-42e8-8b18-4aceec640e2f",
#             "name": "09 采购计划分析报表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "78b55200-3917-4398-8000-ac2220345fdb",
#             "parentId": "464a5ff8-792b-4fbe-a1f0-4958e8cccaa3",
#             "name": "18 各项目回款节点办理情况",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "78caae1f-63e8-463b-acd0-558bfdb54332",
#             "parentId": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "name": "02 供应商合作情况分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "80f62381-1377-4707-a982-9ceb2a59f7a0",
#             "parentId": "c8b01253-9ac5-46c0-ab8f-eae7af3035a7",
#             "name": "08 逾期认购未签约统计_副本1685498794",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "8229cada-d59f-4d35-9933-046bc6842f1a",
#             "parentId": "c077ea72-3e80-4228-937a-32196d7f2f1e",
#             "name": "楼栋指标",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "83488b8c-7256-4c68-9a75-0805cbc17e37",
#             "parentId": "c8b01253-9ac5-46c0-ab8f-eae7af3035a7",
#             "name": "08 逾期认购未签约统计",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "8cccc0cd-a1d3-4a9f-a064-c57115f4314b",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "公司年度营销费用进度及费率分析表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "8e8d009a-2205-4999-96c5-74f582bd7e24",
#             "parentId": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "name": "05 供应商年度寻源入库统计（按类别）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "8ef7e1e9-9a4a-4a85-8aca-1e94a26b2608",
#             "parentId": "87b2861e-ceb1-495f-8d6c-322bfdb3bf05",
#             "name": "房间佣金明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "905c348d-9475-406e-8feb-e26e23d29fc6",
#             "parentId": "9fc53028-4508-4bee-b707-7bcb1262767f",
#             "name": "13 资金计划执行汇报表（部门维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "90e9c789-47b1-45b1-909d-031771090654",
#             "parentId": "1a6565d6-8ee6-46bc-9fee-d320c3418f5f",
#             "name": "项目编制状态统计报表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "96e48bd6-e2fe-466e-b890-a08c512c1719",
#             "parentId": "c077ea72-3e80-4228-937a-32196d7f2f1e",
#             "name": "项目指标",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a0dd1d5e-dcdc-4dbc-96b0-cef00ff3e604",
#             "parentId": "cdde3694-a35a-4167-ad45-00934d846f33",
#             "name": "EXCEL套表导出记录查询",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a100938f-72b9-45d5-84f3-cb349befe13d",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0304增减权益人明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a20d5d7a-2761-49cd-9f79-af4eef6e7cc0",
#             "parentId": "2e50c99c-eea0-499a-9fa7-7da6be58ccab",
#             "name": "04 核算对象建筑成本－目标与动态对比表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a33a1d67-0f4c-44ea-9b18-282db1271c4c",
#             "parentId": "000b825b-e097-4cbe-82ea-5d33136dd4b6",
#             "name": "10 项目支付情况汇总表（合同维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a52b45c3-5d79-43ce-954d-38267e174dfe",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "合同台账",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "a57daa09-4a36-4d25-af88-6ac0b634e86c",
#             "parentId": "6fb472f7-200a-43b5-abdb-3d5b5f187a93",
#             "name": "01 项目动态成本汇总表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "b4074e6d-aa15-4f00-86a3-a328ed179049",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "05 销售月报",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "bd456777-1751-467d-b939-eee3583860df",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "04 销售日报",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "bd50a126-0c2c-40d1-8450-7bb8b294bdae",
#             "parentId": "87b2861e-ceb1-495f-8d6c-322bfdb3bf05",
#             "name": "佣金结算汇总",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "bf57dbb3-b9db-4317-a88d-074c3bb08a2b",
#             "parentId": "762f59ed-03bc-48b3-9c2c-74cc2ef4ebb5",
#             "name": "13 回款汇总",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "bf8f1aaf-72df-49ae-8379-c7b21c684a09",
#             "parentId": "ef28e5d4-e3bf-4733-be8d-04464823fc54",
#             "name": "08.1 合同结算台账表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "c4ddba46-ddf4-4f6a-a656-3557f69f0800",
#             "parentId": "96e48bd6-e2fe-466e-b890-a08c512c1719",
#             "name": "项目附件",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "c586e556-3e5f-4e98-afb2-e275f5bbb2e6",
#             "parentId": "5161a368-2b81-4e1c-82d3-b97a2ff53eee",
#             "name": "项目节点达成率统计",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "c77754b4-7afc-4775-9599-d11d37fc4035",
#             "parentId": "6ea91415-8d82-4cad-8c24-944260e834ef",
#             "name": "合同请款明细台账",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "cb8a0d15-21b9-4a22-b05d-c8bd462e37fb",
#             "parentId": "53ff0ff0-0680-42e8-8b18-4aceec640e2f",
#             "name": "11 执行战采金额统计（按供应商）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "cbed4d2c-66ae-495b-9697-f25373ca57b7",
#             "parentId": "654fdd09-f647-4e07-8105-ff6b6e1e0d5e",
#             "name": "项目节点完成情况明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "cc3d5ebb-6ffc-444e-9ee7-cf784420a662",
#             "parentId": "f200e9cf-9508-4368-8586-c2ab5c3dfc2b",
#             "name": "12.1 资金计划执行明细表（项目维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "d6737f1c-f6ed-4e6c-adf4-6fa1aab41812",
#             "parentId": "b293b595-7009-41da-aba9-919efe7b2e26",
#             "name": "04 供应商年度寻源入库统计（按公司）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "d886576e-c8ea-44d0-b1e9-f6a57d0abbda",
#             "parentId": "762f59ed-03bc-48b3-9c2c-74cc2ef4ebb5",
#             "name": "15 应收款账龄分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e02278e9-b877-4da7-a28a-da4dd58cd3a8",
#             "parentId": "87b2861e-ceb1-495f-8d6c-322bfdb3bf05",
#             "name": "人员佣金明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e040a7f4-aa23-4dc4-b0cb-3c4b077e32ef",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "02 认购明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e31ec4b8-8a5e-422a-99c8-f3b278d2bd02",
#             "parentId": "43344b24-2ef4-4702-93f2-d949370622d2",
#             "name": "YX-JY0302认购退房明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e6108bbc-f65e-4fdd-bc7b-be7673e98a23",
#             "parentId": "32a84bd5-5d78-4b52-a148-4ffb95d1b3d1",
#             "name": "06 预约明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "e631d1f1-9f9d-4510-8a4d-af97fee18133",
#             "parentId": "62596bee-eb91-4785-b957-c51479d1600a",
#             "name": "02.1 项目成本异常监控明细表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "ea5a0b0c-e4a1-4b47-98f0-2a1149d8369d",
#             "parentId": "762f59ed-03bc-48b3-9c2c-74cc2ef4ebb5",
#             "name": "14 实收款明细",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "ecd20ad2-6089-4d37-874e-e194a7423ceb",
#             "parentId": "53ff0ff0-0680-42e8-8b18-4aceec640e2f",
#             "name": "10 执行战采金额统计（按公司）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "eeadf9a5-2ba0-4ff0-a896-7722f4042e35",
#             "parentId": "8229cada-d59f-4d35-9933-046bc6842f1a",
#             "name": "楼栋附件",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "ef28e5d4-e3bf-4733-be8d-04464823fc54",
#             "parentId": "5c27d90a-9e49-4e36-a592-994a793b735f",
#             "name": "08 项目结算情况汇总表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "f200e9cf-9508-4368-8586-c2ab5c3dfc2b",
#             "parentId": "9fc53028-4508-4bee-b707-7bcb1262767f",
#             "name": "12 资金计划执行汇报表（项目维度）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "f9449649-5c42-4984-ad9a-bf50527d2bc1",
#             "parentId": "4cb85e84-129c-4d8a-b075-bb3b95a5fc2f",
#             "name": "07 合同履约评估执行分析",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "fa8c36b0-df95-44b1-8bda-6bbeda00a604",
#             "parentId": "2e50c99c-eea0-499a-9fa7-7da6be58ccab",
#             "name": "03 成本对比分析表",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         },
#         {
#             "id": "fd9b1694-6106-4c31-9018-60fd8fdf8aa2",
#             "parentId": "53ff0ff0-0680-42e8-8b18-4aceec640e2f",
#             "name": "12 执行战采金额统计（按类别）",
#             "type": "FILE",
#             "isPublish": true,
#             "levelCode": null
#         }
#     ]
# }"""
#         data = json.loads(data)
        if not data.get('result'):
            logger.error(f'<{self.tenant_code}> 获取erp报表失败: {data}')
            raise Exception(f'获取erp报表失败: {data}')
        return data.get('data') or []
