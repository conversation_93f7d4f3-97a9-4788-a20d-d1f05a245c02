#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
支持删除DMP平台使用的报告元数据缓存

支持的extra参数:
    project_codes: 操作的租户code，用逗号分隔开 test,dev_test,ycm1
    cache_prefix: 缓存的版本前缀 dmp_v1
    cache_class: 缓存业务模块名 dashboard/dataset

eg:
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=flush_redis_command&type=include
&tenants=test&extra={"project_codes": "test", "cache_prefix": "dmp_v1"}
"""

# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.redis import RedisCache


logger = logging.getLogger(__name__)


class FlushRedisCommand(UpgradeCommand):
    """
    删除报告元数据缓存入口
    """

    def _exec_delete(self, cur, match_keys, pattern, conn):
        logger.exception("当前缓存游标位移值: {0}，待删除键数量：{1}".format(cur, len(match_keys)))
        if match_keys:
            for key in match_keys:
                conn.delete(key)
        if cur:
            next_cur, next_keys = conn.scan(cursor=cur, match=pattern, count=1000)
            self._exec_delete(next_cur, next_keys, pattern, conn)

    def _clear_cache(self, pattern, conn):
        """
        删除模糊查询到的缓存key数据
        :param pattern:
        :return:
        """
        try:
            cur, match_keys = conn.scan(match=pattern, count=1000)
            self._exec_delete(cur, match_keys, pattern, conn)
        except Exception as e:
            message = '''Error 租户code:{0} TaskId:{1} 模糊查询匹配模式:{2} 删除缓存内容失败! 异常信息{3}'''.format(
                self.tenant_code, self.task_id, pattern, str(e)
            )
            logger.exception(message)

    def exec(self):
        project_codes = self.project_codes
        cache_prefix = self.cache_prefix
        project_code_list = str(project_codes).split(",") if project_codes else []

        if not cache_prefix:
            return False

        conn = RedisCache()
        # 清理指定租户code的元数据缓存key数据
        if project_code_list and len(project_code_list):
            logger.exception("=============开始删除指定租户缓存内容=============")
            for project_code in project_code_list:
                if not project_code:
                    continue
                pattern = "*{project_code}:{cache_prefix}:*".format(
                    project_code=project_code, cache_prefix=cache_prefix
                )
                self._clear_cache(pattern, conn)
            logger.exception("=============完成删除指定租户缓存内容=============")

        # 清理指定缓存版本前缀下所有租户code的元数据缓存key数据
        else:
            logger.exception("=============开始删除指定key缓存内容=============")
            pattern = "*:{cache_prefix}:*".format(cache_prefix=cache_prefix)
            self._clear_cache(pattern, conn)
            logger.exception("=============>完成缓存内容删除=============>")

        return True
