#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from components.dmp_api import DMPAPI
from dmplib import config
from dmplib.saas.project import get_db

logger = logging.getLogger(__name__)


class SysDeliveryFolderUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._flush_sys_delivery_folder()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _flush_sys_delivery_folder(self):
        from issue_dashboard.repositories import dataset_repository
        from issue_dashboard.services.dashboard_service import DashboardQueryBuilder, DEFAULT_FOLDER_NAME
        from project.models import ProjectModel
        from issue_dashboard.constants import DELIVER_DEFAULT_FOLDER_NAME

        db = get_db(self.tenant_code)

        logging.exception("新增数据集系统分发文件夹")
        dataset_parent_id = dataset_repository.create_default_folder(self.tenant_code)

        logging.exception("新增报表系统分发文件夹")
        db_name = config.get('DBInstaller.project_db_prefix', 'dmp') + '_' + self.tenant_code
        dashboard_parent_id = DashboardQueryBuilder(ProjectModel(code=self.tenant_code, db_name=db_name)).create_default_folder(db).get('id')

        logging.exception("获取历史所有系统分发数据集(不包括新增的固定id系统分发文件夹)")
        dataset_list = db.query(
            "select child.id as child_id, parent.id as parent_id from dap_bi_dataset as child inner join dap_bi_dataset as parent on parent.id=child.parent_id and parent.name=%(name)s and parent.id != %(default_id)s",
            params={
                'name': DELIVER_DEFAULT_FOLDER_NAME, 'default_id': dataset_parent_id
            }
        ) or []
        sys_delivery_folder = set()
        api = DMPAPI(self.tenant_code)
        for dataset in dataset_list:
            try:
                # 将所有数据集移动到默认的系统分发文件夹
                api.move_dataset(dataset.get('child_id'), dataset_parent_id)
                sys_delivery_folder.add(dataset.get('parent_id'))
            except Exception as e:
                logging.exception(f'move error: {e}')
        # 删除所有的历史系统分发文件夹
        sys_delivery_folder = list(sys_delivery_folder)
        del_dataset_ids = db.query_columns(
            'select id from dap_bi_dataset where name=%(name)s and id!=%(id)s',
            params={
                'name': DELIVER_DEFAULT_FOLDER_NAME,
                'id': dataset_parent_id
            }
        ) or []
        sys_delivery_folder += del_dataset_ids
        for _id in sys_delivery_folder:
            db.delete('dap_bi_dataset', {'id': _id})

        logging.exception("获取历史所有系统分发报表(不包括新增的固定id系统分发文件夹)")
        dashboard_list = db.query(
            "select child.id as child_id, parent.id as parent_id from dap_bi_dashboard as child inner join dap_bi_dashboard as parent on parent.id=child.parent_id and parent.name=%(name)s and parent.id != %(default_id)s",
            params={
                'name': DEFAULT_FOLDER_NAME, 'default_id': dashboard_parent_id
            }
        ) or []
        sys_delivery_dash_folder = set()
        for dashboard in dashboard_list:
            try:
                api.move_dashboard(dashboard.get('child_id'), dashboard_parent_id)
                sys_delivery_dash_folder.add(dashboard.get('parent_id'))
            except Exception as e:
                logging.exception(f'move error: {e}')
        # 删除所有的历史系统分发文件夹
        sys_delivery_dash_folder = list(sys_delivery_dash_folder)
        del_dashboard_ids = db.query_columns(
            'select id from dap_bi_dashboard where name=%(name)s and id!=%(id)s',
            params={
                'name': DEFAULT_FOLDER_NAME,
                'id': dashboard_parent_id
            }
        ) or []
        sys_delivery_dash_folder += del_dashboard_ids
        for _id in sys_delivery_dash_folder:
            db.delete('dap_bi_dashboard', {'id': _id})


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)

    command = SysDeliveryFolderUpgradeCommand(tenant_code="test")
    command.exec()









