import logging

from base.errors import InvalidCallError
from dmplib import config
from dmplib.conf_constants import IS_GRAY_ENV
from dmplib.utils.errors import UserError

from upgrade.command.command import CommandGenerator


class Executor:
    def __init__(self, command_generator: CommandGenerator, tenant_codes: list = None):
        # 命令生成器
        self._command_generator = command_generator
        # 待执行命令的租户代码列表
        self.tenant_codes = tenant_codes if tenant_codes else []
        # 命令执行者容器
        self._command_list = list()

    def get_tenant_codes(self) -> list:
        """
        获取租户代码
        :param kwargs:
        :return:
        """
        raise InvalidCallError(message="请在子类中重写该方法！")

    def create_commander(self, **kwargs):
        """
        执行命令对象
        :param kwargs:
        :return:
        """
        return self._command_generator.create_command(**kwargs)

    def get_command_list(self, **kwargs):
        """
        获取执执行器
        :param kwargs:
        :return:
        """
        tenant_codes = self.get_tenant_codes()
        for tenant_code in tenant_codes:
            kwargs["tenant_code"] = tenant_code
            try:
                command = self.create_commander(**kwargs)
            except Exception as e:
                logging.error(msg=str(e))
            self._command_list.append(command)
        return self._command_list

    def run(self, **kwargs):
        """
        命令控制类
        :param kwargs:
        """
        command_list = self.get_command_list(**kwargs)
        for command in command_list:
            command.start()


class AllExecutor(Executor):
    """
    全量执行
    """

    def get_tenant_codes(self):
        self.tenant_codes = get_curenv_codes()
        return self.tenant_codes



class IncludeExecutor(Executor):
    """
    包含执行
    """
    def get_tenant_codes(self):
        tenant_code_list = get_curenv_codes()
        rv = []
        for tenant_code in tenant_code_list:
            if tenant_code in self.tenant_codes:
                rv.append(tenant_code)
        return rv


class ExcludeExecutor(Executor):
    """
    排除执行
    """

    def get_tenant_codes(self):
        tenant_code_list = get_curenv_codes()
        rv = []
        for tenant_code in tenant_code_list:
            if tenant_code not in self.tenant_codes:
                rv.append(tenant_code)
        return rv

def get_curenv_codes():
    codes = config.get('Grayscale.project_list', '')
    gray_codes = codes.split(',') if codes else []
    if IS_GRAY_ENV == 1:
        return gray_codes
    from project.models import ProjectQueryModel
    from project.services import project_service

    # 云客环境租户超过5000个， 修改为取10000个
    page_data = {'page': 1, 'page_size': 10000}
    tenant_data = project_service.get_project_list(ProjectQueryModel(**page_data)).get_result_dict()
    code_list = [item.get("code") for item in tenant_data.get("items")]
    rv = []
    for code in code_list:
        if code not in gray_codes:
            rv.append(code)
    return rv
