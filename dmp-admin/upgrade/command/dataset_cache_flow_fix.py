#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/29 11:33
# <AUTHOR> songh02
import logging
import time

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from components.rundeck import CommonRunDeckScheduler

logger = logging.getLogger(__name__)


class DatasetCacheFlowFix(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_dataset_cache_flow()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_dataset_cache_flow(self):
        with get_tenant_db(self.tenant_code) as db:
            dataset_list = db.query_columns("select id from flow where type='数据集缓存' and id not in (select cache_flow_id from dataset where cache_flow_id is not null and cache_flow_id!='') ")
            if not dataset_list:
                return
            rundeck = CommonRunDeckScheduler()
            for dataset_id in dataset_list:
                rundeck.delete_job("{}_{}".format(self.tenant_code, dataset_id))
                time.sleep(0.01)
            db.exec_sql("delete from dap_bi_instance where flow_id in %(dataset_ids)s", params={"dataset_ids": dataset_list})
            db.exec_sql("delete from dap_bi_flow where id in %(dataset_ids)s", params={"dataset_ids": dataset_list})


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)

    command = DatasetCacheFlowFix(tenant_code="test")
    command._update_dataset_cache_flow()


