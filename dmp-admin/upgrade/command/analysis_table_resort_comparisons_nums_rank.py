import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from components.dmp_api import DMPAPI

logger = logging.getLogger(__name__)


class AnalysisTableResortComparisonsNumsRank(UpgradeCommand):
    """自助分析对比维度和度量rank重排"""
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._merge()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _merge(self):
        with get_tenant_db(self.tenant_code) as db:
            if self.extra:
                self._upgrade_dashboard(db, self.extra)
                return
            dashboard_ids = db.query_columns("select id from dap_bi_dashboard where application_type=1 and type!='FOLDER'")
            for dashboard_id in dashboard_ids:
                self._upgrade_dashboard(db, dashboard_id)

    def _upgrade_dashboard(self, db, dashboard_id):
        chart_id = db.query_scalar("select id from dap_bi_dashboard_chart where dashboard_id=%s and chart_code='analysis_table'", params=[dashboard_id])
        if not chart_id:
            return
        chart_comparison_ids = db.query_columns("select id from dap_bi_dashboard_chart_comparison where dashboard_chart_id=%s order by `rank` asc", params=[chart_id])
        if not chart_comparison_ids:
            return
        chart_num_ids = db.query_columns("select id from dap_bi_dashboard_chart_num where dashboard_chart_id=%s order by `rank` asc", params=[chart_id])
        if not chart_num_ids:
            return
        rank = 0
        for id in chart_comparison_ids:
            db.exec_sql("update dap_bi_dashboard_chart_comparison set rank=%s where id=%s", [rank, id])
            rank += 1
        for id in chart_num_ids:
            db.exec_sql("update dap_bi_dashboard_chart_num set rank=%s where id=%s", [rank, id])
            rank += 1
