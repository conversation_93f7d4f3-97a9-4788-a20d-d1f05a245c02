#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/9/18 17:24
# <AUTHOR> caoxl
# @File     : fix_filter_chart_fixed_value_command.py

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class FixFilterChartFixedValueCommand(UpgradeCommand):
    def exec(self):
        logger.exception(f"租户 {self.tenant_code} 开始执行升级脚本")
        try:
            bad_filter_chart_fixed_values = self._get_bad_filter_chart_fixed_value(self.tenant_code) or []
        except Exception as e:
            logger.exception(f"租户 {self.tenant_code} 获取异常固定值筛选出错，错误信息 {str(e)}")
            return False
        for item in bad_filter_chart_fixed_values:
            if not item.get("id") or not item.get("dashboard_id"):
                continue
            try:
                result = self._update_filter_chart_fixed_value(self.tenant_code, item["id"], item["dashboard_id"])
                result_msg = "成功" if result else "失败"
                logger.exception(
                    msg=f"报告 {item.get('dashboard_id')} 单图 {item.get('chart_name')} 无数据集筛选配置 {item.get('id')} 填充字段 dashboard_id 为 {item.get('dashboard_id')} {result_msg}"
                )
            except Exception as e:
                logger.exception(msg=f"升级发生异常 {str(e)}")
        return True

    def _get_bad_filter_chart_fixed_value(self, tenant_code):
        sql = """SELECT f.id,c.dashboard_id,c.name as chart_name FROM `dap_bi_dashboard_filter_chart_fixed_value` f LEFT JOIN `dap_bi_dashboard_chart` c
        ON f.chart_id=c.id WHERE f.dashboard_id='' OR f.dashboard_id IS NULL """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    def _update_filter_chart_fixed_value(self, tenant_code, fixed_value_id, dashboard_id, is_debug=False):
        if is_debug:
            return True
        with get_tenant_db(tenant_code) as db:
            return db.update(
                "dap_bi_dashboard_filter_chart_fixed_value", {"dashboard_id": dashboard_id}, {"id": fixed_value_id}
            )
