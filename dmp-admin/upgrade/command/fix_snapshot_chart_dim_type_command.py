#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
单图快照表升级
"""

# ---------------- 标准模块 ----------------
import logging
import json
from json import JSONDecodeError

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from base.tenant_repository import update_data
from dmplib.utils.errors import UserError


logger = logging.getLogger(__name__)


class FixSnapshotChartDimTypeCommand(UpgradeCommand):
    """

    """

    def exec(self):
        """

        :return:
        """
        try:
            self.upgrade_snapshot()
            self.upgrade_dashboard_dims()
        except BaseException as e:
            message = '''Error 租户code:{0} TaskId:{1} 维度类型字段升级 异常信息{2}'''.format(
                self.tenant_code, self.task_id, str(e)
            )
            logger.exception(message)
            raise UserError(message=message)

    def upgrade_snapshot(self):
        """

        :return:
        """
        snapshot_data = self.get_snapshot_data(self.tenant_code) or []
        for single_item in snapshot_data:
            if not single_item:
                continue
            pk = single_item.get("increment_id")
            chart_id = single_item.get("id")
            snapshot_id = single_item.get("snapshot_id")
            dims = single_item.get("dims")
            dims_data = json.loads(dims)
            if not dims_data:
                continue
            for i in dims_data:
                # 只更新第一条数据，因为一个单图中只能有一个地理信息
                i["dim_type"] = 1
                break
            self.update_snapshot_data(self.tenant_code, dims_data, pk, chart_id, snapshot_id)

    @staticmethod
    def get_snapshot_data(tenant_code):
        """

        :return:
        """
        sql = """SELECT increment_id,id,snapshot_id,dims FROM dap_bi_dashboard_released_snapshot_chart 
              where chart_code in ('area_map','area_map_cure', 'label_map') and dims!='' and dims is not null """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def update_snapshot_data(tenant_code, dims, pk, chart_id, snapshot_id):
        """

        :param tenant_code:
        :param dims:
        :param pk:
        :param chart_id:
        :param snapshot_id:
        :return:
        """
        return update_data(
            tenant_code,
            "dap_bi_dashboard_released_snapshot_chart",
            {"dims": json.dumps(dims)},
            {"increment_id": pk, "id": chart_id, "snapshot_id": snapshot_id},
        )

    def upgrade_dashboard_dims(self):
        dim_list = self.get_dashboard_dims(self.tenant_code) or []
        # 一个单图只存在一个地理字段
        updated_chart_ids = []
        for v in dim_list:
            if v['dashboard_chart_id'] in updated_chart_ids:
                continue
            else:
                updated_chart_ids.append(v['dashboard_chart_id'])
                self.update_dashboard_dim_type(self.tenant_code, v['id'])

    @staticmethod
    def get_dashboard_dims(tenant_code):
        """取出预览态需要更新dim_type的数据"""
        sql = """
            SELECT dcd.id, dashboard_chart_id from dap_bi_dashboard_chart_dim dcd join dap_bi_dashboard_chart dc
                ON dcd.dashboard_chart_id=dc.id
            where dc.chart_code in ("area_map","area_map_cure", "label_map")
            order by dcd.`rank` asc
        """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def update_dashboard_dim_type(tenant_code: str, chart_dim_id: str):
        return update_data(
            tenant_code,
            "dap_bi_dashboard_chart_dim",
            {"dim_type": 1},
            {"id": chart_dim_id},
        )
