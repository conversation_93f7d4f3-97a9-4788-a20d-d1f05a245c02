#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
单图排序配置数据数据迁移升级
注：尽量不重复执行
"""

# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.utils.strings import seq_id
from upgrade.models import ChartFieldSortModel
from base.tenant_repository import update_data

logger = logging.getLogger(__name__)


class DashboardChartFieldSortUpgradeCommand(UpgradeCommand):
    """

    """

    def exec(self):
        if not self.tenant_code:
            return True

        try:
            # 数据升级-预览的几张表
            self.upgrade_preview()

            # 数据升级-快照表
            self.upgrade_snapshot()

        except BaseException as e:
            message = '''Error 租户code:{0} TaskId:{1} 单图排序配置数据数据迁移升级任务 异常信息{2}'''.format(
                self.tenant_code, self.task_id, str(e)
            )
            logger.exception(message)
            return False

    def upgrade_preview(self):
        """

        :return:
        """
        # 维度
        chart_dims_field_sort_data = self.get_chart_dims_field_sort_data(self.tenant_code)
        self._batch_add_field_sort(chart_dims_field_sort_data, match_key="dim", field_source="dims")
        # 度量
        chart_nums_field_sort_data = self.get_chart_nums_field_sort_data(self.tenant_code)
        self._batch_add_field_sort(chart_nums_field_sort_data, match_key="num", field_source="nums")
        # 次轴
        chart_zaxis_field_sort_data = self.get_chart_zaxis_field_sort_data(self.tenant_code)
        self._batch_add_field_sort(chart_zaxis_field_sort_data, match_key="num", field_source="zaxis")
        # 对比
        chart_comparisons_field_sort_data = self.get_chart_comparisons_field_sort_data(self.tenant_code)
        self._batch_add_field_sort(
            chart_comparisons_field_sort_data, match_key="dataset_field_id", field_source="comparisons"
        )
        # 目标值
        chart_desires_field_sort_data = self.get_chart_desires_field_sort_data(self.tenant_code)
        self._batch_add_field_sort(chart_desires_field_sort_data, match_key="dataset_field_id", field_source="desires")
        return True

    def upgrade_snapshot(self):
        """
        快照表升级
        :return:
        """
        snapshot_data = self.get_snapshot_data(self.tenant_code)
        if not snapshot_data:
            return True
        for single_item in snapshot_data:
            if not single_item:
                continue
            pk = single_item.get("increment_id")
            chart_id = single_item.get("id")
            snapshot_id = single_item.get("snapshot_id")
            dims = single_item.get("dims")
            nums = single_item.get("nums")
            zaxis = single_item.get("zaxis")
            comparisons = single_item.get("comparisons")
            desires = single_item.get("desires")

            field_sorts = []
            # 维度
            self._append_field_sorts(self._get_loads_data(dims), "dim", "dims", snapshot_id, field_sorts)
            # 度量
            self._append_field_sorts(self._get_loads_data(nums), "num", "nums", snapshot_id, field_sorts)
            # 次轴
            self._append_field_sorts(self._get_loads_data(zaxis), "num", "zaxis", snapshot_id, field_sorts)
            # 对比
            self._append_field_sorts(
                self._get_loads_data(comparisons), "dataset_field_id", "comparisons", snapshot_id, field_sorts
            )
            # 目标值
            self._append_field_sorts(
                self._get_loads_data(desires), "dataset_field_id", "desires", snapshot_id, field_sorts
            )

            # 插入field_sorts数据
            self.add_snapshot_field_sort(self.tenant_code, field_sorts, pk, chart_id, snapshot_id)

    @staticmethod
    def _get_loads_data(data):
        """
        json.loads
        :param data:
        :return:
        """
        return json.loads(data) if data and isinstance(data, str) else []

    @staticmethod
    def _append_field_sorts(data, match_key, field_source, snapshot_id, field_sorts):
        """
        拼装field_sort数据
        :param data:
        :param field_sorts:
        :return:
        """
        for item in data:
            if not item.get("sort"):
                continue
            field_sort = ChartFieldSortModel()
            field_sort.id = seq_id()
            field_sort.dashboard_id = snapshot_id
            field_sort.dashboard_chart_id = item.get("dashboard_chart_id")
            field_sort.dataset_field_id = item.get(match_key, "")
            field_sort.field_source = field_source
            field_sort.sort = item.get("sort")
            field_sort.content = item.get("content", "")
            field_sort.weight = 0
            field_sorts.append(field_sort.get_dict())

    def _batch_add_field_sort(self, data, match_key, field_source):
        """
        批量处理
        :param data:
        :param match_key:
        :param field_source:
        :return:
        """
        if not data:
            return False
        for item in data:
            self.add_single_field_sort(self.tenant_code, item, match_key, field_source)
        return True

    @staticmethod
    def add_single_field_sort(tenant_code, item, match_key, field_source):
        """
        主键冲突的情况下只更新sort和content
        :return:
        """
        field_sort_model = ChartFieldSortModel()
        field_sort_model.id = seq_id()
        field_sort_model.dashboard_id = item.get("dashboard_id")
        field_sort_model.dashboard_chart_id = item.get("dashboard_chart_id")
        field_sort_model.dataset_field_id = item.get(match_key, "")
        field_sort_model.field_source = field_source
        field_sort_model.sort = item.get("sort")
        field_sort_model.content = item.get("content", "")
        field_sort_model.weight = 0

        sql = """INSERT INTO `dap_bi_dashboard_chart_field_sort` ( `id`, `dashboard_id`, `dashboard_chart_id`, 
              `dataset_field_id`, `field_source`, `sort`, `content`, `weight`, `created_by`, `modified_by` ) 
              VALUES (%(id)s, %(dashboard_id)s, %(dashboard_chart_id)s, %(dataset_field_id)s, %(field_source)s, 
              %(sort)s, %(content)s, %(weight)s, %(created_by)s, %(modified_by)s) 
              ON DUPLICATE KEY UPDATE sort=%(sort)s, content=%(content)s, 
              created_by=%(weight)s, modified_by=%(modified_by)s """
        params = field_sort_model.get_dict()
        params["created_by"] = "celery"
        params["modified_by"] = "celery"
        with get_tenant_db(tenant_code) as db:
            db.exec_sql(sql, params=params)

    @staticmethod
    def add_snapshot_field_sort(tenant_code, field_sorts, pk, chart_id, snapshot_id):
        """

        :param tenant_code:
        :param field_sorts:
        :param pk:
        :param chart_id:
        :param snapshot_id:
        :return:
        """
        return update_data(
            tenant_code,
            "dap_bi_dashboard_released_snapshot_chart",
            {"field_sorts": json.dumps(field_sorts)},
            {"increment_id": pk, "id": chart_id, "snapshot_id": snapshot_id},
        )

    @staticmethod
    def get_chart_dims_field_sort_data(tenant_code):
        """
        查询dim表排序数据
        :param tenant_code:
        :return:
        """
        sql = """SELECT dc.dashboard_id,dcd.dashboard_chart_id,dcd.dim,dcd.sort,dcd.content
              FROM dap_bi_dashboard_chart_dim dcd 
              left join dap_bi_dashboard_chart dc on dcd.dashboard_chart_id=dc.id
              where dcd.sort is not null and dcd.sort!='' 
              and dcd.dashboard_chart_id is not null and dcd.dashboard_chart_id!='' 
              and dc.dashboard_id is not null and dc.dashboard_id!='' """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def get_chart_nums_field_sort_data(tenant_code):
        """
        查询num表排序数据
        :param tenant_code:
        :return:
        """
        sql = """SELECT dc.dashboard_id,dcn.dashboard_chart_id,dcn.num,dcn.sort
              FROM dap_bi_dashboard_chart_num dcn 
              left join dap_bi_dashboard_chart dc on dcn.dashboard_chart_id=dc.id
              where dcn.sort is not null and dcn.sort!='' 
              and dcn.dashboard_chart_id is not null and dcn.dashboard_chart_id!='' 
              and dc.dashboard_id is not null and dc.dashboard_id!=''
              and dcn.axis_type=0 """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def get_chart_zaxis_field_sort_data(tenant_code):
        """
        查询num表排序数据
        :param tenant_code:
        :return:
        """
        sql = """SELECT dc.dashboard_id,dcn.dashboard_chart_id,dcn.num,dcn.sort
              FROM dap_bi_dashboard_chart_num dcn 
              left join dap_bi_dashboard_chart dc on dcn.dashboard_chart_id=dc.id
              where dcn.sort is not null and dcn.sort!='' 
              and dcn.dashboard_chart_id is not null and dcn.dashboard_chart_id!='' 
              and dc.dashboard_id is not null and dc.dashboard_id!=''
              and dcn.axis_type=1 """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def get_chart_comparisons_field_sort_data(tenant_code):
        """
        查询comparison表排序数据
        :param tenant_code:
        :return:
        """
        sql = """SELECT dc.dashboard_id,dcc.dashboard_chart_id,dcc.dataset_field_id,dcc.sort
              FROM dap_bi_dashboard_chart_comparison dcc 
              left join dap_bi_dashboard_chart dc on dcc.dashboard_chart_id=dc.id
              where dcc.sort is not null and dcc.sort!='' 
              and dcc.dashboard_chart_id is not null and dcc.dashboard_chart_id!='' 
              and dc.dashboard_id is not null and dc.dashboard_id!='' """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def get_chart_desires_field_sort_data(tenant_code):
        """
        查询desire表排序数据
        :param tenant_code:
        :return:
        """
        sql = """SELECT dc.dashboard_id,dcd.dashboard_chart_id,dcd.dataset_field_id,dcd.sort
              FROM dap_bi_dashboard_chart_desire dcd 
              left join dap_bi_dashboard_chart dc on dcd.dashboard_chart_id=dc.id
              where dcd.sort is not null and dcd.sort!='' 
              and dcd.dashboard_chart_id is not null and dcd.dashboard_chart_id!='' 
              and dc.dashboard_id is not null and dc.dashboard_id!='' """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def get_snapshot_data(tenant_code):
        """

        :return:
        """
        sql = """SELECT increment_id,id,snapshot_id,dims,nums,zaxis,desires,comparisons
              from dap_bi_dashboard_released_snapshot_chart
              where snapshot_id is not null and snapshot_id !='' """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)
