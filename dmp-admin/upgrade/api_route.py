#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

import hug
import logging

import app_celery
from dmplib.utils.strings import seq_id
from components.celery import get_task_id
from dmplib.hug import APIWrapper
from upgrade.services import upgrade_service, graph_upgrade_service
import upgrade.command.executor
from upgrade.command.command import CommandGenerator

api = APIWrapper(__name__)


@api.admin_route.get("/run", output=hug.output_format.html)
def run_data_upgrade_command(**kwargs):
    """
    数据升级
    :param kwargs:
    :return:
    """
    try:
        # 升级类型到计算租户代码对应的类
        type2tenants_cls = {
            'all': upgrade.command.executor.AllExecutor,
            'include': upgrade.command.executor.IncludeExecutor,
            'exclude': upgrade.command.executor.ExcludeExecutor,
        }
        upgrade_type = kwargs.get('type')
        command_name = kwargs.get('command_name')
        if upgrade_type not in type2tenants_cls:
            logging.error(msg='升级类型有误！')
            return 'type参数不正确，只能在 [all, include, exclude] 中任选其一！'
        # 把每一个租户的升级分解为一个任务，这样可以使用多线程并发执行
        tenant_codes = []
        if kwargs.get("tenants"):
            tenant_codes = upgrade_service.convert_to_list_param(kwargs.get("tenants"))
        cls = type2tenants_cls[upgrade_type]
        command_generator = CommandGenerator(command_name)
        tenant_codes = cls(command_generator, tenant_codes).get_tenant_codes()
        if not tenant_codes:
            return '没有租户代码，不能执行升级'
        for tenant_code in tenant_codes:
            celery_kwargs = {
                "task_id": seq_id(),
                "command_name": command_name,
                "upgrade_type": 'include',
                "tenants": [tenant_code],
                "extra": kwargs.get('extra') or "",
                "project_codes": kwargs.get("project_codes", ""),
                "cache_class": kwargs.get("cache_class", ""),
                "cache_prefix": kwargs.get("cache_prefix", ""),
            }
            app_celery.upgrade_data.delay(**celery_kwargs)
    except Exception as e:
        _msg = "任务写入队列失败，错误信息: {msg}, 请重试!".format(msg=str(e))
        logging.error(msg=_msg)
        return _msg
    return "数据升级任务已交由异步脚本执行，现在它正在排队中，请耐心等待..."


@api.admin_route.get('/logs')
def get_upgrade_logs(**kwargs):
    """
    获取升级记录
    :param kwargs:
    :return:
    """
    command_name = kwargs.get("command_name")
    tenant_code = kwargs.get("tenant_code") or ''
    if not command_name:
        return False, "请指定命名名称", []
    return True, '', upgrade_service.get_upgrade_logs(command_name, tenant_code)


@api.admin_route.get("/test_run", output=hug.output_format.html)
def test_data_upgrade_command(**kwargs):
    """
    测试数据升级
    :param kwargs:
    :return:
    """
    result, msg = upgrade_service.start_upgrade(
        task_id='aaaa-sdfsdfsdf-adfsdf', command_name=kwargs.get('command'), upgrade_type='include', tenants=['dev']
    )
    if not result:
        return msg
    return '升级完成'


@api.admin_route.get("/stop", output=hug.output_format.html)
def stop_data_upgrade_command(**kwargs):
    """
    停止数据升级
    :param kwargs:
    :return:
    """
    pass


@api.admin_route.get('/enable_developer')
def enable_developer(**kwargs):
    """
    自动开启开发者模式，并给管理员发送邮件
    """
    from upgrade.services.developer_service import enable_developer, enable_all_developer
    tenant_code_list = kwargs.get('tenant_code') or []
    if not tenant_code_list:
        return True, '开启全量切换成功', enable_all_developer()
    return True, '开启部分切换成功', enable_developer(tenant_code_list)


@api.admin_route.get('/add_printing_report')
def add_printing_report(**kwargs):
    """
    批量开启租户套打报表的权限
    """
    from upgrade.services.developer_service import add_printing_report
    tenant_code_list = kwargs.get('tenant_code') or []
    if not tenant_code_list:
        return False, '租户code不能为空', []
    if isinstance(tenant_code_list, str):
        tenant_code_list = [tenant_code_list]
    return True, '添加成功', add_printing_report(tenant_code_list)


@api.admin_route.get('/upgrade_to_graph')
def upgrade_to_graph(**kwargs):
    return True, '成功', graph_upgrade_service.upgrade(kwargs.get('tenant_code'), kwargs.get('is_all_tenant'))


@api.admin_route.get('/get_upgrade_graph_result')
def upgrade_to_graph(**kwargs):
    return True, '成功', graph_upgrade_service.get_result(kwargs.get('task_id'))


@api.admin_route.get('/test_get_upgrade_info')
def upgrade_to_graph(**kwargs):
    return True, '成功', graph_upgrade_service.test_get_upgrade_info(kwargs.get('tenant_code'))
