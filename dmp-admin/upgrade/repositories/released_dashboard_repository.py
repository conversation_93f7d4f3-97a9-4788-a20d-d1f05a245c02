#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/30

"""
报告发布模块资源代码
"""
from dmplib.saas.project import get_db


def get_released_chart_filter_config(tenant_code):
    """
    获取需要升级的发布表的记录
    :param str tenant_code: 租户code
    :return:
    """
    sql = """
    SELECT id,snapshot_id,dashboard_id,filter_config FROM dap_bi_dashboard_released_snapshot_chart  
    WHERE filter_config IS NOT NULL AND filter_config != '' AND filter_config != 'null'
    """
    param = {}
    with get_db(tenant_code) as db:
        return db.query(sql, param)


def get_released_dashboard_chart_data(tenant_code, snapshot_id, dashboard_id):
    """
    获取已发布报告单图数据
    :return:
    """
    sql = '''select * from dap_bi_dashboard_released_snapshot_chart where snapshot_id="{snapshot_id}" 
          and dashboard_id="{dashboard_id}"
          '''.format(
        snapshot_id=snapshot_id, dashboard_id=dashboard_id
    )
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def insert_released_chart_record(tenant_code, data):
    """
    插入数据
    :param tenant_code:
    :param data:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.insert('dap_bi_dashboard_released_snapshot_chart', data)


def get_released_chart_id(tenant_code, snapshot_id, dashboard_id, chart_id):
    """
    获取已发布单图id记录
    :param tenant_code:
    :param snapshot_id:
    :param dashboard_id:
    :param chart_id:
    :return:
    """
    sql = '''select id from dap_bi_dashboard_released_snapshot_chart where snapshot_id="{snapshot_id}" 
          and dashboard_id="{dashboard_id}" and id="{chart_id}"
          '''.format(
        snapshot_id=snapshot_id, dashboard_id=dashboard_id, chart_id=chart_id
    )
    params = {}
    with get_db(tenant_code) as db:
        return db.query_one(sql, params)


def get_released_dashboard_data(tenant_code):
    """
    获取已发布报告数据
    :return:
    """
    sql = '''select id,snapshot_id,is_multiple_screen from dap_bi_dashboard_released_snapshot_dashboard 
          where data_type=0'''
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_chart_with_no_logic_type(tenant_code):
    """
    获取logic_type为空的记录
    :param tenant_code:
    :return:
    """
    sql = '''select id,chart_code from dap_bi_dashboard_released_snapshot_chart where 
          (data_logic_type_code="" or data_logic_type_code is null)'''
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)
