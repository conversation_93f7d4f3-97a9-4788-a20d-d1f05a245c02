from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id, uniqid


def add_task(tenant_list):
    task_id = seq_id()
    if repository.get_data_scalar_by_sql('select 1 from dap_bi_dataset_graph_upgrade_task where `status` in %(status)s',
                                         {
                                             'status': ['已创建', '执行中']
                                         }):
        raise UserError(code=400, message='已存在升级任务，请勿重复创建')
    repository.add_data('dap_bi_dataset_graph_upgrade_task', {
        'id': task_id,
        'tenant_list': ','.join(tenant_list),
        'status': '已创建',
        'bak_id': uniqid()
    })
    return task_id


def get_task(task_id):
    return repository.get_data('dap_bi_dataset_graph_upgrade_task', {
        'id': task_id,
    }, ['id', 'tenant_list', 'status', 'bak_id', 'result'])


def update_task_status(task_id, status, commit=True):
    return repository.execute_sql('update dap_bi_dataset_graph_upgrade_task set `status`=%(status)s where id=%(id)s', {
        'status': status,
        'id': task_id
    }, commit=commit)


def get_task_item_by_tenant_code(tenant_code, commit=True):
    result = repository.get_data_by_sql(
        'select * from dap_bi_dataset_graph_upgrade_task_item where `tenant_code`=%(tenant_code)s order by created_on desc limit 1',
        {
            'tenant_code': tenant_code
        })
    if result:
        return result[0]
    return None


def end_task_item(task_item_id, success, log):
    return repository.execute_sql(
        'update dap_bi_dataset_graph_upgrade_task_item set `status`=%(status)s,`log`=%(log)s where `id`=%(id)s', {
            'status': 1 if success else 2,
            'log': log,
            'id': task_item_id
        })


def end_task_with_exception(task_id, exception):
    return repository.update_data('dap_bi_dataset_graph_upgrade_task', {
        'status': '执行失败',
        'exception': exception,
    }, {'id': task_id})


def end_task(task_id, result):
    return repository.update_data('dap_bi_dataset_graph_upgrade_task', {
        'result': result,
        'status': '执行结束'
    }, {'id': task_id})
