#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/3/12 15:13
# <AUTHOR> caoxl
# @File     : upgrade.py
from dmplib.db.mysql_wrapper import get_db


def get_upgrade_logs(command_name, tenant_code):
    if not tenant_code:
        sql = """SELECT task_id,tenant_code,command_name,status,execute_msg,created_on,created_by FROM `dap_bi_upgrade_log` WHERE command_name=%(command_name)s order by created_on desc"""
        with get_db() as db:
            return db.query(sql, {"command_name": command_name})
    else:
        sql = """SELECT task_id,tenant_code,command_name,status,execute_msg,created_on,created_by FROM `dap_bi_upgrade_log` WHERE command_name=%(command_name)s and tenant_code=%(tenant_code)s order by created_on desc"""
        with get_db() as db:
            return db.query(sql, {"command_name": command_name, "tenant_code": tenant_code})
