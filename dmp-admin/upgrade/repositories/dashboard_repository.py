#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/30
# pylint: skip-file

"""
报告模块资源代码
"""
import json
from base.enums import DistributeType
from dmplib.saas.project import get_db
from issue_dashboard.constants import get_condition_fields


def get_dataset_fields(tenant_code, dataset_id):
    """
    获取数据集的字段信息
    :param dataset_id:
    :return:
    """
    sql = """
    SELECT id,alias_name,dataset_id,col_name,data_type,field_group,`rank`,type,expression,format 
    FROM dap_bi_dataset_field 
    WHERE dataset_id=%(dataset_id)s AND visible=1
    ORDER BY `rank` ASC
    """
    param = {"dataset_id": dataset_id}
    with get_db(tenant_code) as db:
        return db.query(sql, param)


def batch_get_datasets_fields(tenant_code, dataset_ids):
    """
    批量获取数据集及字段信息
    :param tenant_code:
    :param dataset_ids:
    :return:
    """
    if not dataset_ids:
        return []
    params = {}
    in_conditions = []
    for index, dataset_id in enumerate(dataset_ids):
        _key = "dataset_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = dataset_id
    sql = """
                    select id,dataset_id,alias_name,col_name,origin_col_name,data_type,group_type,type,field_group 
                    from `dap_bi_dataset_field`  
                    where dataset_id IN ({in_condition})
                    """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def batch_get_charts_dims(tenant_code, chart_ids):
    """
    批量获取单图维度
    :param tenant_code:
    :param chart_ids:
    :return:
    """
    if not chart_ids:
        return []
    params = {}
    in_conditions = []
    for index, chart_id in enumerate(chart_ids):
        _key = "chart_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = chart_id
    sql = """
                    select dashboard_chart_id,dim as dataset_field_id  
                    from `dap_bi_dashboard_chart_dim`  
                    where dashboard_chart_id IN ({in_condition}) 
                    order by `rank` asc 
                    """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def batch_get_charts_nums(tenant_code, chart_ids):
    """
    批量获取单图度量
    :param tenant_code:
    :param chart_ids:
    :return:
    """
    if not chart_ids:
        return []
    params = {}
    in_conditions = []
    for index, chart_id in enumerate(chart_ids):
        _key = "chart_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = chart_id
    sql = """
                    select dashboard_chart_id,num as dataset_field_id  
                    from `dap_bi_dashboard_chart_num`  
                    where dashboard_chart_id IN ({in_condition}) 
                    order by `rank` asc 
                    """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_component_filter_config_data(tenant_code):
    """
    获取需要进行筛选器配置数据升级的数据列表
    :return:
    """
    sql = "select id, filter_config from dap_bi_dashboard_chart_backup where upgrade_status=0"
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_has_jump_dims(tenant_code):
    """
    获取拥有跳转配置的dim
    :param tenant_code:
    :return:
    """
    sql = "SELECT " "dim.dashboard_chart_id,dim.dim AS dataset_field_id,dim.alias,dim.dashboard_jump_config,dim.id," "dc.dashboard_id,0 as dataset_field_type " "FROM dap_bi_dashboard_chart_dim dim " "LEFT JOIN dap_bi_dashboard_chart dc ON dc.id=dim.dashboard_chart_id " "WHERE `dashboard_jump_config`!=''"
    with get_db(tenant_code) as db:
        return db.query(sql)


def get_has_jump_nums(tenant_code):
    """
    获取拥有跳转配置的num
    :param tenant_code:
    :return:
    """
    sql = "SELECT " "num.dashboard_chart_id,num.num AS dataset_field_id,num.alias,num.dashboard_jump_config,num.id," "dc.dashboard_id,1 as dataset_field_type " "FROM dap_bi_dashboard_chart_num num " "LEFT JOIN dap_bi_dashboard_chart dc ON dc.id=num.dashboard_chart_id " "WHERE `dashboard_jump_config`!=''"
    with get_db(tenant_code) as db:
        return db.query(sql)


def get_jump_config(
    tenant_code, dashboard_chart_id, dataset_field_id, dataset_field_type
):
    """
    获取单图字段跳转配置
    :param tenant_code:
    :param dashboard_chart_id:
    :param dataset_field_id:
    :param dataset_field_type:
    :return:
    """
    sql = "SELECT id,dashboard_id,dashboard_chart_id,dataset_field_id,target,target_type,open_way,status,has_token " "FROM `dap_bi_dashboard_jump_config` " "WHERE `dashboard_chart_id`=%(dashboard_chart_id)s AND " "`dataset_field_id`=%(dataset_field_id)s AND `dataset_field_type`=%(dataset_field_type)s "
    params = {
        'dashboard_chart_id': dashboard_chart_id,
        'dataset_field_id': dataset_field_id,
        'dataset_field_type': dataset_field_type,
    }
    with get_db(tenant_code) as db:
        return db.query_one(sql, params)


def get_all_params_jump(tenant_code):
    """
    获取所有参数跳转
    :param tenant_code:
    :return:
    """
    sql = "SELECT " "`dashboard_id`,`dashboard_chart_id`,`param_dataset_field_id`,`chart_dataset_field_id`,`dashboard_filter_id`," "`rank`,`created_on`,`created_by`,`modified_on`,`modified_by` " "FROM `dap_bi_dashboard_chart_params_jump` "
    with get_db(tenant_code) as db:
        return db.query(sql)


def upgrade_dashboard_jump(
    tenant_code, dashboard_jump_config, dashboard_jump_relations=None
):
    """
    升级报告跳转(临时)
    :param tenant_code:
    :param dashboard_jump_config:
    :param dashboard_jump_relation:
    :return:
    """
    # 使用事务
    with get_db(tenant_code) as db:
        # 更新数据
        db.replace_multi_data(
            table="dap_bi_dashboard_jump_config",
            list_data=[dashboard_jump_config],
            fields=list(dashboard_jump_config.keys()),
            commit=False,
            condition_field=['id']
        )
        db.delete(
            table='dap_bi_dashboard_jump_relation', condition={'jump_config_id': dashboard_jump_config.get('id')}, commit=False
        )
        if dashboard_jump_relations:
            for dashboard_jump_relation in dashboard_jump_relations:
                db.replace_multi_data(
                    table="dap_bi_dashboard_jump_relation",
                    list_data=[dashboard_jump_relation],
                    fields=list(dashboard_jump_relation.keys()),
                    commit=False,
                    condition_field=get_condition_fields('dap_bi_dashboard_jump_relation')
                )
        return db.commit()


def upgrade_released_param_jump(tenant_code, dashboard_chart_id, data):
    """
    升级已发布的参数跳转
    :param tenant_code:
    :param dashboard_chart_id:
    :param data:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.update(
            "dap_bi_dashboard_released_snapshot_chart",
            {"chart_params_jump": json.dumps(data)},
            {"id": dashboard_chart_id},
        )


def upgrade_released_dashboard_filters(tenant_code, dashboard_id, dashboard_filters):
    """
    升级发布报告报告筛选
    :param tenant_code:
    :param dashboard_id:
    :param dashboard_filters:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.update(
            'dap_bi_dashboard_released_snapshot_dashboard',
            {'dashboard_filters': json.dumps(dashboard_filters)},
            {'id': dashboard_id},
        )


def get_has_filter_released_dashboards(tenant_code):
    """
    获取有筛选的已发布的报告
    :return:
    """
    sql = "SELECT id,increment_id,dashboard_filters,snapshot_id,type,level_code,platform," "is_multiple_screen,status,scale_mode,background,theme,refresh_rate,type_access_released " "FROM dap_bi_dashboard_released_snapshot_dashboard " "WHERE dashboard_filters!=''"
    with get_db(tenant_code) as db:
        return db.query(sql)


def get_all_dashboard_filter_relations(tenant_code):
    """
    获取所有报告筛选的关联关系
    :param tenant_code:
    :return:
    """
    sql = "SELECT `id`,`dashboard_id`,`main_dataset_field_id`,`related_dataset_field_id` " "FROM `dap_bi_dashboard_dataset_field_relation`"
    with get_db(tenant_code) as db:
        return db.query(sql)


def get_released_chart_data(tenant_code, dashboard_chart_id):
    """
    获取已发布单图数据
    :param tenant_code:
    :param dashboard_chart_id:
    :return:
    """
    sql = "SELECT id AS dashboard_chart_id,snapshot_id,dashboard_id,name,chart_code,chart_type,level_code,source," "parent_id,sort_method,,position,filter_config,dims,nums,comparisons,zaxis,desires,filters,marklines," "chart_params,jump,data_logic_type_code " "FROM dap_bi_dashboard_released_snapshot_chart " "WHERE dashboard_chart_id=%(dashboard_chart_id)s"
    params = {'dashboard_chart_id': dashboard_chart_id}
    with get_db(tenant_code) as db:
        return db.query_one(sql, params)


def upgrade_released_chart_jump_config(tenant_code, dashboard_chart_id, jump_config):
    """
    升级报告单图跳转配置
    :param tenant_code:
    :param jump_config:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.update(
            "dap_bi_dashboard_released_snapshot_chart",
            {"jump": jump_config},
            {"id": dashboard_chart_id},
        )


def get_screen_dashboard_data(tenant_code, dashboard_id):
    """
    获取多屏ID对应的报告
    :param tenant_code:
    :param dashboard_id:
    :return:
    """
    sql = """select dashboard_id,screen_id from dap_bi_screen_dashboard where dashboard_id="{dashboard_id}"
              """.format(
        dashboard_id=dashboard_id
    )
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_dashboard_chart_data(tenant_code, dashboard_id):
    """
    获取已发布报告数据
    :return:
    """
    sql = """select id,dashboard_id,name,chart_code,filter_config,page_size,data_logic_type_code,chart_type,
          content,source,data_modified_on,position,sort_method,penetrate,parent_id,level_code,refresh_rate,
          display_item,desired_value,percentage,style_type,default_value,layout,layout_extend,config,layers,
          column_order
          from dap_bi_dashboard_chart where dashboard_id="{dashboard_id}"
          """.format(
        dashboard_id=dashboard_id
    )
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_chart_with_penetrates(tenant_code):
    """
    获取有穿透的父级单图
    :param tenant_code:
    :return:
    """
    sql = '''select id,position,penetrate,level_code from dap_bi_dashboard_chart where 
          position!="" and penetrate=1 and parent_id=""'''
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def update_position_by_level_code(tenant_code, position, level_code, chart_id):
    """
    更新子级单图position
    :param tenant_code:
    :param position:
    :param level_code:
    :param chart_id:
    :return:
    """
    sql = """update dap_bi_dashboard_chart set position='{position}' where position='' and 
          level_code like '{level_code}%%' and id!='{chart_id}'
          """.format(
        position=position, level_code=level_code, chart_id=chart_id
    )
    params = {}
    with get_db(tenant_code) as db:
        return db.exec_sql(sql, params)


def get_component_data(tenant_code):
    """
    获取component
    :param tenant_code:
    :return:
    """
    sql = """select package,data_logic_type_code from component """
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_chart_with_no_logic_type(tenant_code):
    """
    获取logic_type为空的记录
    :param tenant_code:
    :return:
    """
    sql = """select id,chart_code from dap_bi_dashboard_chart where (data_logic_type_code="" or data_logic_type_code is null)"""
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def update_data_logic_type_code(
    tenant_code, dashboard_chart_id, data_logic_type_code, table_name
):
    """
    更新data_logic_type_code
    :param tenant_code:
    :param dashboard_chart_id:
    :param data_logic_type_code:
    :param table_name:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.update(
            table_name,
            {"data_logic_type_code": data_logic_type_code},
            {"id": dashboard_chart_id},
        )


def get_dashboard_filters_data(tenant_code):
    """
    获取报告级筛选
    :param tenant_code:
    :return:
    """
    sql = """select id,dashboard_id,operator,col_value,select_all_flag from dap_bi_dashboard_filter"""
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_dashboard_chart_filters_data(tenant_code):
    """
    获取单图筛选
    :param tenant_code:
    :return:
    """
    sql = """select id,dashboard_chart_id,operator,col_value from dap_bi_dashboard_chart_filter"""
    params = {}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def delete_dashboard_filter_relation(tenant_code, filter_id):
    """
    删除报告筛选operator数据
    :param tenant_code:
    :param filter_id:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.delete(
            table="dap_bi_dashboard_filter_relation",
            condition={"dashboard_filter_id": filter_id},
        )


def delete_dashboard_chart_filter_relation(tenant_code, filter_id):
    """
    删除单图筛选operator数据
    :param tenant_code:
    :param filter_id:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.delete(
            table="dap_bi_dashboard_chart_filter_relation",
            condition={"dashboard_chart_filter_id": filter_id},
        )


def insert_dashboard_filter_relation(tenant_code, data):
    """
    新增报告筛选operator数据
    :param tenant_code:
    :param data:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.insert(table="dap_bi_dashboard_filter_relation", data=data)


def insert_dashboard_chart_filter_relation(tenant_code, data):
    """
    新增单图筛选operator数据
    :param tenant_code:
    :param data:
    :return:
    """
    with get_db(tenant_code) as db:
        return db.insert(table="dap_bi_dashboard_chart_filter_relation", data=data)


def get_all_dashboard_component_filter(tenant_code):
    """
    获取所有报告组件筛选配置
    :param tenant_code:
    :return:
    """
    sql = """select 
            df.id,df.chart_initiator_id,df.chart_responder_id,df.dataset_id,df.is_same_dataset,dc.dashboard_id
            from dap_bi_dashboard_component_filter df left join dap_bi_dashboard_chart dc on df.chart_initiator_id=dc.id
        """
    with get_db(tenant_code) as db:
        return db.query(sql)


def batch_get_dashboard_chart_datasets(tenant_code, chart_ids):
    """
    批量获取报告单图的数据集
    :param tenant_code:
    :param chart_ids:
    :return:
    """
    if not chart_ids:
        return []
    params = {}
    in_conditions = []
    for index, chart_id in enumerate(chart_ids):
        _key = "chart_id_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = chart_id
    sql = """
    select id as chart_id, source as dataset_id from `dap_bi_dashboard_chart` where id IN ({in_condition})
    """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def batch_get_chart_dims(tenant_code, chart_ids):
    """
    批量获取chart的维度
    :param tenant_code:
    :param chart_ids:
    :return:
    """
    if not chart_ids:
        return []
    params = {}
    in_conditions = []
    for index, chart_id in enumerate(chart_ids):
        _key = "chart_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = chart_id
    sql = """
        select dcd.dashboard_chart_id, dcd.dim, dcd.alias,dcd.formula_mode,dcd.rank,dcd.sort,dcd.id,df.dataset_id  
        from `dap_bi_dashboard_chart_dim` dcd
        left join dap_bi_dataset_field df on df.id=dcd.dim 
        where `dashboard_chart_id` IN ({in_condition})
        """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def batch_get_component_filter_relations(tenant_code, filter_config_ids):
    """
    批量获取component_fitler 关联关系
    :param tenant_code:
    :param filter_config_ids:
    :return:
    """
    if not filter_config_ids:
        return []
    params = {}
    in_conditions = []
    for index, config_id in enumerate(filter_config_ids):
        _key = "fitler_config_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = config_id
    sql = """
            select relation.id, relation.filter_id, relation.chart_id, relation.field_initiator_id,
            relation.field_responder_id,filter.dataset_id 
            from `dap_bi_dashboard_component_filter_field_relation`  relation left join dap_bi_dashboard_component_filter filter 
            on relation.filter_id=filter.id
            where relation.filter_id IN ({in_condition})
            """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def batch_get_dataset_fields(tenant_code, dataset_field_ids):
    """
    批量获取字段信息
    :param tenant_code:
    :param dataset_ids:
    :return:
    """
    if not dataset_field_ids:
        return []
    params = {}
    in_conditions = []
    for index, dataset_field_id in enumerate(dataset_field_ids):
        _key = "dataset_field_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = dataset_field_id
    sql = """
                select dataset_id,id,alias_name  
                from `dap_bi_dataset_field`  
                where id IN ({in_condition})
                """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def batch_insert_filter_charts(tenant_code, configs, relations):
    """
    批量插入筛选和关联关系
    :param tenant_code:
    :param configs:
    :param relations:
    :return:
    """
    with get_db(tenant_code) as db:
        try:
            db.begin_transaction()
            db.replace_multi_data(
                "dap_bi_dashboard_filter_chart",
                configs,
                [
                    "id",
                    "chart_id",
                    "dataset_field_id",
                    "dataset_id",
                    "dashboard_id",
                ],
                condition_field=get_condition_fields("dap_bi_dashboard_filter_chart")
            )
            db.replace_multi_data(
                "dap_bi_dashboard_filter_chart_relation",
                relations,
                [
                    "id",
                    "filter_id",
                    "chart_responder_id",
                    "field_responder_id",
                    "dataset_responder_id",
                    "dashboard_id",
                ],
                condition_field=get_condition_fields("dap_bi_dashboard_filter_chart_relation")
            )

            db.commit()
            return True, ""
        except Exception as e:
            return False, str(e)


def get_has_default_value_charts(tenant_code):
    """
    获取拥有默认值的单图
    :param tenant_code:
    :return:
    """
    sql = """
    select id,dashboard_id,name,chart_code,data_logic_type_code,chart_type,source,parent_id,level_code,default_value 
    from dap_bi_dashboard_chart where default_value is not null and default_value != '' 
    """
    with get_db(tenant_code) as db:
        return db.query(sql)


def batch_del_filter_chart_default_values(tenant_code, dashboard_ids):
    """
    批量删除报告默认配置
    :param tenant_code:
    :param dashboard_ids:
    :return:
    """
    if not dashboard_ids:
        return True
    params = {}
    in_conditions = []
    for index, dashboard_id in enumerate(dashboard_ids):
        _key = "dashboard_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = dashboard_id
    sql = """
                    delete  
                    from `dap_bi_dashboard_filter_chart_default_values`  
                    where dashboard_id IN ({in_condition})
                    """.format(
        in_condition=",".join(in_conditions)
    )
    with get_db(tenant_code) as db:
        return db.exec_sql(sql, params)


def batch_insert_filter_chart_default_values(tenant_code, data):
    """
    批量插入筛选默认值
    :param tenant_code:
    :param data:
    :return:
    """
    with get_db(tenant_code) as db:
        try:
            db.begin_transaction()
            for item in data:
                each = {
                    "id": item.get("id"),
                    "dashboard_id": item.get("dashboard_id"),
                    "chart_id": item.get("chart_id"),
                    "dataset_id": item.get("dataset_id"),
                    "dataset_field_id": item.get("dataset_field_id"),
                    "operator": item.get("operator"),
                    "value": item.get("value"),
                    "select_all": item.get("select_all"),
                    "extend_data": item.get("extend_data")
                }
                db.insert(
                    "dap_bi_dashboard_filter_chart_default_values",
                    each
                )
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False


def get_dashboard_chart_selector_list(tenant_code):
    """
    获取需要升级的旧联动数据
    :param tenant_code:
    :return:
    """
    sql = """SELECT dcs.id, dcs.chart_initiator_id, dcs.chart_responder_id, dcs.dashboard_id, dcs.type, 
             dcs.is_same_dataset, dcsf.field_initiator_id, dcsf.field_responder_id, 
             idf.dataset_id as dataset_initiator_id, rdf.dataset_id as dataset_responder_id 
             FROM dap_bi_dashboard_chart_selector dcs 
             LEFT JOIN dap_bi_dashboard_chart_selector_field dcsf 
             ON dcs.id=dcsf.selector_id 
             LEFT JOIN dap_bi_dataset_field as idf on idf.id=dcsf.field_initiator_id
             LEFT JOIN dap_bi_dataset_field as rdf on rdf.id=dcsf.field_responder_id"""
    with get_db(tenant_code) as db:
        return db.query(sql)


def get_dashboard_chart_dim_list(tenant_code, chart_id):
    """
    根据单图id获取单图中的维度数据
    :param str tenant_code: 租户code
    :param str chart_id: 单图id
    :return:
    """
    sql = """SELECT dcd.dim, df.dataset_id 
             FROM dap_bi_dashboard_chart_dim as dcd 
             LEFT JOIN dap_bi_dataset_field as df on dcd.dim=df.id 
             WHERE dashboard_chart_id=%(chart_id)s"""
    params = {"chart_id": chart_id}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_distribute_dashboard_list(tenant_code):
    """
    获取系统分发报告文件夹
    :param tenant_code:
    :return:
    """
    sql = """select id,distribute_type from dap_bi_dashboard 
          where parent_id="" and level_code="9000-" and distribute_type=%(default_type)s"""
    params = {"default_type": DistributeType.Default.value}
    with get_db(tenant_code) as db:
        return db.query(sql, params)


def get_dashboard_linkage_id(tenant_code, chart_id, dataset_field_id):
    """
    根据单图id和字段id获取数据联动记录
    :param str tenant_code: 租户code
    :param str chart_id: 单图id
    :param str dataset_field_id: 字段id
    :return:
    """
    sql = """SELECT id FROM dap_bi_dashboard_linkage WHERE chart_id=%(chart_id)s and dataset_field_id=%(field_id)s"""
    params = {"chart_id": chart_id, "field_id": dataset_field_id}
    with get_db(tenant_code) as db:
        return db.query_scalar(sql, params)


def get_dashboard_linkage_relation_id(tenant_code, link_id, chart_id, dataset_field_id):
    """
    根据单图id和字段id获取数据联动关联记录
    :param str tenant_code: 租户code
    :param str link_id: 联动id
    :param str chart_id: 单图id
    :param str dataset_field_id: 字段id
    :return:
    """
    sql = """SELECT id FROM dap_bi_dashboard_linkage_relation 
             WHERE link_id=%(link_id)s and chart_responder_id=%(chart_id)s and field_responder_id=%(field_id)s"""
    params = {"link_id": link_id, "chart_id": chart_id, "field_id": dataset_field_id}
    with get_db(tenant_code) as db:
        return db.query_scalar(sql, params)


def update_distribute_dashboard(tenant_code, dashboard_id):
    """
    更新系统分发报告文件夹distribute_type字段
    :param tenant_code:
    :param dashboard_id:
    :return:
    """
    if not tenant_code or not dashboard_id:
        return False
    with get_db(tenant_code) as db:
        return db.update(
            "dap_bi_dashboard",
            {"distribute_type": DistributeType.Distribute.value},
            {"id": dashboard_id},
        )


def batch_del_dashboard_filter_charts(tenant_code, dashboard_ids: list):
    """
    批量删除报告的单图筛选
    :param tenant_code:
    :param dashboard_ids:
    :return:
    """
    if not dashboard_ids:
        return
    params = {}
    in_conditions = []
    for index, dashboard_id in enumerate(dashboard_ids):
        _key = "dashboard_" + str(index)
        in_conditions.append("%({word})s".format(word=_key))
        params[_key] = dashboard_id
    tables = ["dap_bi_dashboard_filter_chart", "dap_bi_dashboard_filter_chart_relation"]
    sqls = [
        "DELETE FROM `{table}` WHERE `dashboard_id` IN ({in_condition})".format(
            table=table, in_condition=",".join(in_conditions)
        )
        for table in tables
    ]
    with get_db(tenant_code) as db:
        for sql in sqls:
            db.exec_sql(sql, params, False)
        db.commit()
