#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/8/30
"""

import unittest
import os
import json


os.environ["DMP_CFG_FILE_PATH"] = os.path.join(
    os.path.dirname(__file__), "../../app.config"
)
os.environ["DMP_ROOT_PATH"] = os.path.dirname(__file__)


from tests.base import BaseTest
import logging

from upgrade.services import upgrade_service
import random

logger = logging.getLogger(__name__)


class TestCommand(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name)
        super().__init__(method_name, code='test', account='cc')

    def test_upgrade(self):
        kwargs = {"task_id": str(random.randrange(1, 1000000)),
                  "command_name": "fix_filter_chart_fixed_value_command",
                  "upgrade_type": "include",
                  "tenants": ["test"]}
        result, msg = upgrade_service.start_upgrade(**kwargs)
        if not result:
            print(msg)
        else:
            print('升级成功!')

    def test_get_logs(self):
        command_name = "column_field_sorts_upgrade_command"
        logs = upgrade_service.get_upgrade_logs(command_name)
        print(logs)

    def test_fix_level(self):
        kwargs = {"task_id": str(random.randrange(1, 1000000)),
                  "command_name": "fix_level_code_command",
                  "upgrade_type": "include",
                  "tenants": ["debug_level"]}
        result, msg = upgrade_service.start_upgrade(**kwargs)
        if not result:
            print(msg)
        else:
            print('升级成功!')

    def test_msg_statistics(self):
        kwargs = {
                    "task_id": str(random.randrange(1, 1000000)),
                    "command_name": "msg_used_dataset_statistics_command",
                    "upgrade_type": "include",
                    "tenants": ["uitest"]
                  }
        result, msg = upgrade_service.start_upgrade(**kwargs)
        if not result:
            print(msg)
        else:
            print('计算完成!')

    def test_dashboard_diff(self):
        extra = {"dashboard_ids": ["3a0937b9-f820-dd1e-e805-08e6929f1c42", "3a04828c-ac69-bd23-6570-60f8a0e5c109"],
                 "code": "uitest"}
        extra_json = json.dumps(extra)
        kwargs = {
                    "task_id": str(random.randrange(1, 1000000)),
                    "command_name": "dashboard_diff_result_command",
                    "upgrade_type": "include",
                    "tenants": ["uitest"],
                    "extra": extra_json
        }
        result, msg = upgrade_service.start_upgrade(**kwargs)
        if not result:
            print(msg)
        else:
            print('计算完成!')


if __name__ == "__main__":
    unittest.main()
