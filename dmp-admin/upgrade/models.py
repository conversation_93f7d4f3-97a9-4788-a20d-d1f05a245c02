#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/8/30
"""
from base.models import BaseModel


class UpgradeLogModel(BaseModel):
    __slots__ = ["id", "task_id", "tenant_code", "command_name", "status", "execute_msg"]

    def __init__(self, **kwargs):
        self.id = None
        self.task_id = None
        self.tenant_code = None
        self.command_name = None
        self.status = None
        self.execute_msg = ""
        super().__init__(**kwargs)

    # 0：已创建 1:执行中 2:已成功 3:已中止 4:已失败
    @property
    def create_status(self):
        return 0

    @property
    def running_status(self):
        return 1

    @property
    def success_status(self):
        return 2

    @property
    def stop_status(self):
        return 3

    @property
    def fail_status(self):
        return 4


class DashboardComponentFilterModel(BaseModel):
    __slots__ = ["id", "chart_initiator_id", "chart_responder_id", "dataset_id", "is_same_dataset"]

    def __init__(self, **kwargs):
        """
        单图组件过滤器模型
        :param kwargs:
        """
        self.id = None
        self.chart_initiator_id = None
        self.chart_responder_id = None
        self.dataset_id = None
        self.is_same_dataset = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((["chart_initiator_id", "chart_responder_id", "dataset_field_id"], "string", {"max": 36}))
        return rules


class DashboardComponentFilterFieldModel(BaseModel):
    __slots__ = ["id", "field_initiator_id", "field_responder_id", "chart_id", "filter_id"]

    def __init__(self, **kwargs):
        """
        单图组件过滤器字段关系模型
        :param kwargs:
        """
        self.id = None
        self.field_initiator_id = None
        self.field_responder_id = None
        self.chart_id = None
        self.filter_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((["field_initiator_id", "field_responder_id", "chart_id", "filter_id"], "string", {"max": 36}))
        return rules


class DashboardJumpConfigModel(BaseModel):
    """
    报告跳转配置model
    """

    __slots__ = [
        "dashboard_id",
        "dashboard_chart_id",
        "dataset_field_id",
        "target",
        "target_type",
        "open_way",
        "status",
        "has_token",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.target = None
        self.target_type = None
        self.open_way = None
        self.status = None
        self.has_token = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("dashboard_id", "string", {"max": 36}))
        rules.append(("dashboard_chart_id", "string", {"max": 36}))
        rules.append(("dataset_field_id", "string", {"max": 36}))
        rules.append(("target", "string", {"max": 1000}))
        rules.append(("target_type", "in_range", {"range": ["dashboard", "url"]}))
        rules.append(("open_way", "in_range", {"range": [1, 2, 3]}))
        rules.append(("status", "in_range", {"range": [0, 1]}))
        rules.append(("has_token", "in_range", {"range": [0, 1]}))
        return rules


class DashboardJumpRelationModel(BaseModel):
    """
    报告跳转-筛选关系model
    """

    __slots__ = ["jump_config_id", "dashboard_chart_id", "dashboard_id", "dashboard_filter_id", "dataset_field_id"]

    def __init__(self, **kwargs):
        self.jump_config_id = None
        self.dashboard_chart_id = None
        self.dashboard_id = None
        self.dashboard_filter_id = None
        self.dataset_field_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("jump_config_id", "string", {"max": 36}))
        rules.append(("dashboard_chart_id", "string", {"max": 36}))
        rules.append(("dashboard_id", "string", {"max": 36}))
        rules.append(("dashboard_filter_id", "string", {"max": 36}))
        rules.append(("dataset_field_id", "string", {"max": 36}))
        return rules


class DashboardLinkageModel(BaseModel):
    """
    单图联动model
    """

    __slots__ = ["id", "chart_id", "dataset_field_id", "dataset_id", "dashboard_id"]

    def __init__(self, **kwargs):
        self.id = None
        self.chart_id = None
        self.dataset_field_id = None
        self.dataset_id = None
        self.dashboard_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((["chart_id", "dataset_field_id", "dataset_id", "dashboard_id"], "string", {"max": 36}))
        return rules


class DashboardLinkageRelationModel(BaseModel):
    """
    单图联动关系model
    """

    __slots__ = ["id", "link_id", "chart_responder_id", "field_responder_id", "dataset_responder_id", "dashboard_id"]

    def __init__(self, **kwargs):
        self.id = None
        self.link_id = None
        self.chart_responder_id = None
        self.field_responder_id = None
        self.dataset_responder_id = None
        self.dashboard_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            (
                ["link_id", "chart_responder_id", "field_responder_id", "dataset_responder_id", "dashboard_id"],
                "string",
                {"max": 36},
            )
        )
        return rules


class ChartFieldSortModel(BaseModel):
    __slots__ = [
        'id',
        'dashboard_id',
        'dashboard_chart_id',
        'dataset_field_id',
        'field_source',
        'sort',
        'content',
        'weight',
    ]

    def __init__(self, **kwargs):
        """

        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.field_source = None
        self.sort = None
        self.content = None
        self.weight = None
        super().__init__(**kwargs)
