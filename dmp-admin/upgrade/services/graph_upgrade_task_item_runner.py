import json
from datetime import datetime

from loguru import logger

from components import graph_upgrade_helper
from dmplib.saas.project import get_db


class GraphUpgradeTaskItemRunner:
    def __init__(self, task, task_item):
        self.log = []
        self.task = task
        self.task_item = task_item

    def write_log(self, message):
        logger.error(message)
        self.log.append(f'[{datetime.now().strftime("%H:%M:%S")}][{self.task_item["tenant_code"]}]{message}')

    def run(self):
        if self.task_item.get('status') == 1:
            self.write_log('升级任务已完成，跳过执行')
            return
        self.create_bak()
        self.upgrade()

    def create_bak(self):
        self.write_log('开始备份')
        tables = ['dap_bi_dataset', 'dap_bi_dataset_tables_collection', 'dap_bi_dataset_filter']
        with get_db(self.task_item.get('tenant_code')) as db:
            bak_id = self.task.get('bak_id')
            for table in tables:
                bak_table_name = f'{table}_bak_{bak_id}'
                sql = f'create table if not exists {bak_table_name} as select * from {table}'
                self.write_log(sql)
                db.exec_sql(sql)

        self.write_log('备份完成')

    def prepare_upgrade_data(self):
        self.write_log("开始准备要升级的数据")
        with get_db(self.task_item.get('tenant_code')) as db:
            datasets = db.query("select * from dap_bi_dataset where edit_mode='relation'")
            if len(datasets) == 0:
                self.write_log('没有需要升级的数据集')
                return []
            self.write_log(f"准备升级的数据集数量:{len(datasets)}")
            dataset_id_list = [dataset.get('id') for dataset in datasets]
            all_dataset_fields = db.query('select * from dap_bi_dataset_field where dataset_id in %(dataset_id)s', {
                'dataset_id': dataset_id_list,
            })
            all_dataset_tables_collections = db.query(
                'select * from dap_bi_dataset_tables_collection where dataset_id in %(dataset_id)s', {
                    'dataset_id': dataset_id_list,
                })
            all_dataset_filter = db.query('select * from dap_bi_dataset_filter where dataset_id in %(dataset_id)s', {
                'dataset_id': dataset_id_list,
            })

        insert_data = {
            'dap_bi_dataset_graph_table': [],
            'dap_bi_dataset_graph_table_relation': [],
            'dap_bi_dataset_graph_filter': [],
        }
        for dataset in datasets:
            self.write_log(f'数据集[{dataset.get("id")}:{dataset.get("name")}]开始升级元数据')
            dataset_tables_collections = [i for i in all_dataset_tables_collections if
                                          i.get('dataset_id') == dataset.get('id')]

            dataset_filter = next((i for i in all_dataset_filter if
                                   i.get('dataset_id') == dataset.get('id')), None)

            dataset_fields = [i for i in all_dataset_fields if
                              i.get('dataset_id') == dataset.get('id')]

            graph_content = graph_upgrade_helper.build_graph_content(dataset, dataset_fields,
                                                                     dataset_tables_collections,
                                                                     dataset_filter)

            insert_data.get('dap_bi_dataset_graph_table').extend(graph_content.get('node'))
            insert_data.get('dap_bi_dataset_graph_table_relation').extend(graph_content.get('link'))
            filter_data = graph_upgrade_helper.build_graph_filter_data(dataset.get('id'), graph_content, dataset_filter)
            if filter_data:
                insert_data.get('dap_bi_dataset_graph_filter').append(filter_data)

            self.write_log(f'数据集[{dataset.get("id")}:{dataset.get("name")}]元数据升级完成')
        return insert_data

    def upgrade(self):
        insert_data = self.prepare_upgrade_data()
        if insert_data and len(insert_data) > 0:
            self.write_log('开始写入升级数据')
            with get_db(self.task_item.get('tenant_code')) as db:
                for table, items in insert_data.items():
                    if len(items) > 0:
                        # 为了兼容有脏数据的场景也能插入成功，采用先删后插
                        rows = db.exec_sql(f"delete from {table} where dataset_id in %(dataset_id)s", {
                            'dataset_id': [item.get('dataset_id') for item in items]
                        }, commit=False)
                        self.write_log(f'{table}删除脏数据，影响行数:{rows}')
                        rows = db.insert_multi_data(table, items, list(items[0].keys()), commit=False)
                        self.write_log(f'{table}写入完成，影响行数:{rows}')
                rows = db.exec_sql(
                    "update dap_bi_dataset set edit_mode='graph',is_import_table=0 where edit_mode='relation'",
                    commit=False)
                self.write_log(f'刷新edit_mode和is_import_table，影响行数:{rows}')
                rows = db.exec_sql("delete from dap_bi_dataset_tables_collection where 1=1", commit=False)
                self.write_log(f'清理dap_bi_dataset_tables_collection表，影响行数:{rows}')
                rows = db.exec_sql("delete from dap_bi_dataset_filter where 1=1", commit=False)
                self.write_log(f'清理dap_bi_dataset_filter表，影响行数:{rows}')
                db.commit()
