#!/usr/local/bin python3
# -*- coding: utf-8 -*-
#

from .base import SceneUpgradeBase


class ParamsRedirectSceneUpgrade(SceneUpgradeBase):
    scene = '升级参数传参字段（起跳）'

    """
    跳转传参数：升级参数传参字段
    """

    def do(self):  # NOSONAR

        # 准备数据
        all_chart_params_jumps = []
        for from_chart_data in self.from_chart_datas:
            chart_params_jumps = self.loads(from_chart_data.get('chart_params_jump') or '[]')
            all_chart_params_jumps.extend(chart_params_jumps)
        all_chart_filters = []
        for from_chart_data in self.from_chart_datas:
            chart_filters = self.loads(from_chart_data.get('chart_filter') or '[]')
            all_chart_filters.extend(chart_filters)

        # def get_chart_filters_by_ref_dataset_filed_id(dataset_filed_id):
        #     for chart_filter in all_chart_filters:
        #         for related_list in chart_filter.get('related_list', []):
        #             if related_list['field_responder_id'] == dataset_filed_id:
        #                 return chart_filter['chart_initiator_id']
        #     return ''

        def get_chart_filter_by_ref_dataset_filed_id(chart_responder_id, dataset_filed_id):
            copy_all_chart_filters = all_chart_filters.copy()
            for chart_filter in copy_all_chart_filters:
                indicator_dim_obj = self.loads(chart_filter.get('indicator_dim_obj') or '{}')
                indicator_dim_id = indicator_dim_obj.get('id', '')
                for related_list in chart_filter.get('related_list', []):
                    # 处理同数据集
                    # 如果值字段存在，同数据集真正作用的字段是字段，需要将原有的关系替换
                    same_dataset_flag = related_list.get('is_same_dataset')
                    if same_dataset_flag and indicator_dim_id:
                        related_list['field_responder_id'] = indicator_dim_id
                    if related_list['field_responder_id'] == dataset_filed_id \
                            and related_list['chart_responder_id'] == chart_responder_id:
                        return chart_filter
            return {}

        # 遍历jump_config
        for from_chart_data in self.from_chart_datas:
            chart_params_jumps = self.loads(from_chart_data.get('chart_params_jump') or '[]')
            for chart_params_jump in chart_params_jumps:
                # 参数接受了筛选器作为起跳
                self.deal_accept_filter_chart(
                    from_chart_data, chart_params_jump, get_chart_filter_by_ref_dataset_filed_id,
                    all_chart_filters
                )

                # 参数接受了组件过滤作为起跳
                self.deal_accept_chart_filter_with_filter_conditions(from_chart_data, chart_params_jump)

                # 参数接受了全局筛选参数作为起跳
                self.deal_accept_dashboard_filter(from_chart_data, chart_params_jump)

    def get_from_dashboard_filters(self):
        # 获取当前(起跳)报告的全局筛选配置
        # "dashboard_filters": [
        #     {
        #         "id": "678863c2-031d-11ed-8aab-ff7a85a8c3f2",
        #         "dashboard_id": "3a050cba-d567-b1e0-6c9f-ff6848105744",
        #         "operator": null,
        #         "col_value": null,
        #         "main_dataset_field_id": "39fcd3a3-ebcc-3b62-529e-5de862c0e1e1",
        #         "select_all_flag": "0",
        #         "operators": [
        #             {
        #                 "id": "7bfbae23-031d-11ed-8aab-ff7a85a8c3f2",
        #                 "operator": "=",
        #                 "col_value": "参数3",
        #                 "select_all_flag": 0
        #             }
        #         ],
        #         "main_dataset_id": "39fcd383-037f-7cf9-eadc-bc8126258602",
        #         "alias_name": "参数",
        #         "col_name": "CS_8039023338",
        #         "data_type": "字符串",
        #         "field_group": "维度",
        #         "type": "普通",
        #         "expression": null,
        #         "format": null,
        #         "filter_relations": [
        #             {
        #                 "id": "77971b8f-031d-11ed-8aab-ff7a85a8c3f2",
        #                 "dashboard_id": "3a050cba-d567-b1e0-6c9f-ff6848105744",
        #                 "main_dataset_field_id": "39fcd3a3-ebcc-3b62-529e-5de862c0e1e1",
        #                 "related_dataset_field_id": "39fcd3a2-f909-9693-bbff-f77753716f6e",
        #                 "related_dataset_id": "39fcd382-b1a9-c517-5aa7-a77540ec14c5"
        #             }
        #         ]
        #     }
        # ]
        from_dashboard_data = self.from_dashboard_datas[0]
        dashboard_filters = self.loads(from_dashboard_data.get('dashboard_filters') or '[]')
        return dashboard_filters

    def get_dashboard_filter_by_main_dataset_id(self, dashboard_filters, main_dataset_field_id):
        for dashboard_filter in dashboard_filters:
            if main_dataset_field_id in str(dashboard_filter):
                # if dashboard_filter['main_dataset_field_id'] == main_dataset_field_id:
                return dashboard_filter
        return {}

    def deal_accept_dashboard_filter(self, from_chart_data, chart_params_jump):
        # 参数接受全局参数配置跳转
        from_dashboard_filters = self.get_from_dashboard_filters()
        from_dashboard_id = from_chart_data['dashboard_id']
        dataset_field_id = chart_params_jump['param_dataset_field_id']
        relate_filter = self.get_dashboard_filter_by_main_dataset_id(from_dashboard_filters, dataset_field_id)
        target_key, from_key = self.get_jump_relation_key(chart_params_jump)

        def deal_params_accept_dashboard_filter_without_filter_conditions():
            # 配置了参数跳转，而且参数字段被设置接受全局筛选
            # 没有配置筛选条件, # 参数字段被设置接受全局筛选
            if (not relate_filter.get('operators')) and relate_filter and dataset_field_id in str(relate_filter):
                # if relate_filter and dataset_field_id in str(relate_filter):
                # dataset_field_col_name = relate_filter['col_name']
                # A->B->C
                # 处理起跳升级全局参数的生成一致的名字
                result = []
                global_param = self.get_global_params_id_by_alias_name(from_dashboard_id, relate_filter['alias_name'])
                if global_param:
                    from_global_param_name, from_global_param_id = global_param['name'], global_param['id']
                else:
                    n_target_key = self.get_global_param_by_dashboard_filter_id(dashboard_filter_id=relate_filter['id'])
                    from_global_param_name, from_global_param_id = self.generate_global_param_name(
                        '', from_dashboard_id, n_target_key
                    )
                # 处理起跳报告的全局参数
                trans_global_params = {
                    "id": from_global_param_id,
                    "dashboard_id": from_dashboard_id,
                    "name": from_global_param_name,
                    "alias_name": relate_filter['alias_name'],
                }
                self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
                self.merge_to_global_params(from_dashboard_id, trans_global_params)

                jump_config = self.get_chart_jump_config(chart_params_jump)
                to_global_params_id = self.get_global_params_id_by_target_key(target_key)

                if not jump_config:
                    return

                # 升级成全局参数-->全局参数的跳转
                global_params_redirect_dict = {
                    'jump_config_id': jump_config['id'],
                    'dashboard_id': from_dashboard_id,
                    'dashboard_chart_id': from_chart_data['id'],
                    'initiator_global_params_id': from_global_param_id,
                    'global_params_id': to_global_params_id,
                }
                result.append(global_params_redirect_dict)
                self.merge_to_jump_config_v2({
                    'global_params_jump_relation': result
                })

        def deal_params_accept_dashboard_filter_with_filter_conditions():
            # 参数接受全局筛选，而且全局筛选配置了过滤条件
            if (relate_filter.get('operators')) and relate_filter and dataset_field_id in str(relate_filter):
                result = []
                jump_config = self.get_chart_jump_config(chart_params_jump)
                if not jump_config:
                    # 参数跳转找不到值
                    return
                # 全局筛选过滤器的特殊标记
                filter_chart_id = '00000000-0000-0000-0000-000000000002'
                date_filter_chart_flag = ''
                filter_dataset_field_id = relate_filter['main_dataset_field_id']
                # 原有的参数跳转升级后的全局参数id
                global_params_id = self.get_global_params_id_by_target_key(target_key) or \
                                   self.get_global_params_id_by_target_key(f'{from_key}-{target_key}') \
                                   or chart_params_jump.get('global_params_id', '')

                if global_params_id:
                    trans_map = {
                        "jump_config_id": jump_config['id'],
                        "dashboard_id": from_dashboard_id,
                        "dashboard_chart_id": from_chart_data['id'],
                        "filter_chart_id": filter_chart_id,
                        "dataset_field_id": filter_dataset_field_id,
                        "date_filter_chart_flag": date_filter_chart_flag,
                        "global_params_id": global_params_id,
                    }
                    result.append(trans_map)
                else:
                    self.logging(f'参数历史数据升级场景遗漏，没有生成对应的global_params_id，config: {chart_params_jump}')

                self.merge_to_jump_config_v2({
                    'filter_chart_jump_relation': result
                })

        deal_params_accept_dashboard_filter_without_filter_conditions()
        deal_params_accept_dashboard_filter_with_filter_conditions()

    def deal_accept_chart_filter_with_filter_conditions(
            self,
            from_chart_data, chart_params_jump
    ):
        # [
        #     {
        #         "dashboard_chart_id": "3a05ff98-c126-d09c-b325-f7147d848170",
        #         "operator": null,
        #         "col_value": null,
        #         "dataset_field_id": "39fcd382-e27d-7d88-fc68-b7ac3ab33859",
        #         "dataset_id": "39fcd382-b1a9-c517-5aa7-a77540ec14c5",
        #         "filter_relation": 0,
        #         "filter_id": "abc35cea-282d-11ed-adb3-515e1db651da",
        #         "alias_name": "项目",
        #         "field_group": "维度",
        #         "data_type": "字符串",
        #         "col_name": "XM_6260761125",
        #         "expression": null,
        #         "type": "普通",
        #         "visible": 1,
        #         "operators": [
        #             {
        #                 "id": "ace3e811-282d-11ed-adb3-515e1db651da",
        #                 "operator": "=",
        #                 "col_value": "B"
        #             }
        #         ]
        #     }
        # ]
        # 说明这个参数被配置了跳转
        dataset_field_id = chart_params_jump['param_dataset_field_id']
        chart_filters = self.loads(from_chart_data.get('filters') or '[]')
        if dataset_field_id not in str(chart_filters):
            # 没有接受筛选条件传值
            return

        result = []
        filter_chart_id = '00000000-0000-0000-0000-000000000001'
        date_filter_chart_flag = ''
        filter_dataset_field_id = dataset_field_id
        dashboard_id = from_chart_data['dashboard_id']
        target_key, from_key = self.get_jump_relation_key(chart_params_jump)
        jump_config = self.get_chart_jump_config(chart_params_jump)
        if not jump_config:
            # 参数跳转找不到值
            return

        global_params_id = self.get_global_params_id_by_target_key(target_key) or \
                           self.get_global_params_id_by_target_key(f'{from_key}-{target_key}') \
                           or chart_params_jump.get('global_params_id', '')

        if global_params_id:
            trans_map = {
                "jump_config_id": jump_config['id'],
                "dashboard_id": dashboard_id,
                "dashboard_chart_id": from_chart_data['id'],
                "filter_chart_id": filter_chart_id,
                "dataset_field_id": filter_dataset_field_id,
                "date_filter_chart_flag": date_filter_chart_flag,
                "global_params_id": global_params_id,
            }
            result.append(trans_map)
        else:
            self.logging(f'参数历史数据升级场景遗漏，没有生成对应的global_params_id，config: {chart_params_jump}')

        self.merge_to_jump_config_v2({
            'filter_chart_jump_relation': result
        })

    def deal_accept_filter_chart(
            self,
            from_chart_data, chart_params_jump,
            get_chart_filter_by_ref_dataset_filed_id,
            all_chart_filters
    ):
        # 说明这个参数被配置了跳转
        dataset_field_id = chart_params_jump['param_dataset_field_id']

        if dataset_field_id not in str(all_chart_filters):
            # 没有接受筛选器传值
            return

        result = []
        # 接受了筛选器传值
        chart_responder_id = chart_params_jump['dashboard_chart_id']
        chart_filter = get_chart_filter_by_ref_dataset_filed_id(chart_responder_id, dataset_field_id)
        if not chart_filter:
            return
        filter_chart_id = chart_filter['chart_initiator_id']
        filter_dataset_field_id = chart_filter['field_initiator_id']
        dashboard_id = self.from_dashboard_datas[0]['id']
        target_key, from_key = self.get_jump_relation_key(chart_params_jump)
        jump_config = self.get_chart_jump_config(chart_params_jump)
        if not jump_config:
            # 参数跳转找不到值
            return

        date_filter_chart_flag = self.get_date_filter_chart_flag_by_chart_id(filter_chart_id)

        global_params_id = self.get_global_params_id_by_target_key(target_key) or \
                           self.get_global_params_id_by_target_key(f'{from_key}-{target_key}') \
                           or chart_params_jump.get('global_params_id', '')

        if date_filter_chart_flag == 'date':
            filter_dataset_field_id = ''
        if global_params_id:
            trans_map = {
                "jump_config_id": jump_config['id'],
                "dashboard_id": dashboard_id,
                "dashboard_chart_id": from_chart_data['id'],
                "filter_chart_id": filter_chart_id,
                "dataset_field_id": filter_dataset_field_id,
                "date_filter_chart_flag": date_filter_chart_flag,
                "global_params_id": global_params_id,
            }
            result.append(trans_map)
        else:
            self.logging(f'参数历史数据升级场景遗漏，没有生成对应的global_params_id，config: {chart_params_jump}')

        self.merge_to_jump_config_v2({
            'filter_chart_jump_relation': result
        })
