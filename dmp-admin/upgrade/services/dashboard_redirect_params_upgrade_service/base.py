#!/usr/local/bin python3
# -*- coding: utf-8 -*-
#
import copy
# import datetime
import json
import sys
import traceback
from hashlib import md5
import logging

from base.errors import UserError
from dmplib.utils import strings
# from dmplib.hug import g
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.utils.strings import seq_id
from components.dmp_api import DMPAPI
from issue_dashboard.constants import get_condition_fields

logger = logging.getLogger(__name__)


class Base:
    scene = '基类'

    def __init__(self, tenant_code, upgrade_mode):
        self.tenant_code = tenant_code
        self.upgrade_mode = upgrade_mode

    def logging(self, c):
        # TODO 后续添加格式
        from_dashboard_id = self.from_dashboard_id if hasattr(self, 'from_dashboard_id') else ''
        info = f'----->>>>>>[{from_dashboard_id}{self.scene}][{self.upgrade_mode}] {c}'
        logger.error(info)
    #     self._record_log(info)
    #
    # def _record_log(self, info):
    #     upgrade_model = getattr(g, 'upgrade_model', None)
    #     if not upgrade_model:
    #         return
    #     path = f'/tmp/{upgrade_model.id}.log'
    #     now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    #     info = f"[{now}] {info}"
    #     with open(path, 'a') as f:
    #         f.write(info + '\n')

    def logging_func(self, c):
        called_func = sys._getframe(1).f_code.co_name  # noqa
        self.logging(f'[{called_func}] {c}')

    def loads(self, c):
        return json.loads(c)

    def dumps(self, c):
        return json.dumps(c)

    def get_data(self, table_name, conditions, fields, multi_row=None, order_by=None):
        """
        获取表数据
        """
        sql = 'SELECT {col} FROM {table_name} ' \
              ''.format(col='`' + '`,`'.join(fields) + '`' if fields else '*',
                        table_name=table_name)
        if conditions and isinstance(conditions, dict):
            sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
        if order_by and isinstance(order_by, list):
            sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
        with get_tenant_db(self.tenant_code) as db:
            if multi_row:
                return db.query(sql, conditions) or []
            else:
                sql += ' LIMIT 1 '
                return db.query_one(sql, conditions) or {}

    def add_data(self, table_name, data, commit=False):
        """
        添加数据
        """
        if not data:
            return False
        with get_tenant_db(self.tenant_code) as db:
            return db.insert(table_name, data, commit=commit) == 1

    def update_data(self, table_name, data, condition, commit=False, with_none=False):
        """
        更新数据
        """
        if not data:
            return 0
        with get_tenant_db(self.tenant_code) as db:
            return db.update(table_name, data, condition, commit=commit, with_none=with_none)

    def exec_sql(self, sql, params=None, commit=False):
        with get_tenant_db(self.tenant_code) as db:
            affect_row = db._execute(sql, params).rowcount
            if not db.transaction and commit:
                db.commit()
            return affect_row

    def is_table_exist(self, table_name):
        try:
            self.get_data(table_name, conditions={}, fields=[], multi_row=False)
            return True
        except Exception as e:
            # '(1146, "Table \'dmp_test0613.dashboard_released_snapshot_dashboard_redirect_upgrade_backup\' doesn\'t exist")'
            if '1146' in str(e):
                return False
            else:
                self.logging(f'意外的情况：{traceback.format_exc()}')
                return False

    # def get_data_by_sql(self, sql, params, pagination=None):
    #     """
    #     根据sql语句获取数据
    #     """
    #     if not sql:
    #         raise ValueError('sql not allow empty')
    #     with get_tenant_db(self.tenant_code) as db:
    #         if pagination is not None:
    #             sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
    #         return db.query(sql, params) or []

    def replace_multi_data(self, table, list_data, fields, commit=False, condition_field=None):

        """
        可重复添加数据
        """
        with get_tenant_db(self.tenant_code) as db:
            return db.replace_multi_data(table, list_data, fields, commit=commit, condition_field=condition_field) == 1

    def get_released_data(self, dashboard_id):
        if self.upgrade_mode == 'unreleased':
            result = DMPAPI(self.tenant_code).get_dashboard_released_data(dashboard_id)

            # debug
            # from dmplib.redis import RedisCache
            # conn = RedisCache()
            # key = f'{self.upgrade_mode}-{dashboard_id}'
            # cache_data = conn.get(key)
            # if cache_data:
            #     result = self.loads(cache_data)
            # else:
            #     result = DMPAPI(self.tenant_code).get_dashboard_released_data(dashboard_id)
            #     conn.set(key, self.dumps(result), time=24*60*60*60)

            data = result.get('data', {})
            if not data:
                self.logging(f'[{dashboard_id}]获取运行时数据失败: {result}')
                raise UserError(message=f'[{dashboard_id}]获取运行时数据失败, 退出当前报告的升级: {result.get("msg")}')
            dashboard_data = data.get(dashboard_id, {}).get('dashboard', [])
            chart_data = data.get(dashboard_id, {}).get('chart', [])
            return dashboard_data, chart_data
        else:
            dashboard_data = self.get_data(
                'dap_bi_dashboard_released_snapshot_dashboard', conditions={'id': dashboard_id}, fields=[], multi_row=True
            )
            chart_data = self.get_data(
                'dap_bi_dashboard_released_snapshot_chart', conditions={'dashboard_id': dashboard_id}, fields=[],
                multi_row=True
            )
            return dashboard_data, chart_data

    def update_jump_config_global_param(self, table_name, jump_config, commit=False):
        jump_config_copy = copy.deepcopy(jump_config)
        activate_data = self._get_relation_import_data(jump_config_copy)
        if table_name in ['dap_bi_dashboard_filter_chart_jump_relation', 'dap_bi_dashboard_global_params_jump_relation']:
            # with get_tenant_db(self.tenant_code) as db:
            #     list_data = [jump_config]
            #     return db.replace_multi_data(table_name, list_data, list(jump_config.keys()), commit=commit) == 1
            exist = self.get_data(table_name, conditions=activate_data, fields=[], multi_row=False)
            if not exist:
                self.add_data(table_name, jump_config_copy)
        else:
            global_param_id = jump_config_copy.pop('global_params_id', '')
            self.update_data(
                table_name,
                data={'global_params_id': global_param_id},
                condition=jump_config_copy,
                commit=commit
            )

    def _get_relation_import_data(self, data):
        fields = ['created_on', 'created_by', 'modified_on', 'modified_by']
        return {key: val for key, val in data.items() if key not in fields}

    def get_all_chart_2_dateset_id_map(self, chart_data) -> dict:
        # 从json中解析出组件id与数据集的idmap
        result = {}
        for data in chart_data:
            if data['id'] not in result:
                result[data['id']] = set()
            result[data['id']].add(data['source'])
        return result

    def get_dashboard_all_chart_jump_config(self, chart_datas) -> list:
        # 从json中解析出组件所有的jump_config
        result = []
        for chart_data in chart_datas:
            result.extend(self.loads(chart_data.get('jump', '[]')))
        return result

    def get_dashboard_all_chart_jump_relations(self, chart_data) -> list:
        # 从json中解析出组件所有的jump_config中的relations
        result = []
        configs = self.get_dashboard_all_chart_jump_config(chart_data)
        for config in configs:
            result.extend(self._wrap_flag(config, 'jump_relation'))
            result.extend(self._wrap_flag(config, 'var_jump_relation'))
            result.extend(self._wrap_flag(config, 'fixed_var_jump_relation'))
        for data in chart_data:
            result.extend(self.loads(data.get('chart_params_jump') or '[]'))
        return result

    def _wrap_flag(self, config, key):
        datas = config.get(key, [])
        # for data in datas:
        #     setattr(data, '__group_key__', key)
        return datas

    def get_all_to_dashboard_ids(self, chart_data) -> set:
        configs = self.get_dashboard_all_chart_jump_config(chart_data)
        return {config['target'] for config in configs if config['target_type'] == 'dashboard'}


class SceneUpgradeBase(Base):
    scene = '场景升级基类'

    def __init__(self,  # NOSONAR
                 from_dashboard_id, from_dashboard_datas, to_dashboard_ids, from_chart_datas, tenant_code,  # NOSONAR
                 upgrade_mode, to_dashboard_datas, to_chart_datas, extra_data):  # NOSONAR
        self.from_dashboard_id = from_dashboard_id
        self.to_dashboard_ids = to_dashboard_ids
        self.from_dashboard_datas = from_dashboard_datas
        self.from_chart_datas = from_chart_datas
        self.to_dashboard_datas = to_dashboard_datas
        self.to_chart_datas = to_chart_datas
        self.tenant_code = tenant_code
        self.extra_data = extra_data
        super().__init__(tenant_code=tenant_code, upgrade_mode=upgrade_mode)
        self.pre_load_target_dashboard_global_params()

    def do(self):  # NOSONAR
        raise NotImplemented  # NOSONAR

    def after_do(self, from_dashboard_datas, from_chart_datas, to_dashboard_datas, to_chart_datas):
        def processing_unreleased():
            # 处理设计时数据
            self.deal_global_params(to_dashboard_datas, from_dashboard_datas)
            self.deal_from_dashboard_jump(from_chart_datas)

        def processing_released():
            # 处理运行时数据
            self.deal_from_dashboard_jump(from_chart_datas)
            self.deal_released_data(from_dashboard_datas, from_chart_datas, to_dashboard_datas, to_chart_datas)

        process_mapping = {
            'unreleased': processing_unreleased,
            'released': processing_released
        }
        if self.upgrade_mode not in process_mapping:
            self.logging(f'升级模式[{self.upgrade_mode}]错误，不会完成数据处理！')
            return

        process_mapping[self.upgrade_mode]()

    def deal_released_data(self, from_dashboard_datas, from_chart_datas, to_dashboard_datas, to_chart_datas):
        # 目前只有发布表需要替换数据，跳转表不需要处理
        # 需要区分场景，起跳还是被跳
        # 起跳升级组件数据，被跳升级报告数据
        dashboard_datas = [one for val in to_dashboard_datas.values() for one in val]
        chart_datas = from_chart_datas

        for dashboard_data in dashboard_datas:
            self._replace_released_data(dashboard_data, 'dap_bi_dashboard_released_snapshot_dashboard')

        for chart_data in chart_datas:
            self._replace_released_data(chart_data, 'dap_bi_dashboard_released_snapshot_chart')

    def _replace_released_data(self, data: dict, table_name):
        increment_id = data.pop('increment_id', '')
        self.update_data(table_name, condition={'increment_id': increment_id}, data=data, commit=False)

    def deal_global_params(self, to_dashboard_datas: dict, from_dashboard_datas):
        # 处理目标报告的全局参数
        all_dashboards = [*[data for datas in to_dashboard_datas.values() for data in datas], *from_dashboard_datas]
        # for dashboards in all_dashboards:
        for dashboard in all_dashboards:
            global_params = self.loads(dashboard.get('global_params') or '[]')
            for global_param in global_params:
                # 1. 添加全局参数
                global_param_id = global_param['id']
                global_param_name = global_param['name']
                alias_name = global_param.get('alias_name', '')
                dashboard_id = global_param['dashboard_id']
                exist = self.get_global_param_limit1_by_sql(global_param_name, dashboard_id)
                if not exist:
                    self.add_data(
                        'dap_bi_dashboard_jump_global_params',
                        data={
                            'name': global_param_name,
                            'alias_name': alias_name,
                            'dashboard_id': dashboard_id,
                            'id': global_param_id
                        },
                        commit=False
                    )
                # 2. 添加全局参数关联表关系
                dataset_field_relations = global_param.get('dataset_field_relations', [])
                dataset_vars_relations = global_param.get('dataset_vars_relations', [])
                filter_chart_relations = global_param.get('filter_chart_relations', [])
                self._deal_relations(dataset_field_relations, 'dap_bi_dashboard_global_params_2_dataset_field_relation')
                self._deal_relations(dataset_vars_relations, 'dap_bi_dashboard_global_params_2_dataset_vars_relation')
                self._deal_relations(filter_chart_relations, 'dap_bi_dashboard_global_params_2_filter_chart_relation')

    def _deal_relations(self, relations, table_name):
        # for relation in relations:
        #     exist = self.get_data(table_name, conditions=relation, multi_row=False, fields=['global_params_id'])
        #     if not exist:
        #         self.add_data(table_name, data=relation, commit=False)
        if relations:
            self.replace_multi_data(table_name, relations, fields=list(relations[0].keys()), commit=False, condition_field=get_condition_fields(table_name))

    def deal_from_dashboard_jump(self, from_chart_datas):
        # jump_configs = self.get_dashboard_all_chart_jump_config(from_chart_datas)
        for chart_data in from_chart_datas:
            jump_configs = self.loads(chart_data.get('jump') or '[]')
            for jump_config in jump_configs:
                jump_relations = jump_config.get('jump_relation', [])
                var_jump_relations = jump_config.get('var_jump_relation', [])
                fixed_var_jump_relations = jump_config.get('fixed_var_jump_relation', [])
                filer_chart_jump_relations = jump_config.get('filter_chart_jump_relation', [])
                global_params_jump_relation = jump_config.get('global_params_jump_relation', [])
                self._deal_jump_relations(jump_relations, 'dap_bi_dashboard_jump_relation')
                self._deal_jump_relations(var_jump_relations, 'dap_bi_dashboard_vars_jump_relation')
                self._deal_jump_relations(fixed_var_jump_relations, 'dap_bi_dashboard_fixed_var_jump_relation')
                self._deal_jump_relations(filer_chart_jump_relations, 'dap_bi_dashboard_filter_chart_jump_relation')
                self._deal_jump_relations(global_params_jump_relation, 'dap_bi_dashboard_global_params_jump_relation')
            chart_data['jump'] = self.dumps(jump_configs)

    def _deal_jump_relations(self, jump_relations, table_name):
        if self.upgrade_mode == 'unreleased':
            for jump_relation in jump_relations:
                has_upgraded = jump_relation.pop('__has_upgraded__', None)
                if has_upgraded:
                    self.update_jump_config_global_param(table_name, jump_relation)
        elif self.upgrade_mode == 'released':
            for jump_relation in jump_relations:
                global_param = self.get_global_param_limit1_json_by_id(jump_relation.get('global_params_id'))
                if global_param:
                    jump_relation['global_params_name'] = global_param.get('name', '')
                    jump_relation['global_params_alias_name'] = global_param.get('alias_name', '')
                else:
                    self.logging(f'没有通过global_params_id取到全局参数信息！')

    def generate_new_col_name(self, dataset_id, col_name, table_name=None):
        """
        生成新的字段名（中文会生成以首字母的拼音），来自dmp
        """
        if table_name:
            return "{}_{}".format(
                strings.get_first_pinyin_hanzi(col_name), strings.fletcher32(table_name + dataset_id + ":" + col_name)
            )
        else:
            return "{}_{}".format(
                strings.get_first_pinyin_hanzi(col_name), strings.fletcher32(dataset_id + ":" + col_name)
            )

    def pre_load_target_dashboard_global_params(self):
        last = self.extra_data.get('global_params', {})
        if not last:
            self.extra_data['global_params'] = {}
            for to_dashboard_id, dashboard_datas, in self.to_dashboard_datas.items():
                self.extra_data['global_params'][to_dashboard_id] = self.loads(
                    dashboard_datas[0].get('global_params') or '[]'
                )
            # 起跳报告的全局参数
            if self.from_dashboard_datas:
                from_dashboard_data = self.from_dashboard_datas[0]
                self.extra_data['global_params'][from_dashboard_data['id']] = self.loads(
                    from_dashboard_data.get('global_params') or '[]'
                )

    def get_global_param_limit1_by_json(self, global_param_name, dashboard_id):
        global_params = self.extra_data['global_params'][dashboard_id]  # list
        for global_param in global_params:
            if global_param['name'] == global_param_name and global_param['dashboard_id'] == dashboard_id:
                return global_param
        return {}

    def get_global_param_limit1_json_by_id(self, global_params_id):
        global_params = [one for val in self.extra_data['global_params'].values() for one in val]
        global_params = {i.get('id'): i for i in global_params}
        if global_params_id in global_params:
            return global_params[global_params_id]
        return {}

    def get_global_param_limit1_by_sql(self, global_param_name, dashboard_id):
        return self.get_data(
            'dap_bi_dashboard_jump_global_params',
            conditions={'name': global_param_name, 'dashboard_id': dashboard_id},
            multi_row=False, fields=[]
        )

    # def generate_global_param_name_with_no_ass(self, dashboard_id, dataset_id, name):
    #     times = 10
    #     global_param_name = self.generate_global_param_name(dataset_id, name)
    #     global_param_names = [global_param_name]
    #     global_param_names.extend([f'{global_param_name}_{i}' for i in range(1, 1 + times)])
    #
    #     for global_param_name in global_param_names:
    #         exists = self.get_global_param_limit1(global_param_name, dashboard_id)
    #         if not exists:
    #             global_param_id = seq_id()
    #             self.add_data(
    #                 'dashboard_jump_global_params',
    #                 data={'name': global_param_name, 'dashboard_id': dashboard_id, 'id': global_param_id},
    #                 commit=False
    #             )
    #             return global_param_name, global_param_id
    #     raise UserError(message=f'在尝试{times}后没有生成合适全局参数名')

    def generate_global_param_name(self, global_param_name, dashboard_id, global_id=None):
        # global_param_name 务必使用一个能确定唯一的名字
        if not global_param_name and global_id:
            # 没有传名字，就用一个默认名字
            global_param_name = self.generate_new_col_name(global_id, 'CS')
        global_param = self.get_global_param_limit1_by_json(global_param_name, dashboard_id)
        if not global_param:
            global_param_id = seq_id()
            # 节点中加一个
            self.extra_data['global_params'][dashboard_id].append(
                {
                    "id": global_param_id,
                    "dashboard_id": dashboard_id,
                    "name": global_param_name,
                    "dataset_field_relations": [],
                    "dataset_vars_relations": [],
                    "filter_chart_relations": []
                }
            )
            self.update_target_key_2_global_params_map(global_id, global_param_id)
            return global_param_name, global_param_id
        self.update_target_key_2_global_params_map(global_id, global_param['id'])
        return global_param['name'], global_param['id']

    def update_target_key_2_global_params_map(self, target_key, global_param_id):
        # 更新key与全局参数的map
        target_key_2_global_params_map = self.extra_data.get('target_key_2_global_params_map', {})
        if not target_key_2_global_params_map:
            self.extra_data['target_key_2_global_params_map'] = {}
        target_key_2_global_params_map[target_key] = global_param_id
        self.extra_data['target_key_2_global_params_map'] = target_key_2_global_params_map

    def get_global_params_id_by_target_key(self, target_key):
        return self.extra_data.get('target_key_2_global_params_map', {}).get(target_key, '')

    def get_global_params_id_by_alias_name(self, dashboard_id, alias_name):
        global_params = self.extra_data['global_params'].get(dashboard_id) or []  # list
        for global_param in global_params:
            if global_param['alias_name'] == alias_name and global_param['dashboard_id'] == dashboard_id:
                return global_param
        return {}

    def update_target_key_2_global_params_with_none(self, relation, global_param_id):
        target_key, from_key = self.get_jump_relation_key(relation)
        key = f'{from_key}-{target_key}'
        return self.update_target_key_2_global_params_map(key, global_param_id)

    def merge_to_global_params(self, dashboard_id, new_global_params: dict):
        # 将新的全局参数合到json里面
        all_global_params = self.extra_data['global_params'][dashboard_id]  # type: list
        all_global_params_ids = {i['id'] for i in all_global_params}
        if new_global_params['id'] not in all_global_params_ids:
            all_global_params.append(new_global_params)
        else:
            for global_param in all_global_params:
                if global_param['id'] == new_global_params['id']:
                    # 更新节点信息
                    global_param['alias_name'] = new_global_params.get('alias_name', '')
                    dataset_field_relations = global_param.get('dataset_field_relations', [])
                    new_dataset_field_relations = new_global_params.get('dataset_field_relations', [])
                    dataset_vars_relations = global_param.get('dataset_vars_relations', [])
                    new_dataset_vars_relations = new_global_params.get('dataset_vars_relations', [])
                    filter_chart_relations = global_param.get('filter_chart_relations', [])
                    new_filter_chart_relations = new_global_params.get('filter_chart_relations', [])
                    self._merge_relations(dataset_field_relations, new_dataset_field_relations)
                    self._merge_relations(dataset_vars_relations, new_dataset_vars_relations)
                    self._merge_relations(filter_chart_relations, new_filter_chart_relations)

        # 回填目标报告的群居参数信息
        all_global_params = self.extra_data['global_params'][dashboard_id]  # type: list
        if dashboard_id in self.to_dashboard_datas:
            self.to_dashboard_datas[dashboard_id][0]['global_params'] = self.dumps(all_global_params)
            self.to_dashboard_datas[dashboard_id][1]['global_params'] = self.dumps(all_global_params)
        else:
            # 来自起跳报告
            if self.from_dashboard_datas[0]['id'] == dashboard_id:
                self.from_dashboard_datas[0]['global_params'] = self.dumps(all_global_params)
                self.from_dashboard_datas[1]['global_params'] = self.dumps(all_global_params)
            else:
                raise RuntimeError(f'未考虑到的异常id: {dashboard_id}')

        # 回填目标报告的跳转关联关系
        self.merge_to_jump_config_v2()

    # def merge_to_jump_config_v2(self, from_dashboard_params_jumps=[]):
    def merge_to_jump_config_v2(self, extra_data={}):
        # extra_data: {
        # 'filter_chart_jump_relation': [],
        # 'global_params_jump_relation': [],
        # }
        # 把新跳转关系合并到没起跳报告中
        # 把所有组件的jump-config遍历，根据relation生成target_key去匹配global_params_id，然后自动替换global_params_id
        for chart_data in self.from_chart_datas:
            # 处理维度、变量、固定值跳转
            jumps = self.loads(chart_data.get('jump') or '[]')
            for jump in jumps:
                self._deal_jump_relation_map_v2(jump, 'jump_relation')
                self._deal_jump_relation_map_v2(jump, 'var_jump_relation')
                self._deal_jump_relation_map_v2(jump, 'fixed_var_jump_relation')
                # 处理参数起跳跳转
                self._deal_extra_relation_map_v2(jump, extra_data)

            # 回填jump数据
            chart_data['jump'] = self.dumps(jumps)

    def _deal_extra_relation_map_v2(self, jump, extra_data):
        # 单独指定合并数据，处理参数跳转传参的跳转关系
        for key, data in extra_data.items():
            for from_dashboard_jump in data:
                # if from_dashboard_jump['jump_config_id'] == jump['id']:
                from_dashboard_jump['__has_upgraded__'] = 1
                jump_relations = jump.get(key, [])
                jump_relations.append(from_dashboard_jump)
                jump[key] = jump_relations

    def _deal_jump_relation_map_v2(self, jump, key):
        # 根据relation生成target_key去匹配global_params_id，然后自动替换global_params_id
        jump_relations = jump.get(key, [])
        for jump_relation in jump_relations:
            jump_relation_copy = copy.deepcopy(jump_relation)
            jump_relation_copy.pop('global_params_id', None)
            target_key, from_key = self.get_jump_relation_key(relation=jump_relation_copy)
            global_params_id = self.get_global_params_id_by_target_key(target_key)
            if global_params_id:
                jump_relation['global_params_id'] = global_params_id
                jump_relation['__has_upgraded__'] = 1

    def _merge_relations(self, old_relations: list, new_relations: list):
        # getkey = lambda d: sorted(d.items(), key=lambda x: x[0], reverse=False)
        old_relations_keys = [self.getkey(o) for o in old_relations]
        for new_relation in new_relations:
            key = self.getkey(new_relation)
            if key not in old_relations_keys:
                old_relations.append(new_relation)

    def getkey(self, d: dict):
        return md5(str(sorted(d.items(), key=lambda x: x[0], reverse=False)).encode('utf-8')).hexdigest()

    def get_jump_relation_key(self, relation: dict):
        # 获取目标报告跳转的唯一key
        target_keys = ['dashboard_filter_id', 'relation_field_type', 'target_filter_field_id', 'target_filter_id']
        relation_copy_1 = copy.deepcopy(relation)
        relation_copy_1.pop('global_params_id', None)
        target_map = {key: val for key, val in relation_copy_1.items() if key in target_keys}
        target_key = self.getkey(target_map)
        from_map = {key: val for key, val in relation_copy_1.items() if key not in target_keys}
        from_key = self.getkey(from_map)
        return target_key, from_key

    def get_dataset_field_id_from_jump_relation(self, relation):
        # 参数跳转比较特殊，数据集字段名是param_dataset_field_id
        return relation.get('dataset_field_id', '') or relation.get('param_dataset_field_id', '')

    def get_date_filter_chart_flag_by_chart_id(self, chart_id):

        cache_key = 'cached_chart_info'
        cached_chart_info = self.extra_data.get(cache_key, {})
        if not cached_chart_info:
            self.extra_data[cache_key] = {}
        if chart_id not in cached_chart_info:
            chart_info = self.get_data(
                "dap_bi_dashboard_chart", {"id": chart_id}, fields=[], multi_row=False
            )
            self.extra_data[cache_key][chart_id] = chart_info
        else:
            chart_info = cached_chart_info[chart_id]

        if chart_info.get('chart_code') == 'date_interval_filter':
            date_filter_chart_flag = 'date'
        else:
            date_filter_chart_flag = ''
        return date_filter_chart_flag

    def get_chart_jump_config(self, chart_params_jump):
        dashboard_chart_id = chart_params_jump['dashboard_chart_id']
        source_id = chart_params_jump['source_id']

        cache_key = 'cached_jump_config'
        cache_flag = f'{dashboard_chart_id}-{source_id}'
        cached_chart_info = self.extra_data.get(cache_key, {})
        if not cached_chart_info:
            self.extra_data[cache_key] = {}
        if cache_flag not in cached_chart_info:
            jump_config = self.get_data(
                "dapp_bi_dashboard_jump_config",
                {"dashboard_chart_id": dashboard_chart_id, "source_id": source_id}, fields=[], multi_row=False
            )
            self.extra_data[cache_key][cache_flag] = jump_config
        else:
            jump_config = cached_chart_info[cache_flag]

        return jump_config

    def get_global_param_by_dashboard_filter_id(self, dashboard_filter_id):
        target_map = {
            'dashboard_filter_id': dashboard_filter_id,
            'relation_field_type': 1,
            'target_filter_field_id': '',
            'target_filter_id': '',
        }
        n_target_key = self.getkey(target_map)
        return n_target_key


class DashboardDirectParamsUpgrade(Base):
    scene = '升级入口'

    def __init__(self, from_dashboard_id, tenant_code, upgrade_mode):
        self.from_dashboard_id = from_dashboard_id
        # self.to_dashboard_ids = to_dashboard_ids
        self.tenant_code = tenant_code
        self.upgrade_mode = 'unreleased'  # released/unreleased  运行时/设计时
        self.data_op_tables = [
            'dap_bi_dashboard_released_snapshot_dashboard',
            'dap_bi_dashboard_released_snapshot_chart',
        ]
        self.data_op_tables_suffix = 'redirect_upgrade_backup'
        super().__init__(tenant_code=tenant_code, upgrade_mode=upgrade_mode)

    def backing_up_data(self):
        # 备份数据, 备份所有的设计涉及到的表
        # 只有发布表会覆盖替换旧数据，跳转关系表处理的是新字段，理论上不会有影响，先暂时只处理发布数据
        try:
            tables = self.data_op_tables
            suffix = self.data_op_tables_suffix
            with get_tenant_db(self.tenant_code) as db:
                db.begin_transaction()
                for table in tables:
                    backup_table = f'{table}_{suffix}'
                    if not self.is_table_exist(backup_table):
                        # 表不存在，创建备份表
                        sql = f'create table {backup_table} like {table}'
                        self.exec_sql(sql, commit=False)
                        sql = f'insert into {backup_table} select * from {table}'
                        self.exec_sql(sql, commit=False)
                db.commit()
        except Exception as e:
            self.logging(f'备份组件表失败，跳过备份：{str(e)}')

    def rolling_back_data(self):
        # 回滚备份的数据
        # 只回滚发布表
        tables = self.data_op_tables
        suffix = self.data_op_tables_suffix
        batch = 50
        with get_tenant_db(self.tenant_code) as db:
            db.begin_transaction()
            for table in tables:
                backup_table = f'{table}_{suffix}'
                if self.is_table_exist(backup_table):
                    datas = self.get_data(backup_table, conditions={}, fields=[], multi_row=True)
                    for idx, data in enumerate(datas):
                        increment_id = data.pop('increment_id', '')
                        self.update_data(
                            table,
                            condition={'increment_id': increment_id}, data=data, commit=False, with_none=True
                        )
                        if idx % batch == (batch - 1):
                            db.commit()
            db.commit()

    def do_upgrade(self):
        from . import SCENE_UPGRADERS

        # 因为只有一个报告,两个都是list
        # from_dashboard_datas: []
        # from_chart_datas: []
        from_dashboard_datas, from_chart_datas = self.get_released_data(self.from_dashboard_id)
        # 因为可能有多个报告，两个都是dict, key是目标报告id
        # to_dashboard_datas: {'id1': []}
        # to_chart_datas: {'id2': []}
        to_dashboard_datas, to_chart_datas = {}, {}
        to_dashboard_ids = self.get_all_to_dashboard_ids(from_chart_datas)
        not_exist_ids = set()
        for to_dashboard_id in to_dashboard_ids:
            try:
                to_dashboard_data, to_chart_data = self.get_released_data(to_dashboard_id)
                to_dashboard_datas[to_dashboard_id] = to_dashboard_data
                to_chart_datas[to_dashboard_id] = to_chart_data
            except UserError:
                not_exist_ids.add(to_dashboard_id)

        if not_exist_ids:
            raise UserError(message=f'存在不存在报告，退出升级！{",".join(not_exist_ids)}')

        extra_data = {}

        # 备份数据
        self.backing_up_data()

        # scene_upgraders = SceneUpgradeBase.__subclasses__()
        with get_tenant_db(self.tenant_code) as db:
            try:
                db.begin_transaction()
                # 处理数据
                for scene_cls in SCENE_UPGRADERS:
                    scene_obj = scene_cls(
                        from_dashboard_id=self.from_dashboard_id,
                        tenant_code=self.tenant_code,
                        to_dashboard_ids=to_dashboard_ids,
                        upgrade_mode=self.upgrade_mode,
                        from_dashboard_datas=from_dashboard_datas,
                        from_chart_datas=from_chart_datas,
                        to_dashboard_datas=to_dashboard_datas,
                        to_chart_datas=to_chart_datas,
                        extra_data=extra_data
                    )
                    scene_obj.do()

                # 完成数据处理，操作数据保存，数据加工
                scene_obj.after_do(
                    from_dashboard_datas=from_dashboard_datas,
                    from_chart_datas=from_chart_datas,
                    to_dashboard_datas=to_dashboard_datas,
                    to_chart_datas=to_chart_datas
                )
                db.conn and db.commit()
            except Exception as e:
                self.logging(f'当前报告升级失败，详细原因: {traceback.format_exc()}')
                db.conn and db.rollback()
                raise RuntimeError(f"当前报告升级失败，错误原因: {e}") from e
            finally:
                self.logging('结束当前报告升级')
                db.conn and db.rollback()
