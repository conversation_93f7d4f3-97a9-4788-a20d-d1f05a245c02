#!/usr/local/bin python3
# -*- coding: utf-8 -*-
#

from .base import SceneUpgradeBase


class DashboardFilterSceneUpgrade(SceneUpgradeBase):
    scene = '升级全局筛选（接参）'

    """
    跳转接受参数：升级全局筛选
    """

    def do(self):  # NOSONAR

        # 找到所有目标报告dashboard_filter_id与dashboard_id的关系map
        dashboard_filter_id_2_dashboard_id_map = {}
        for to_dashboard_data in self.to_dashboard_datas.values():
            to_dashboard_data = to_dashboard_data[0]
            dashboard_filters = self.loads(to_dashboard_data.get('dashboard_filters') or '[]')
            for dashboard_filter in dashboard_filters:
                dashboard_filter_id_2_dashboard_id_map[dashboard_filter['id']] = dashboard_filter

        # def get_dataset_id_from_filter_by_dataset_field_id(dataset_field_id):
        #     for dashboard_filter in dashboard_filter_id_2_dashboard_id_map.values():
        #         for filter_relation in dashboard_filter.get('filter_relations', []):
        #             if dataset_field_id == filter_relation['related_dataset_field_id']:
        #                 return filter_relation['related_dataset_id']
        #     return ''

        dataset_id_2_chart_map = {}
        for to_chart_data in self.to_chart_datas.values():
            for chart_data in to_chart_data:
                dataset_id = chart_data['source']
                if dataset_id not in dataset_id_2_chart_map:
                    dataset_id_2_chart_map[dataset_id] = set()
                dataset_id_2_chart_map[dataset_id].add(chart_data['id'])

        for from_chart_data in self.from_chart_datas:
            # 1. 取单组件所有的组件跳转
            chart_jump_relations = self.get_dashboard_all_chart_jump_relations([from_chart_data])
            for chart_jump_relation in chart_jump_relations:
                if chart_jump_relation.get('relation_field_type') != 1:
                    continue
                dashboard_filter_id = chart_jump_relation['dashboard_filter_id']
                if dashboard_filter_id:
                    dashboard_filter = dashboard_filter_id_2_dashboard_id_map.get(dashboard_filter_id)
                    if not dashboard_filter:
                        continue
                    to_dashboard_id = dashboard_filter['dashboard_id']
                    # dataset_field_id = chart_jump_relation['dataset_field_id']
                    # 起跳的字段id
                    # from_dataset_field_id = self.get_dataset_field_id_from_jump_relation(chart_jump_relation)
                    # dataset_field_info = self.get_dataset_field_info_by_dataset_filed_id(dataset_field_id)
                    # dataset_id = get_dataset_id_from_filter_by_dataset_field_id(from_dataset_field_id)
                    target_key, from_key = self.get_jump_relation_key(relation=chart_jump_relation)
                    global_param_name, global_param_id = self.generate_global_param_name(
                        '', to_dashboard_id, target_key
                    )
                    alias_name = dashboard_filter.get('alias_name', '')
                    # 全局筛选不同数据集的情况
                    for filter_relation in dashboard_filter.get('filter_relations', []):
                        to_dataset_field_id = filter_relation['related_dataset_field_id']
                        to_dataset_id = filter_relation['related_dataset_id']
                        # 处理全局参数
                        trans_global_params = {
                            "id": global_param_id,
                            "dashboard_id": to_dashboard_id,
                            "name": global_param_name,
                            "alias_name": alias_name,
                            "dataset_field_relations": [
                                {
                                    'global_params_id': global_param_id,
                                    'dashboard_id': to_dashboard_id,
                                    'chart_id': chart_id,
                                    'dataset_id': to_dataset_id,
                                    'dataset_field_id': to_dataset_field_id,
                                }
                                for chart_id in dataset_id_2_chart_map.get(to_dataset_id, [])
                            ],
                            # "dataset_vars_relations": [],
                            # "filter_chart_relations": []
                        }
                        self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
                        self.merge_to_global_params(to_dashboard_id, trans_global_params)

                    # 处理同数据集的情况
                    self.same_dataset(dashboard_filter, to_dashboard_id, global_param_id, global_param_name, alias_name)

                # elif dashboard_filter_id == '':
                #     #  配置是无的情况
                #     #  见下面
                #     pass

        self.deal_filter_opt_none()

    def same_dataset(self, dashboard_filter, to_dashboard_id, global_param_id, global_param_name, alias_name):
        # 处理同数据集的情况
        # 找到被跳报告所有引用了这个数据数据集的组件
        main_dataset_id = dashboard_filter['main_dataset_id']
        to_dataset_field_id = dashboard_filter['main_dataset_field_id']
        to_chart_data = self.to_chart_datas.get(to_dashboard_id, [])
        mps = [(c.get('id'), c.get('source')) for c in to_chart_data if c.get('source')]
        dataset_id_maps = []
        for chart_id, dataset_id in mps:
            if dataset_id == main_dataset_id:
                dataset_id_maps.append((chart_id, dataset_id))

        trans_global_params = {
            "id": global_param_id,
            "dashboard_id": to_dashboard_id,
            "name": global_param_name,
            "alias_name": alias_name,
            "dataset_field_relations": [
                {
                    'global_params_id': global_param_id,
                    'dashboard_id': to_dashboard_id,
                    'chart_id': one[0],
                    'dataset_id': one[1],
                    'dataset_field_id': to_dataset_field_id,
                }
                for one in dataset_id_maps
            ],
            # "dataset_vars_relations": [],
            # "filter_chart_relations": []
        }
        self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
        self.merge_to_global_params(to_dashboard_id, trans_global_params)

    def get_dataset_field_info_by_dataset_filed_id(self, dataset_field_id):
        return self.get_data(
            'dap_bi_dataset_field', conditions={'id': dataset_field_id}, fields=[], multi_row=False
        )

    def deal_filter_opt_none(self):  # NOSONAR
        #  处理配置是无的情况
        #  全局筛选配置为无： 维度字段-col_name 变量-变量id 固定值-固定值名字

        def get_dim_jump_global_name(jump_relation):
            dataset_field_id = jump_relation['dataset_field_id']
            dataset_field_info = self.get_dataset_field_info_by_dataset_filed_id(dataset_field_id)
            if dataset_field_info:
                global_param_name = dataset_field_info['col_name']
                alias_name = dataset_field_info['alias_name']
                return global_param_name, alias_name
            return '', ''

        def get_params_jump_global_name(jump_relation):
            dataset_field_id = jump_relation['param_dataset_field_id']
            dataset_field_info = self.get_dataset_field_info_by_dataset_filed_id(dataset_field_id)
            if dataset_field_info:
                global_param_name = dataset_field_info['col_name']
                alias_name = dataset_field_info['alias_name']
                return global_param_name, alias_name
            return '', ''

        def get_var_jump_global_name(jump_relation):
            var_id = jump_relation['var_id']
            var = self.get_data('dap_bi_dataset_vars', conditions={'id': var_id}, fields=[], multi_row=False)
            alias_name = var.get('name', '')
            if var:
                return var_id, alias_name
            return '', ''

        deal_mapping = {
            'jump_relation': get_dim_jump_global_name,
            'var_jump_relation': get_var_jump_global_name,
            'fixed_var_jump_relation': lambda jump_relation: (jump_relation['var_name'], jump_relation['var_name']),
        }

        for from_chart_data in self.from_chart_datas:
            # 1. 取单组件所有的组件跳转

            jumps = self.loads(from_chart_data.get('jump') or '[]')
            for jump in jumps:
                if jump.get('target_type') == "dashboard":
                    to_dashboard_id = jump['target']  # 跳转的报告id
                    for key, func in deal_mapping.items():
                        self._deal_common_jump_config_with_opt_none(jump, key, to_dashboard_id, func)

            # 处理参数跳转
            chart_params_jumps = self.loads(from_chart_data.get('chart_params_jump') or '[]')
            self._deal_params_config_with_opt_none(chart_params_jumps, get_params_jump_global_name)

            # 回填jump数据
            from_chart_data['jump'] = self.dumps(jumps)
            from_chart_data['chart_params_jump'] = self.dumps(chart_params_jumps)

    def _deal_params_config_with_opt_none(self, jump, global_params_func):
        for jump_relation in jump:
            dashboard_filter_id = jump_relation['dashboard_filter_id']
            # 是配置的全局筛选以及配置的是无
            if dashboard_filter_id == '' and jump_relation.get('relation_field_type') == 1:
                # 以前的参数跳转是通过source_id来找jump_config的
                from_dashboard_id = jump_relation['dashboard_id']
                source_id = jump_relation['source_id']
                jump_config = self.get_data(
                    'dap_bi_dashboard_jump_config', conditions={'dashboard_id': from_dashboard_id, 'source_id': source_id},
                    fields=[], multi_row=False
                )
                to_dashboard_id = jump_config.get('target', '')
                global_param_name, alias_name = global_params_func(jump_relation)
                global_param_name, global_param_id = self.generate_global_param_name(
                    global_param_name, to_dashboard_id
                )
                # 处理全局参数
                trans_global_params = {
                    "id": global_param_id,
                    "dashboard_id": to_dashboard_id,
                    "name": global_param_name,
                    "alias_name": alias_name,
                    "dataset_field_relations": [],
                    "dataset_vars_relations": [],
                    "filter_chart_relations": []
                }
                self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
                self.update_target_key_2_global_params_with_none(jump_relation, global_param_id)
                self.merge_to_global_params(to_dashboard_id, trans_global_params)
                jump_relation['global_params_id'] = global_param_id
                jump_relation['__has_upgraded__'] = 1

    def _deal_common_jump_config_with_opt_none(self, jump, key, to_dashboard_id, global_params_func):
        jump_relations = jump.get(key, [])
        for jump_relation in jump_relations:
            dashboard_filter_id = jump_relation['dashboard_filter_id']
            # 是配置的全局筛选以及配置的是无
            if dashboard_filter_id == '' and jump_relation.get('relation_field_type') == 1:
                global_param_name, alias_name = global_params_func(jump_relation)
                global_param_name, global_param_id = self.generate_global_param_name(
                    global_param_name, to_dashboard_id
                )
                # 处理全局参数
                trans_global_params = {
                    "id": global_param_id,
                    "dashboard_id": to_dashboard_id,
                    "name": global_param_name,
                    "alias_name": alias_name,
                    "dataset_field_relations": [],
                    "dataset_vars_relations": [],
                    "filter_chart_relations": []
                }
                self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
                self.update_target_key_2_global_params_with_none(jump_relation, global_param_id)
                self.merge_to_global_params(to_dashboard_id, trans_global_params)
                jump_relation['global_params_id'] = global_param_id
                jump_relation['__has_upgraded__'] = 1
