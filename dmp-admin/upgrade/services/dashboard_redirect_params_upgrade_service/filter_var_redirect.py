#!/usr/local/bin python3
# -*- coding: utf-8 -*-
#
import json
from .base import SceneUpgradeBase


class FilterVarRedirectSceneUpgrade(SceneUpgradeBase):
    scene = '升级筛选器参数接收'

    """
    跳转传参数：升级筛选器参数接收
    """

    def do(self):  # NOSONAR
        for from_chart in self.from_chart_datas:
            jump_config = from_chart.get('jump')
            jump_config = json.loads(jump_config) if jump_config else []
            if not jump_config:
                continue
            for jump in jump_config:
                target_dashboard_id = jump.get('target')
                self._deal_relation(jump, 'jump_relation', target_dashboard_id)
                self._deal_relation(jump, 'var_jump_relation', target_dashboard_id)
                self._deal_relation(jump, 'fixed_var_jump_relation', target_dashboard_id)
            from_chart['jump'] = json.dumps(jump_config, ensure_ascii=False)

        # 参数跳转
        for from_chart in self.from_chart_datas:
            chart_params_jumps = from_chart.get('chart_params_jump')
            chart_params_jumps = json.loads(chart_params_jumps) if chart_params_jumps else []
            if not chart_params_jumps:
                continue
            for chart_params_jump in chart_params_jumps:
                real_jump_config = self.get_chart_jump_config(chart_params_jump)
                # dashboard_chart_id = chart_params_jump['dashboard_chart_id']
                # source_id = chart_params_jump['source_id']
                # real_jump_config = self.get_data(
                #     "dashboard_jump_config",
                #     {"dashboard_chart_id": dashboard_chart_id, "source_id": source_id}, fields=[], multi_row=False
                # )
                if not real_jump_config:
                    continue
                target_dashboard_id = real_jump_config.get('target')
                if chart_params_jump.get('relation_field_type') == 2:
                    self.prepare_filter_global_params(target_dashboard_id, chart_params_jump)
                elif chart_params_jump.get('relation_field_type') == 3:
                    self.prepare_var_global_params(target_dashboard_id, chart_params_jump)
            from_chart['chart_params_jump'] = json.dumps(chart_params_jumps, ensure_ascii=False)

    def _deal_relation(self, jump, key, target_dashboard_id):
        jump_relation = jump.get(key, [])
        if not jump_relation:
            return
        for relation in jump_relation:
            if relation.get('relation_field_type') == 2:
                self.prepare_filter_global_params(target_dashboard_id, relation)
            elif relation.get('relation_field_type') == 3:
                self.prepare_var_global_params(target_dashboard_id, relation)

    def prepare_filter_global_params(self, target_dashboard_id, relation):
        target_key, from_key = self.get_jump_relation_key(relation)
        filter_chart_id = relation.get('target_filter_id')
        dataset_field_id = relation.get('target_filter_field_id')
        global_param_name, global_param_id = self.generate_global_param_name('', target_dashboard_id, target_key)
        date_filter_chart_flag = self.get_date_filter_chart_flag_by_chart_id(filter_chart_id)
        if date_filter_chart_flag == 'date':
            dataset_field_id = ''
        chart = self.get_data('dap_bi_dashboard_chart', conditions={'id': filter_chart_id}, fields=[], multi_row=False)
        alias_name = chart.get('name', '')
        relation['global_params_id'] = global_param_id
        relation['__has_upgraded__'] = 1

        # 处理全局参数
        trans_global_params = {
            "id": global_param_id,
            "dashboard_id": target_dashboard_id,
            "name": global_param_name,
            "alias_name": alias_name,
            "filter_chart_relations": [
                {
                    'global_params_id': global_param_id,
                    'dashboard_id': target_dashboard_id,
                    'filter_chart_id': filter_chart_id,
                    "date_filter_chart_flag": date_filter_chart_flag,
                    'dataset_field_id': dataset_field_id,
                }
            ],
        }
        self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
        self.merge_to_global_params(target_dashboard_id, trans_global_params)

    def prepare_var_global_params(self, target_dashboard_id, relation):
        target_key, from_key = self.get_jump_relation_key(relation)
        global_param_name, global_param_id = self.generate_global_param_name('', target_dashboard_id, target_key)
        var_ids = relation.get('target_filter_field_id', '')
        dataset_ids = relation.get('target_filter_id', '')
        var_ids = [i.strip() for i in var_ids.split(',')]
        dataset_ids = [i.strip() for i in dataset_ids.split(',')]
        list_data = list(zip(dataset_ids, var_ids))

        for dataset_id, var_id in list_data:
            var = self.get_data('dap_bi_dataset_vars', conditions={'id': var_id}, fields=[], multi_row=False)
            alias_name = var.get('name', '')
            relation['global_params_id'] = global_param_id
            relation['__has_upgraded__'] = 1
            # 处理全局参数
            trans_global_params = {
                "id": global_param_id,
                "dashboard_id": target_dashboard_id,
                "name": global_param_name,
                "alias_name": alias_name,
                "dataset_vars_relations": [
                    {
                        'global_params_id': global_param_id,
                        'dashboard_id': target_dashboard_id,
                        'dataset_id': dataset_id,
                        'var_id': var_id,
                    }
                ],
            }
            self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
            self.merge_to_global_params(target_dashboard_id, trans_global_params)
