#!/usr/local/bin python3
# -*- coding: utf-8 -*-
#
import json

from .base import SceneUpgradeBase
from dmplib.utils.strings import seq_id


class PlatformKeywordsSceneUpgrade(SceneUpgradeBase):
    scene = '平台关键字升级'

    """
    平台关键字升级
    """

    def do(self):
        for to_dashboard_id, to_dashboard_data in self.to_dashboard_datas.items():
            to_dashboard_data = to_dashboard_data[0]
            if self.upgrade_mode == 'unreleased':
                self.deal_unreleased(to_dashboard_data)

        if self.from_dashboard_datas:
            self.deal_unreleased(self.from_dashboard_datas[0])

    def deal_unreleased(self, to_dashboard_data):
        # 现在只考虑设计时的关键字
        # """
        # [
        #     {
        #         "id": "f38bdbb7-f1fa-11ec-b27c-ddef90441dfe",
        #         "value_source_name": "userid",
        #         "value_source": "context",
        #         "value_identifier": "userid",
        #         "relations": [
        #         "3a02c83a-f7f7-d6d5-d052-9e62e11961e8"
        #         ]
        #     }
        # ]
        # """
        var_value_sources = self.loads(to_dashboard_data.get('var_value_sources', '[]'))
        for var_value_source in var_value_sources:
            # 升级平台关键字
            if var_value_source.get('value_source') == 'context' and var_value_source.get(
                    'value_identifier') == 'userid':
                self.deal_keywords(var_value_source)
            elif var_value_source.get('value_source') == 'url':
                # 处理url变量
                self.deal_url_vars(var_value_source, to_dashboard_data['id'])
        # to_dashboard_data['var_value_sources'] = json.dumps(var_value_sources)

    def deal_url_vars(self, var_value_source, to_dashboard_id):
        # 处理url变量
        # var_value_source: {
        #     "id": "3a056110-3aa1-3bc9-dbc8-23bfefe591d2",
        #     "value_source_name": "test_1",
        #     "value_source": "url",
        #     "value_identifier": "test_1",
        #     "relations": [
        #         "3a04eef6-4b2d-6320-076c-70eb0629ecf3"
        #     ]
        # }
        var_ids = var_value_source.get("relations", [])
        global_param_name = var_value_source.get('value_source_name', '')
        alias_name = var_value_source.get('value_identifier', '')
        global_param_name, global_param_id = self.generate_global_param_name(
            global_param_name, to_dashboard_id
        )
        # 处理全局参数
        trans_global_params = {
            "id": global_param_id,
            "dashboard_id": to_dashboard_id,
            "name": global_param_name,
            "alias_name": alias_name,
            # "dataset_vars_relations": [
            #     {
            #         'global_params_id': global_param_id,
            #         'dashboard_id': to_dashboard_id,
            #         'dataset_id': dataset_id,
            #         'var_id': var_id,
            #     }
            # ],
        }
        dataset_vars_relations = []
        for var_id in var_ids:
            var = self.get_data('dap_bi_dataset_vars', conditions={'id': var_id}, fields=[], multi_row=False)
            dataset_id = var.get('dataset_id', '')
            dataset_vars_relations.append(
                {
                    'global_params_id': global_param_id,
                    'dashboard_id': to_dashboard_id,
                    'dataset_id': dataset_id,
                    'var_id': var_id,
                }
            )
        trans_global_params['dataset_vars_relations'] = dataset_vars_relations
        self.logging_func(f'要添加的全局参数信息: {trans_global_params}')
        self.merge_to_global_params(to_dashboard_id, trans_global_params)

    def deal_keywords(self, var_value_source):
        var_ids = var_value_source.get("relations", [])
        # 处理关键字
        keyword = self.get_data('dap_bi_keyword', conditions={'keyword_name': '本人id', 'is_system': 1}, fields=['id'])
        if not keyword:
            keyword_id = seq_id()
            self.add_data('dap_bi_keyword', data={
                'id': keyword_id,
                'keyword_type': 0,
                'data_type': '文本',
                'keyword_name': '本人id',
                'is_system': 1,
            }, commit=False)
        else:
            keyword_id = keyword['id']

        # 处理关键字关系
        for var_id in var_ids:
            dataset_var = self.get_data('dap_bi_dataset_vars', conditions={'id': var_id}, fields=[])
            dataset_id = dataset_var.get('dataset_id', '')
            exist = self.get_data('dap_bi_keyword_details', conditions={
                'keyword_id': keyword_id,
                'var_id': var_id,
                'dataset_id': dataset_id,
            }, fields=['id'])
            if not exist:
                self.add_data('dap_bi_keyword_details', data={
                    'keyword_id': keyword_id,
                    'var_id': var_id,
                    'dataset_id': dataset_id,
                }, commit=False)
            self.update_data(
                'dap_bi_dataset_vars',
                condition={'id': var_id}, data={'default_value_type': 1},
                commit=False
            )
