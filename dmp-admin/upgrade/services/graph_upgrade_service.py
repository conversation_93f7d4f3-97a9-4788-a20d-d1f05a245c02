import json
import traceback

from base import repository
from dmplib.db.mysql_wrapper import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from upgrade.repositories import graph_upgrade_repository
from upgrade.services.graph_upgrade_task_item_runner import GraphUpgradeTaskItemRunner
from loguru import logger


def upgrade(tenant_code, is_all_tenant):
    if is_all_tenant:
        from project.models import ProjectQueryModel
        from project.services import project_service
        page_data = {'page': 1, 'page_size': 10000}
        tenant_data = project_service.get_project_list(ProjectQueryModel(**page_data)).get_result_dict()
        code_list = [item.get("code") for item in tenant_data.get("items")]
    else:
        if not tenant_code:
            raise UserError(code=400, message="tenant_code is required")
        code_list = [tenant_code]
    return upgrade_internal(code_list)


def upgrade_internal(code_list):
    task_id = graph_upgrade_repository.add_task(code_list)
    from app_celery import upgrade_to_graph
    upgrade_to_graph.delay(**{
        'task_id': task_id,
    })
    return task_id


def execute(task_id):
    try:
        execute_internal(task_id)
    except:
        logger.error(traceback.format_exc())
        graph_upgrade_repository.end_task_with_exception(task_id, traceback.format_exc())


def execute_internal(task_id):
    task = graph_upgrade_repository.get_task(task_id)
    if task.get("status") != "已创建":
        raise Exception(f'无法执行状态为{task.get("status")}的任务')
    tenant_list = task.get("tenant_list").split(',')
    # 创建子任务
    logger.error("开始创建子任务")
    task_items = create_task_items(task_id, tenant_list)
    logger.error('子任务创建完成')
    execute_result = {}
    for task_item in task_items:
        logger.error(f'开始运行子任务:{task_item.get("tenant_code")}')
        runner = GraphUpgradeTaskItemRunner(task, task_item)
        try:
            runner.run()
            success = True
        except:
            error_stack = traceback.format_exc()
            runner.write_log(error_stack)
            success = False
            logger.error(error_stack)

        log = runner.log
        graph_upgrade_repository.end_task_item(task_item.get('id'), success, json.dumps(log, ensure_ascii=False))
        execute_result[task_item.get("tenant_code")] = success

        logger.error(f'子任务运行结束:{task_item.get("tenant_code")}，运行结果:{"成功" if success else "失败"}')

    if all(execute_result.values()):
        result = '全部成功'
    elif any(execute_result.values()):
        result = '部分成功'
    else:
        result = '全部失败'
    graph_upgrade_repository.end_task(task_id, result)


def create_task_items(task_id, tenant_list):
    with get_db() as db:
        graph_upgrade_repository.update_task_status(task_id, '执行中', commit=False)
        task_item_insert_list = []
        for tenant_code in tenant_list:
            task_item = graph_upgrade_repository.get_task_item_by_tenant_code(tenant_code)
            if task_item and task_item.get('is_complete') == 1:
                # 租户已经升级成功了，复制一份数据即可
                task_item['task_id'] = task_id
            else:
                task_item = {
                    'id': seq_id(),
                    'task_id': task_id,
                    'status': 0,
                    'tenant_code': tenant_code,
                }
            task_item_insert_list.append(task_item)
        if len(task_item_insert_list) > 0:
            repository.add_list_data('dap_bi_dataset_graph_upgrade_task_item', task_item_insert_list,
                                     list(task_item_insert_list[0].keys()))
        db.commit()
        return task_item_insert_list


def get_result(task_id, loads_log=True):
    if not task_id:
        raise UserError(code=400, message="task_id is required")
    task = repository.get_data_by_sql('select * from dap_bi_dataset_graph_upgrade_task where id=%(id)s', {
        'id': task_id,
    })
    if task is None or len(task) == 0:
        raise UserError(code=400, message="task_id not found")

    task_items = repository.get_data_by_sql(
        'select * from dap_bi_dataset_graph_upgrade_task_item where task_id=%(task_id)s', {
            'task_id': task_id
        })
    if loads_log:
        if task_items:
            for task_item in task_items:
                if task_item.get('log'):
                    task_item['log'] = json.loads(task_item['log'])
    return {
        'task': task[0],
        'task_items': task_items,
    }


def test_get_upgrade_info(tenant_code):
    runner = GraphUpgradeTaskItemRunner({}, {
        'tenant_code': tenant_code
    })
    result = runner.prepare_upgrade_data()
    return result
