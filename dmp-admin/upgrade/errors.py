from dmplib.utils.errors import UserError


class SameJobRunError(UserError):
    """
    同时允许相同任务错误
    """

    def __init__(self, message=""):
        """
        同时允许相同任务错误
        :param str message:
        """
        self.code = 7000
        self.message = message


class UpgrageError(UserError):
    """
    数据升级异常
    """

    def __init__(self, message=""):
        """
        数据升级异常
        :param str message:
        """
        self.code = 8000
        self.message = message
