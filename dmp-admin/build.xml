<?xml version="1.0" encoding="UTF-8"?>
<project name="dmp-admin" default="build" xmlns:sonar="antlib:org.sonar.ant">
    <!-- By default, we assume all tools to be on the $PATH -->
    <property name="toolsdir" value=""/>

    <property name="sonar.host.url" value="http://sonar.mypaas.com.cn"/>
    <property name="sonar.login" value="****************************************"/>

    <!-- Define the SonarQube project properties -->
    <property name="sonar.projectKey" value="cn.com.mypaas:dmp-admin"/>
    <property name="sonar.projectName" value="DMP.Admin"/>
    <property name="sonar.projectVersion" value="1.0"/>
    <property name="sonar.sources" value="base,project,project_odps,rds,upload,user"/>
    <property name="sonar.exclusions" value=""/>
    <property name="sonar.language" value="py"/>
    <property name="sonar.sourceEncoding" value="UTF-8"/>
    <property name="sonar.python.xunit.reportPath" value="tests/nosetests.xml"/>
    <property name="sonar.python.coverage.reportPath" value="tests/coverage.xml"/>

    <!-- Define the SonarQube target -->
    <target name="sonar">
        <!-- Execute the SonarQube analysis -->
        <sonar:sonar/>
    </target>

    <target name="nose"
            depends="prepare"
            description="nose extends unittest to make testing easier">
        <exec executable="${toolsdir}nosetests" failonerror="true">
            <arg value="-w"/>
            <arg value="tests/"/>
            <arg value="--with-xunit"/>
            <arg value="--xunit-file=tests/nosetests.xml"/>
            <arg value="--with-coverage"/>
            <arg value="--cover-erase"/>
            <arg value="--cover-xml"/>
        </exec>
    </target>

    <target name="build"
            depends="nose,sonar"
            description="默认构建任务"/>

    <target name="tools-parallel" description="Run tools in parallel">
        <parallel threadCount="2">
            <sequential>
                <!--antcall target="pdepend"/-->
                <antcall target="phpmd-ci"/>
            </sequential>
            <antcall target="phpcpd-ci"/>
            <antcall target="phpcs-ci"/>
            <antcall target="phploc-ci"/>
        </parallel>
    </target>

    <target name="clean"
            unless="clean.done"
            description="Cleanup build artifacts">
        <delete dir="${basedir}/build/api"/>
        <delete dir="${basedir}/build/coverage"/>
        <delete dir="${basedir}/build/logs"/>
        <delete dir="${basedir}/build/pdepend"/>
        <delete dir="${basedir}/build/phpdox"/>
        <property name="clean.done" value="true"/>
    </target>

    <target name="prepare"
            unless="prepare.done"
            depends="clean"
            description="Prepare for build">
        <mkdir dir="${basedir}/build/api"/>
        <mkdir dir="${basedir}/build/coverage"/>
        <mkdir dir="${basedir}/build/logs"/>
        <mkdir dir="${basedir}/build/pdepend"/>
        <mkdir dir="${basedir}/build/phpdox"/>
        <property name="prepare.done" value="true"/>
    </target>

    <target name="lint" description="Perform syntax check of sourcecode files">
        <apply executable="php" failonerror="true">
            <arg value="-l"/>

            <fileset dir="${basedir}">
                <include name="**/*.php"/>
                <exclude name="protected/vendor/**"/>
                <modified/>
            </fileset>
        </apply>
    </target>

    <target name="phploc"
            description="Measure project size using PHPLOC and print human readable output. Intended for usage on the command line.">
        <exec executable="${toolsdir}phploc">
            <arg value="--count-tests"/>
            <arg path="${basedir}"/>
        </exec>
    </target>

    <target name="phploc-ci"
            depends="prepare"
            description="Measure project size using PHPLOC and log result in CSV and XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${toolsdir}phploc">
            <arg value="--count-tests"/>
            <arg value="--log-csv"/>
            <arg path="${basedir}/build/logs/phploc.csv"/>
            <arg value="--log-xml"/>
            <arg path="${basedir}/build/logs/phploc.xml"/>
            <arg path="${basedir}"/>
        </exec>
    </target>

    <target name="pdepend"
            depends="prepare"
            description="Calculate software metrics using PHP_Depend and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${toolsdir}pdepend">
            <arg value="--jdepend-xml=${basedir}/build/logs/jdepend.xml"/>
            <arg value="--jdepend-chart=${basedir}/build/pdepend/dependencies.svg"/>
            <arg value="--overview-pyramid=${basedir}/build/pdepend/overview-pyramid.svg"/>
            <arg path="${basedir}"/>
        </exec>
    </target>

    <target name="phpmd"
            description="Perform project mess detection using PHPMD and print human readable output. Intended for usage on the command line before committing.">
        <exec executable="${toolsdir}phpmd">
            <arg path="${basedir}"/>
            <arg value="text"/>
            <arg path="${basedir}/build/phpmd.xml"/>
            <arg value="--exclude"/>
            <arg path="protected/vendor"/>
        </exec>
    </target>

    <target name="phpmd-ci"
            depends="prepare"
            description="Perform project mess detection using PHPMD and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${toolsdir}phpmd">
            <arg path="${basedir}"/>
            <arg value="xml"/>
            <!--arg path="${basedir}/build/phpmd.xml" /-->
            <arg value="design,unusedcode"/>
            <arg value="--reportfile"/>
            <arg path="${basedir}/build/logs/pmd.xml"/>
            <arg value="--exclude"/>
            <arg path="protected/vendor"/>
        </exec>
    </target>

    <target name="phpcs"
            description="Find coding standard violations using PHP_CodeSniffer and print human readable output. Intended for usage on the command line before committing.">
        <exec executable="${toolsdir}phpcs">
            <arg value="--standard=PSR2"/>
            <arg value="--extensions=php"/>
            <arg value="--ignore=autoload.php"/>
            <arg path="${basedir}"/>
            <arg path="${basedir}/tests"/>
        </exec>
    </target>

    <target name="phpcs-ci"
            depends="prepare"
            description="Find coding standard violations using PHP_CodeSniffer and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${toolsdir}phpcs">
            <arg value="--report=checkstyle"/>
            <arg value="--report-file=${basedir}/build/logs/checkstyle.xml"/>
            <arg value="--standard=PSR2"/>
            <arg value="--extensions=php"/>
            <arg value="--ignore=autoload.php"/>
            <arg path="${basedir}"/>
        </exec>
    </target>

    <target name="phpcpd"
            description="Find duplicate code using PHPCPD and print human readable output. Intended for usage on the command line before committing.">
        <exec executable="${toolsdir}phpcpd">
            <arg path="${basedir}"/>
            <arg value="--exclude"/>
            <arg path="protected/vendor"/>
        </exec>
    </target>

    <target name="phpcpd-ci"
            depends="prepare"
            description="Find duplicate code using PHPCPD and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${toolsdir}phpcpd">
            <arg value="--log-pmd"/>
            <arg path="${basedir}/build/logs/pmd-cpd.xml"/>
            <arg value="--exclude"/>
            <arg path="protected/vendor"/>
            <arg path="${basedir}"/>
        </exec>
    </target>

    <target name="phpunit"
            depends="prepare"
            description="Run unit tests with PHPUnit">
        <exec executable="${toolsdir}phpunit" failonerror="true">
            <arg value="--configuration"/>
            <arg path="${basedir}/protected/phpunit.xml"/>
        </exec>
    </target>

    <target name="phpdox"
            depends="phploc-ci,phpcs-ci,phpmd-ci"
            description="Generate project documentation using phpDox">
        <exec executable="${toolsdir}phpdox" dir="${basedir}/build"/>
    </target>
</project>

