#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base.models import BaseModel, QueryBaseModel


class ProjectYzsConfigModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = ''
        self.code = ''
        self.tenant_id = ''
        self.customer_id = ''
        self.secret = ''
        self.enable_hd_report = 0  # 是否集成HighData报告，默认 0 不集成
        self.enable_yzs_message = 0  # 是否启用云助手发送简讯，默认是 0 不启用
        self.hd_secret = ''

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('code', 'string', {'max': 254, 'required': True}))
        rules.append(('tenant_id', 'string', {'max': 254, 'required': False}))
        rules.append(('customer_id', 'string', {'max': 254, 'required': False}))
        rules.append(('secret', 'string', {'max': 254, 'required': False}))
        rules.append(('hd_secret', 'string', {'max': 254, 'required': False}))
        return rules


class ProjectYzsConfigQueryModel(QueryBaseModel):
    pass
