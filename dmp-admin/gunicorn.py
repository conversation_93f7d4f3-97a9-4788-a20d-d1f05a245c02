import os



def when_ready(server):
    from components.celery_base import worker_process_init
    worker_process_init()

def on_starting(_):
    prometheus_dir = os.environ.get('prometheus_multiproc_dir')
    if prometheus_dir and not os.path.exists(prometheus_dir):
        os.mkdir(prometheus_dir)


def worker_exit(_, worker):
    from prometheus_client import multiprocess

    multiprocess.mark_process_dead(worker.pid)
