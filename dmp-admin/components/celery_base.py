import logging
import random
from celery import Task
import json
from celery.signals import task_prerun as task_prerun_de, task_postrun as task_postrun_de, celeryd_after_setup, \
    worker_process_init, worker_process_shutdown, worker_ready
import project, tenant, upload, rds

from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.redis import RedisCache

logger = logging.getLogger(__name__)

redis_cache = RedisCache()
DMP_UNACKED = 'dmp_admin_unacked'


def get_unacked_id(flow_id):
    return 'dmp-admin:celery:unacked:1:{flow_id}'.format(flow_id=flow_id)


# Not needed for RabbitMQ、
def restore_all_unacknowledged_messages():
    for key in redis_cache.keys(get_unacked_id('') + '*'):
        unacked = redis_cache.get_data(key)
        ip_module = __import__('app_celery')
        k = getattr(ip_module, unacked.get('func_name'))
        k.delay(**unacked.get('params'))


@worker_ready.connect
def worker_process_init(**kwargs):
    from project.services.template_sync_service import query_not_run_task
    from app_celery import do_delivery_template_data_sync
    from project.services.project_init_service import TASK_KEY

    logger.info('断点续传')

    worker_g = _AppCtxGlobals()
    worker_g.account = 'celery'
    _app_ctx_stack.push(worker_g)
    db_ctx = DBContext()
    db_ctx.inject(worker_g)

    try:
        tasks = query_not_run_task() or []
        task_map = {}
        cache = RedisCache(key_prefix='open_tenant')
        values = cache.get(TASK_KEY)
        logger.info(f"key: {TASK_KEY}, values: {str(values)}")
        if values:
            values = json.loads(values) or []
            for task in tasks:
                task_id = task.get('task_id')
                if task_id not in values:
                    continue
                if task_id not in task_map:
                    task_map[task_id] = {
                        'model': json.loads(task.get('model')),
                        'files': [json.loads(task.get('export'))],
                        'apps' : json.loads(task.get('apps'))
                    }
                else:
                    task_map[task_id]['files'].append(json.loads(task.get('export')))

        logger.info(f'待处理的任务数: {len(task_map)}')

        for task_id, task in task_map.items():
            do_delivery_template_data_sync.apply_async(kwargs=task)

        cache.delete(TASK_KEY)
    finally:
        worker_g = _app_ctx_stack.pop()
        # close all connections
        db_ctx = DBContext.instance(worker_g)
        if db_ctx:
            db_ctx.close_all()

    # logger.error('处理开户回调')
    # try:
    #     cache = RedisCache(key_prefix='open_tenant')
    #     values = cache.get(TASK_KEY)
    #     if values:
    #         values = json.loads(values) or []
    #         for task_id in values:
    #             logger.error(f'开户失败回调,task_id：{task_id}')
    #             result_callback(task_id, False, "达梦SDK异常，请联系数见团队")
    #         cache.delete(TASK_KEY)
    # except Exception as e:
    #     logger.error(f'处理开户回调失败：{e}')



@task_prerun_de.connect()
def task_prerun(signal, sender, **kwargs):
    g = _AppCtxGlobals()
    project_code = kwargs.get('project_code')
    account = kwargs.get('account') or 'celery'
    g.code = project_code
    g.account = account
    _app_ctx_stack.push(g)
    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)


@task_postrun_de.connect()
def task_postrun(signal, sender, **kwargs):
    g = _app_ctx_stack.pop()
    # close all connections
    db_ctx = DBContext.instance(g)
    if db_ctx:
        db_ctx.close_all()


@celeryd_after_setup.connect()
def celeryd_after_setup_handle(**kwargs):
    from components.rundeck import CommonRunDeckScheduler
    from dmplib import config

    if config.get('App.scheduler', 'rundeck') != 'rundeck':
        return

    logger.info("注册心跳任务")
    queue_name = kwargs.get('conf').CELERY_DEFAULT_QUEUE
    job_id = f"dmp_admin_heartbeat_{queue_name}"

    def get_command():
        celery_task_name = "dmp_admin_celery.heartbeat_task"
        cmd_template = config.get(
            "Rundeck.cmd_template_celery",
            "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
        )
        return '%s %s %s %s %s' % (cmd_template, "heartbeat", celery_task_name, job_id, queue_name)

    scheduler = CommonRunDeckScheduler()

    minute = random.randint(5, 10)
    schedule = f"0 */{minute} * ? * * *"
    command = get_command()

    if not scheduler.job_is_exists(job_id):
        scheduler.upset_job(job_id, f"心跳任务{queue_name}", schedule, command)


class BaseTask(Task):
    def run(self, *args, **kwargs):
        pass

    def on_success(self, task_progress, task_id, args, kwargs):
        logger.info('Task %s: success returned with progress: %s' % (task_id, task_progress))

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.info(u'Task %s: failure returned' % task_id)
