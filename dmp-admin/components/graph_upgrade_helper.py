import copy
import json

from dmplib.components import relation_converter
from dmplib.utils.strings import seq_id


def build_graph_filter_data(dataset_id, graph_content, dataset_filter):
    graph_filter = graph_content.get('filter')
    if graph_filter:
        where_factor = json.dumps(graph_filter.get('where_factor')) if graph_filter else None
        combo = graph_filter.get('combo') if graph_filter else None
        filter_data = {
            "id": dataset_filter.get('id') if dataset_filter else seq_id() or seq_id(),
            'dataset_id': dataset_id,
            'where_factor': where_factor,
            'combo': combo,
        }
    else:
        filter_data = None
    return filter_data


def build_graph_content(dataset, dataset_fields, dataset_tables_collection, dataset_filter):
    if dataset.get('is_import_table'):
        node_alias = None
        if len(dataset_tables_collection) > 0:
            node_alias = dataset_tables_collection[0].get('from_alias_name')
        if not node_alias:
            node_alias = dataset.get('import_table_name')

        node_name_cn = None
        if len(dataset_fields) > 0:
            node_name_cn = dataset_fields[0]['origin_table_comment']
        if not node_name_cn:
            node_name_cn = dataset.get('name').replace(f'({dataset.get("import_table_name")})', '')

        graph_content = {
            "node": [{
                "id": seq_id(),
                "node_id": seq_id(),
                "dataset_id": dataset.get("id"),
                "parent_id": None,
                "name": dataset.get("import_table_name"),
                "name_cn": node_name_cn,
                "alias": node_alias,
                "type": "TABLE",
                "table_type": dataset.get("import_table_type")
            }],
            "link": [],
            "filter": None
        }
    else:
        relation_content = __get_dataset_tables_collection(dataset_fields, dataset_tables_collection)
        graph_content = relation_converter.convert_relation_data_to_graph(relation_content, json.loads(
            dataset_filter.get('json_value')) if dataset_filter else None)

        if graph_content.get('link'):
            for link_item in graph_content.get('link'):
                link_item['dataset_id'] = dataset.get('id')
                link_item['link_id'] = link_item.get('id')
                link_item['id'] = seq_id()
                link_item['join_fields'] = json.dumps(link_item['join_fields'])
                link_item['join_factor'] = json.dumps(link_item['join_factor'])

        if graph_content.get('node'):
            for node_item in graph_content.get('node'):
                node_item['dataset_id'] = dataset.get('id')
                node_item['node_id'] = node_item.get('id')
                node_item['id'] = seq_id()
    return graph_content


def __get_dataset_tables_collection(dataset_fields, dataset_tables, return_web=True):
    """
    获取视图模式模型，从dmp复制过来
    """
    view_datas = dict()
    # 获取nodeDatas
    node_datas = copy.deepcopy(dataset_fields)
    dataset_tables_collections = copy.deepcopy(dataset_tables)
    new_node_datas = {}
    # 转换成前端传下来的格式
    for dataset_field in node_datas:
        # 视图模式下，origin_table_id一定不能为空
        if not dataset_field.get("origin_table_id"):
            continue
        dataset_field["type"] = dataset_field.get("origin_field_type")
        dataset_field["name"] = dataset_field.get("origin_col_name")
        dataset_field["comment"] = dataset_field.get("note")
        dataset_field["alias_name"] = dataset_field.get("alias_name")

        if dataset_field.get('origin_table_id') not in new_node_datas.keys():
            temp_dict = dict()
            temp_dict["id"] = dataset_field.get('origin_table_id')
            temp_dict["name"] = dataset_field.get('origin_table_name')
            temp_dict["name_cn"] = dataset_field.get('origin_table_comment')
            temp_dict["comment"] = dataset_field.get('origin_table_comment')
            temp_dict["alias_name"] = dataset_field.get('origin_table_alias_name')
            temp_dict["fields"] = [dataset_field]
        # 追加fields
        else:
            temp_dict = new_node_datas.get(dataset_field.get('origin_table_id'))
            temp_dict["fields"].append(dataset_field)
        new_node_datas[dataset_field.get('origin_table_id')] = temp_dict
    all_node_data_keys = list(new_node_datas.keys())
    # 组装nodeDatas
    view_datas['nodeDataArray'] = [v for k, v in new_node_datas.items()]
    table_type_dic = {}
    # 获取linkDatas
    link_datas = dataset_tables_collections
    new_link_datas = []
    if return_web:
        for row in link_datas:
            # 没有to_id的情况下，不需要返回给前端
            if not row.get("to_id") and return_web:
                table_type_dic[row.get("from_id")] = row.get("from_table_type")
                continue
            row["join_fields"] = json.loads(row.get("join_fields", json.dumps([])))
            new_link_datas.append(row)
            if row.get("to_id") and row.get("to_id") not in all_node_data_keys:
                _node_data = {
                    "id": row.get("to_id"),
                    "alias_name": row.get("to_alias_name"),
                    "comment": row.get("to_alias_name"),
                    "fields": [],
                    "name": row.get("to_table_name"),
                    "table_type": row.get("to_table_type"),
                }
                view_datas['nodeDataArray'].append(_node_data)
                all_node_data_keys.append(row.get("to_id"))
            if row.get("from_id") and row.get("from_id") not in all_node_data_keys:
                _node_data = {
                    "id": row.get("from_id"),
                    "alias_name": row.get("from_alias_name"),
                    "comment": row.get("to_alias_name"),
                    "fields": [],
                    "name": row.get("from_table_name"),
                    "table_type": row.get("from_table_type"),
                }
                view_datas['nodeDataArray'].append(_node_data)
                all_node_data_keys.append(row.get("from_id"))
            table_type_dic[row.get("to_id")] = row.get("to_table_type")
            table_type_dic[row.get("from_id")] = row.get("from_table_type")
        view_datas['linkDataArray'] = new_link_datas
        # 给节点添加表类型
        for node in view_datas['nodeDataArray']:
            if not node.get('table_type'):
                node['table_type'] = table_type_dic.get(node.get('id'))
    # 不是返回前端的话，应该要处理成对象的形式
    else:
        new_link_datas = []
        for link_data in link_datas:
            join_fields = [row for row in json.loads(link_data.get("join_fields", json.dumps([])))]
            link_data['join_fields'] = join_fields
            link_data['from_table_name'] = link_data.get("from_table_name")
            link_data['from_alias_name'] = link_data.get("from_table_name")
            link_data['to_table_name'] = link_data.get("to_table_name")
            link_data['to_alias_name'] = link_data.get("to_alias_name")
            link_data['from_table_type'] = link_data.get("from_table_type")
            link_data['to_table_type'] = link_data.get("to_table_type")
            new_link_datas.append(link_data)
        # 单表的情况下，nodeDatas有值，linkDatas中没有值，需要写一条linkDatas的记录
        if not new_link_datas and view_datas.get("nodeDataArray"):
            link_data = dict()
            link_data["from_id"] = view_datas.get("nodeDataArray")[0].get("id")
            link_data["from_table_name"] = view_datas.get("nodeDataArray")[0].get("name")
            link_data["from_table_type"] = view_datas.get("nodeDataArray")[0].get("from_table_type")
            new_link_datas.append(link_data)
        view_datas["linkDataArray"] = new_link_datas
    return view_datas
