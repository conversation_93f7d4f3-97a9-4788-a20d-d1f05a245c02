import base64
import datetime
import functools
import hashlib
import hmac
import os
from datetime import timedelta
from hashlib import md5
from urllib.parse import urlparse

import jwt
from alibabacloud_openapi_util.sm3 import Sm3
from gmssl import sm4
from jwt import register_algorithm
from jwt.algorithms import HMACAlgorithm
from loguru import logger

from dmplib.components import auth_util


def sm4_encode(key: str, value: bytes):
    """
    原文文本编码:UTF-8
    模式: CBC
    IV: 取密钥的前16位
    填充模式: PKCS#7
    块大小(密钥长度): 128位(16字节)
    :param key:
    :param value:
    :return:
    """
    iv = key[:16].encode()  # bytes类型
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(key, sm4.SM4_ENCRYPT)
    return crypt_sm4.crypt_cbc(iv, value)  # bytes类型


def sm4_decode(key: str, value: bytes):
    """
    原文文本编码:UTF-8
    模式: CBC
    IV: 取密钥的前16位
    填充模式: PKCS#7
    块大小(密钥长度): 128位(16字节)
    :param key:
    :param value:
    :return:
    """
    iv = key[:16].encode()  # bytes类型
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(key, sm4.SM4_DECRYPT)
    return crypt_sm4.crypt_cbc(iv, value)  # bytes类型


def sm3_encode(key, src_data):
    try:
        key = fill_padding(key.decode())
        key = base64.urlsafe_b64decode(key)
        mac = hmac.new(key, digestmod=Sm3)
        mac.update(src_data)
        return mac.digest()
    except Exception as e:
        logger.info("Failed to encrypt with key: %s", e)
        raise RuntimeError("Exception occurred during encryption with key")


def fill_padding(s):
    miss = 4 - len(s) % 4
    if miss:
        s += '=' * miss
    return s


class SM4Algorithm(HMACAlgorithm):

    def __init__(self, hash_alg=None):
        super().__init__(hash_alg)

    def sign(self, msg, key):
        return sm4_encode(key, msg)

    def verify(self, msg, key, sig):
        return sig == self.sign(msg, key)


class SM3Algorithm(HMACAlgorithm):

    def __init__(self, hash_alg=None):
        super().__init__(hash_alg)

    def sign(self, msg, key):
        return sm3_encode(key, msg)

    def verify(self, msg, key, sig):
        print(f'sig={base64.b64encode(sig)}')
        return sig == self.sign(msg, key)


def jwt_patch():
    try:
        register_algorithm('SM4', SM4Algorithm())
        register_algorithm('SM3', SM3Algorithm())
    except Exception as e:
        logger.info(f'注册jwt算法:{str(e)}')


def get_file_extension(url):
    """
    从url中提取出文件中后缀
    """
    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)
    file_extension = os.path.splitext(file_name)[1]
    return file_extension if file_extension else '.tmp'


def hash_k(k):
    return md5(str(k).encode("utf-8")).hexdigest()


def get_file_md5(fp):
    """
    计算本地文件的md5值
    """
    md5 = hashlib.md5()
    if isinstance(fp, str):

        with open(fp, 'rb') as file:
            for chunk in iter(lambda: file.read(4096), b""):
                md5.update(chunk)
        return md5.hexdigest()
    else:
        fp.seek(0)
        md5.update(fp.read())
        return md5.hexdigest()


def get_dist_upload_record(old_url):
    from base import repository
    url_fn = get_filename_from_url(old_url)
    sql = """select * from dap_bi_dist_upload_record where old_url like   %(like_url)s or old_url = %(old_url)s limit 1;"""
    rs = repository.get_data_by_sql(sql, {"old_url": old_url, "like_url": f'%/dist/{url_fn}%'}) or []
    if rs:
        record = rs[0]
    else:
        record = {}
    return record


def get_filename_from_url(url):
    """
    从url中提取出文件名称
    """
    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)
    return file_name



def apply_oss_file_to_own(old_url, fp, root):
    """
    把oss地址转换成自己的oss地址
    ！！！！前提是本地有这个oss的文件
        Args:
        old_url:  旧的oss地址
        old_fp:   文件路径
        root:     上传文件夹
    Returns: oss url
    """
    from base import repository
    from components.oss import OSSFileProxy
    oss = OSSFileProxy()
    endpoint = oss.proxy.endpoint.replace('https://', '').replace('http://', '').strip()
    bucket = oss.proxy.bucket_name
    # 保证是本环境的oss地址
    if endpoint in old_url and bucket in old_url:
        # 已经是本环境的地址了
        return old_url
    record = get_dist_upload_record(old_url)
    old_filename = get_filename_from_url(old_url)
    if not record:
        # 不存在，上传
        md5_val = get_file_md5(fp)
        if isinstance(fp, str):
            # fn = os.path.basename(fp)

            # 如果没有这个文件就使用就的oss地址
            if not os.path.exists(fp):
                # # 先oss去这个文件，取到了说明文件存在
                # data = oss.get_object(f'{root}/{fn}')
                logger.error(f'要上传oss的文件地址不存在:{fp}')
                return old_url

            with open(fp, 'rb') as f:
                oss_url = OSSFileProxy().upload(f, file_name=old_filename, root=root, **{'auto_mime': True})
                data = {'old_url': old_url, 'new_url': oss_url, 'md5': md5_val}
                repository.add_data('dap_bi_dist_upload_record', data=data)

        else:
            # fn = os.path.basename(fp.name)
            fp.seek(0)
            oss_url = OSSFileProxy().upload(fp, file_name=old_filename, root=root, **{'auto_mime': True})
            data = {'old_url': old_url, 'new_url': oss_url, 'md5': md5_val}
            repository.add_data('dap_bi_dist_upload_record', data=data)
        return oss_url
    else:
        oss_url = record.get('new_url', '')
        return oss_url


if __name__ == '__main__':
    import os

    os.environ['prometheus_multiproc_dir'] = '/tmp'
    jwt_patch()
    s = 'eyJrdHkiOiJvY3QiLCJ0eXAiOiJKV1QiLCJhbGciOiJTTTMiLCJraWQiOiIxNjk5NDI0ODg5In0.eyJzdWIiOiJ3b3JrYmVuY2giLCJuYmYiOjE3MDU1NTkzOTUsImV4cCI6MjA2NTU2Mjk5NSwianRpIjoiM2ExMDJkYTYtZDNiYi1lNWRhLTZhYjUtZjU1MWY1MzJhMmZiIn0.7HtOl7NljKoghdIlLzY78bMbdDG3HC2jctrDF7tC0ds'
    k = 'ByM1SysPpbyDfgZld3umj1qzKObwVMkoqQ-EstJQLr_T-1qS0gZH75aKtMN3Yj0iPS4hcgUuTwjAzZr1Z9CAow'
    # s = jwt.decode(s, k, algorithms='SM3')
    # print(s)

    payload = {
        'sub': 'dmp',
        'nbf': int((datetime.datetime.now() - timedelta(hours=1)).timestamp()),
        'exp': int((datetime.datetime.now() + timedelta(hours=1)).timestamp()),
        'jti': 'aaaaaa',
    }
    headers = {'kid': '1699424889', 'k': k}
    token = jwt.encode(payload, headers=headers, key=k, algorithm='SM3')
    print(token)
    decode = jwt.decode(token, k, algorithms='SM3')
    print(decode)

    t = auth_util.gen_auth_token({'a': '12312321321321321'})
    print(t)
    d = auth_util.verify_token(t)
    print(d)
