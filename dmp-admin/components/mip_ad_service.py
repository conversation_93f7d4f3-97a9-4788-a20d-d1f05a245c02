import copy
import json
import traceback
import requests
from loguru import logger

from dmplib import config
from dmplib.conf_constants import DEFAULT_LOGIN_SERVER_SECRET, DEFAULT_LOGIN_SERVER_KEY


# 兼容现有域名服务调用逻辑
class MIPADServiceClient:

    def __init__(self, service_url):
        self.service_url = service_url
        self.app_key = DEFAULT_LOGIN_SERVER_KEY
        self.app_secret = DEFAULT_LOGIN_SERVER_SECRET
        self.default_timeout = 15
        self.__check_args()
        access_token = self.__init_service_token()
        self.mip_headers = self.__build_mip_headers(access_token)
        # self.service = MIPADService(client=self)
        self.api_gate = f'{self.service_url}/apigate'

    def __check_args(self):
        self.service_url = self.service_url.strip('/')
        if not self.service_url:
            raise Exception('配置域服务地址缺失！')
        if not self.app_key:
            raise Exception('配置域服务认证AppKey缺失！')
        if not self.app_secret:
            raise Exception('配置域服务认证AppSecret缺失！')

    def __build_mip_headers(self, access_token):
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }

    def __build_default_kwargs(self, kwargs):
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.default_timeout
        if 'json' not in kwargs:
            kwargs['json'] = {}  # mip要求空数据传空json
        if 'headers' not in kwargs:
            kwargs['headers'] = {
                'Content-Type': 'application/json',
            }
        return kwargs

    def send(self, method='POST', return_json=True, **kwargs):
        # 交互文档
        # https://www.showdoc.com.cn/2221231897132631/9969925637728338

        kwargs = self.__build_default_kwargs(kwargs)
        try:
            logger.info(f'开始请求域服务的参数({method})：\n{self.hidde_and_format_data(kwargs)}')
            response = getattr(requests, method.lower())(**kwargs)
            logger.info(f'请求域服务的参数的结果：\n{response.text}')
            if return_json:
                return response.json()
            else:
                return response.content.decode()
        except Exception as e:
            logger.error(f'请求域服务失败，原因: {traceback.format_exc()}')
            raise Exception(f'请求域服务失败，原因: {str(e)}')

    def hidde_and_format_data(self, kwargs):
        format_data = copy.deepcopy(kwargs)
        if 'sPassWord' in format_data.get('json', {}):
            format_data['json']['sPassWord'] = '************'
        return json.dumps(format_data, ensure_ascii=False)

    def __init_service_token(self):
        kwargs = {
            "url": f"{self.service_url}/MIPApiAuth/Jwt",
            "json": {
                "AppKey": self.app_key,
                "AppSecret": self.app_secret,
            }
        }
        result = self.send(**kwargs)
        access_token = result.get('access_token', '')
        if not access_token:
            raise Exception('从域服务获取access_token失败！')
        return access_token

    def get_function(self, item):
        # mip神奇的逻辑，这个接口要多加个api，与其他接口规则不一致，不知道为啥
        if item == 'GetUserList':
            item = 'api/GetUserList'
        kwargs = {
            "url": f'{self.api_gate}/{item}',
            "headers": self.mip_headers
        }
        return self.callable_function(item, kwargs)

    def callable_function(self, item, pre_kwargs):
        def function(return_json=True, **real_kwargs):
            pre_kwargs['json'] = real_kwargs
            result = self.send(return_json=return_json, **pre_kwargs)
            if return_json:
                if result.get('code') == '404':
                    raise Exception(f'域服务函数{item}不存在！')
            return result

        return function


class MIPADService:
    def __init__(self, service_url):
        self.client = MIPADServiceClient(service_url)

    def __getattr__(self, item):
        # mip神奇的逻辑，这个接口要多加个api，与其他接口规则不一致，不知道为啥
        return self.client.get_function(item)

    def UserLoginForDomain(self, sUserName, sPassWord):
        # 校验用户信息，有点特殊，返回的不是json, mip返回的是字符串的True, 单独处理
        result = self.__getattr__('UserLoginForDomain')(return_json=False, **{
            "sUserName": sUserName,
            "sPassWord": sPassWord,
        })
        return result == 'True'
