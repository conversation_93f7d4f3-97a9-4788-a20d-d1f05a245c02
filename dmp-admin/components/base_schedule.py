
from dmplib.hug import g
from dmplib import config

def getScheduler(flow_model=None):
    if use_xxljob():
        from components.xxljob import XxlScheduler
        return XxlScheduler(flow_model)
    else:
        from components.rundeck import RundeckScheduler
        return RundeckScheduler(flow_model)


def use_xxljob():
    return config.get('App.scheduler', 'rundeck') == 'xxl-job'



class BaseScheduler:

    def __init__(self, flow_model=None):
        """
        Rundeck调度器
        :param flow.models.FlowModel flow_model:
        """
        self.flow_model = flow_model
        if flow_model:
            self.flow_model.name = self.format_name(self.flow_model.name)
        self.project_code = getattr(g, 'code', 'celery')
        self.queue_name = ''


    @staticmethod
    def format_name(name):
        return name.replace("/", '_').replace("\\", '_')

    def job_is_exists(self,job_id=None):
        pass

    def add_job(self, command=None):
        pass

    def update_job(self, command=None):
        pass

    def delete_job(self,job_id=None):
        pass

    def run_job(self, job_id=None):
        pass

    def upset_job(self, job_id, name, schedule, command, schedule_enabled: bool = True, **kwargs):
        pass
