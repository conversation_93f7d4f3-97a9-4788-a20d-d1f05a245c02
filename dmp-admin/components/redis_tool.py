import inspect
from functools import wraps
from binascii import crc32
import pickle

from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError


class RCache(RedisCache):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def del_by_scan(self, pattern, count=10000):
        """
        通过scan的方式删除key, 避免keys阻塞的问题
        """
        result = {}
        scan_result = self.scan(match=pattern, count=count)
        if isinstance(scan_result, dict):
            scan_result = scan_result.values()
        else:
            scan_result = [scan_result]
        for cur, match_keys in scan_result:
            while match_keys or cur:
                for p in match_keys:
                    p = p.decode()
                    result[p] = self._connection.delete(p)
                match_keys = None
                if cur:
                    cur, match_keys = self.scan(cursor=cur, match=pattern, count=count)
        return result


def rate_limit(rate=1, seconds=120, key_prefix="rate_limit"):
    def decorate(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache = RCache(key_prefix=key_prefix)
            if kwargs.get('request'):
                del kwargs['request']
            if kwargs.get('response'):
                del kwargs['response']
            key = f"{inspect.getfile(inspect.currentframe())}:{func.__name__}:{crc32(pickle.dumps(args)+pickle.dumps(kwargs))}"
            value = cache.get(key)
            if value:
                value = int(value.decode())
            else:
                value = 0
                cache.set(key, 0, seconds)

            if value + 1 > rate:
                raise UserError(message=f"超出调用频率，每{seconds}秒允许调用{rate}次, 剩余：{cache.ttl(key)}秒")
            res = func(*args, **kwargs)
            cache.incr(key, 1)
            return res
        return wrapper
    return decorate
