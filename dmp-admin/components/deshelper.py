#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    使用DES对称加密算法的CBC模式加密，解密
    <AUTHOR>
    @time 2021-3-22 17:52:25
"""
from Crypto.Cipher import DES
from Crypto.Util.Padding import pad
from Crypto.Util.Padding import unpad
from binascii import b2a_hex, a2b_hex


def encrypt(data: str, phase):
    # 使用DES对称加密算法的CBC模式加密
    key = iv = phase
    cipher = DES.new(key=key.encode(), mode=DES.MODE_CBC, iv=iv.encode())
    v = pad(data.encode(), DES.block_size)
    v = cipher.encrypt(v)
    return b2a_hex(v).decode('utf-8').upper()


def decrypt(data: str, phase):
    # 使用DES对称加密算法的CBC模式解密
    key = iv = phase
    cipher = DES.new(key=key.encode(), mode=DES.MODE_CBC, iv=iv.encode())
    bv = a2b_hex(data.lower())
    return unpad(cipher.decrypt(bv), DES.block_size).decode('utf-8')
