# -*- coding: utf-8 -*-
# @Time : 2022/1/10 15:27
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : sqlhelper.py
# @Project : dmp-admin
import re

import sql_metadata
import sqlparse


def clean(sql_str):
    # remove the /* */ comments
    q = re.sub(r"/\*[^*]*\*+(?:[^*/][^*]*\*+)*/", "", sql_str)
    # remove whole line -- and # comments
    lines = [line for line in q.splitlines() if not re.match("^\s*(--|#)", line)]
    # remove trailing -- and # comments
    q = " ".join([re.split("--|#", line)[0] for line in lines])
    q = ' '.join(q.split())
    return q


def extract_tables(sql):
    """
    获取表名
    :param sql:
    :return:
    """
    table_list = []
    sql = clean(sql)
    for s in sql.split(";"):
        table_list += sql_metadata.Parser(s).tables
    return table_list


def get_table_names(sql, is_alias=False):
    """
    解析sql表名称
    :param sql:
    :param is_alias: 是否包含别名（默认否）
    :return:
    """
    sql = clean(sql)
    table_names = parse_sql(sql, is_alias=is_alias)

    table_names = filter(None, table_names)
    table_names = [i for i in table_names if i.strip() and i.strip() not in ['(', ")"]]
    table_names = list(set(table_names))

    # 再用正则匹配一次, 匹配数据服务中心使用的表名
    dc_table_names = re.findall(r'\bdata_wide\w+', sql) or []

    table_names = table_names + dc_table_names

    table_names = [i.replace("`", "").replace('"', "").replace("[", "").replace("]", "") for i in table_names]

    return list(set(table_names))


def parse_sql(sql, is_alias=False):
    table_names = []
    sql_format = sqlparse.format(sql, encoding='UTF-8', reindent=True, keyword_case='upper')
    subsections = sql_format.split('\n')
    for subsection in subsections:
        if 'FROM' in subsection:
            if sql_format.upper().count('SELECT') > 1:
                table_names.append(subsection.replace("FROM", "").replace(")", ""))
            else:
                table_names.extend(re.findall("FROM(.*)", subsection, flags=re.I))
        if 'JOIN' in subsection:
            table_names.extend(re.findall("JOIN(.*)ON", subsection, flags=re.I))

    # 检验表名是否有字符串小写被upper成大写了
    return check_table_name_upper(table_names, sql, is_alias)


def check_table_name_upper(table_names: list, sql: str, is_alias: bool) -> list:
    # 检验表名是否有字符串小写被upper成大写了
    for index, table_name in enumerate(table_names):
        table_names[index] = table_name.strip().rstrip(";")
        if table_name not in sql and table_name.lower() in sql:
            table_names[index] = table_name.lower().rstrip(";")
        if table_name.isspace():
            table_names[index] = None
        if not is_alias and table_name.strip() and table_name.find(" ") > -1:
            table_names[index] = table_name.strip().split()[0].rstrip(";")
    return table_names
