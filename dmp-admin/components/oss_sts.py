#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    Author:<EMAIL>
    Created: 2017/11/9
"""
import re
import json
from oss2.api import utils
from oss2.auth import StsAuth
from dmplib import config
from dmplib.utils.errors import UserError
from components.oss import OSSFile

from aliyunsdkcore import client
from aliyunsdksts.request.v20150401 import AssumeRoleRequest

from typing import Dict, Union


class OssStsFileProxy:
    """ Summary
     使用oss sts  上传下载文件
    Attributes:
         :
    """

    def __init__(
        self,
        access_key_id: None = None,
        access_key_secret: None = None,
        bucket: None = None,
        endpoint: None = None,
        role_arn: None = None,
        role_session_name: None = None,
    ) -> None:
        self.proxy = None
        self.service = config.get('OSS_Config.service')
        self._auth = None
        self._bucket = None
        self.proxy = None
        if self.service == 'OSS':
            self.proxy = OssStsFile(access_key_id=access_key_id, access_key_secret=access_key_secret, bucket=bucket, endpoint=endpoint,
                                    role_arn=role_arn, role_session_name=role_session_name)
        elif self.service.upper() in ['MINIO', 'OBS']:
            self.proxy = MinioJsFile()
        # elif self.service == "OBS":
        #     self.proxy = ObsBrowserJsFile(access_key_id=access_key_id, access_key_secret=access_key_secret, bucket=bucket, endpoint=endpoint)
        else:
            raise UserError("配置不存在")
        self._auth = None
        self.temp_token = None
        self.security_token = None

    def auth(self):
        return self.proxy.auth()

    @property
    def region_id(self) -> str:
        return self.proxy.region_id()

    def get_security_token(self) -> Dict[str, Union[int, str]]:
        token = self.proxy.get_security_token()
        token['service'] = self.service
        return token


class OssStsFile(OSSFile):
    """ Summary
     使用oss sts  上传下载文件
    Attributes:
         :
    """

    def __init__(
        self,
        access_key_id: None = None,
        access_key_secret: None = None,
        bucket: None = None,
        endpoint: None = None,
        role_arn: None = None,
        role_session_name: None = None,
    ) -> None:
        self.access_key_id = access_key_id or config.get('OSS_STS.access_key_id')
        self.access_key_secret = access_key_secret or config.get('OSS_STS.access_key_secret')
        self.bucket_name = bucket or config.get('OSS_STS.bucket')
        self.endpoint = endpoint or config.get('OSS_STS.endpoint')
        self.role_arn = role_arn or config.get('OSS_STS.role_arn')
        self.role_session_name = role_session_name or config.get('OSS_STS.role_session_name')
        self._region_id = None
        self._auth = None
        self.temp_token = None
        self.security_token = None

    def auth(self):
        if self._auth:
            return self._auth

        self.get_security_token()

        self._auth = StsAuth(
            self.temp_token.get('access_key_id'),
            self.temp_token.get('access_key_secret'),
            self.temp_token.get('security_token'),
        )
        return self._auth

    @property
    def region_id(self) -> str:
        """
            从end_point获取 region_id
            Created: 2017/11/10
        """

        if self.endpoint:

            re_str = r'http[s]?://oss-(\w+-\w+)\.aliyun(cs)?\.com'
            match = re.findall(re_str, self.endpoint)
            try:
                if match[0][0]:
                    self._region_id = match[0][0]
                    return self._region_id

            except Exception:

                UserError(message="oss endpoint setting not find")
        return ''

    def get_security_token(self) -> Dict[str, Union[int, str]]:

        clt = client.AcsClient(self.access_key_id, self.access_key_secret, self.region_id)
        req = AssumeRoleRequest.AssumeRoleRequest()

        req.set_accept_format('json')
        req.set_RoleArn(self.role_arn)
        req.set_RoleSessionName(self.role_session_name)

        body = clt.do_action_with_exception(req)

        j = json.loads(body.decode('utf-8'))

        token = {
            'Arn': j.get('AssumedRoleUser').get('Arn'),
            'AssumedRoleId': j.get('AssumedRoleUser').get('AssumedRoleId'),
            'access_key_id': j['Credentials']['AccessKeyId'],
            'access_key_secret': j['Credentials']['AccessKeySecret'],
            'security_token': j['Credentials']['SecurityToken'],
            'request_id': j['RequestId'],
            'endpoint': self.endpoint,
            'expiration': utils.to_unixtime(j['Credentials']['Expiration'], '%Y-%m-%dT%H:%M:%SZ'),
            'bucket': self.bucket_name,
            'oss_disable': False,
        }

        # access_key_id: 临时用户的access key id
        # access_key_secret: 临时用户的access key secret
        # expiration: 过期时间，UNIX时间，自1970年1月1日UTC零点的秒数
        # security_token: 临时用户Token
        # request_id: 请求ID
        # 参考：https://github.com/aliyun/aliyun-oss-python-sdk/blob/master/examples/sts.py
        # ?spm=5176.doc32033.2.9.LNcJaP&file=sts.py

        self.temp_token = token

        return token


class ObsBrowserJsFile(OSSFile):
    """ Summary
     使用oss sts  上传下载文件
    Attributes:
         :
    """

    def __init__(
        self,
        access_key_id: None = None,
        access_key_secret: None = None,
        bucket: None = None,
        endpoint: None = None,
    ) -> None:
        self.access_key_id = access_key_id or config.get('OBS.access_key_id')
        self.access_key_secret = access_key_secret or config.get('OBS.access_key_secret')
        self.bucket_name = bucket or config.get('OBS.bucket')
        self.endpoint = endpoint or config.get('OBS.endpoint')

    def auth(self):
        return None

    @property
    def region_id(self) -> str:
        return None

    def get_security_token(self) -> Dict[str, Union[int, str]]:
        token = {
            'access_key_id': self.access_key_id,
            'access_key_secret': self.access_key_secret,
            'endpoint': self.endpoint,
            'bucket': self.bucket_name,
            'oss_disable': True,
        }
        return token


class MinioJsFile(OSSFile):
    """ Summary
     使用oss sts  上传下载文件
    Attributes:
         :
    """

    def __init__(
        self,
    ) -> None:
        pass

    def auth(self):
        pass

    @property
    def region_id(self) -> str:
        pass

    def get_security_token(self) -> Dict[str, Union[int, str]]:
        return {"oss_disable": True}
