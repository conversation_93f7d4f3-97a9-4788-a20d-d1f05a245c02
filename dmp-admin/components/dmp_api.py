#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/1/26.
"""
import logging
import re
import requests
from requests.exceptions import RequestException

from dmplib import config
from dmplib.conf_constants import DBINSTALLER_SAAS_ENV_URL
from dmplib.utils.errors import UserError
from base.enums import ProjectAction


class DMPAPI:
    """
    DMP接口类
    """

    __slots__ = ['host', 'project_code', 'retries']

    def __init__(self, project_code, retries = -1):
        """
        :param str project_code:
        """
        self.host = config.get("Dmp.host", "http://dp-bi-server:8000")
        self.project_code = project_code
        self.retries = retries

    def update_operation_dataset_flow(self, status):
        """
        开启或关闭运营数据清洗调度
        """
        route_url = '/openapi/dataset/operation/flow'
        params = {
            "project_code": self.project_code,
            "status": int(status)
        }
        return self._api_request(route_url, params)

    def refresh_dataset_is_complex(self):
        """
        刷新升级过来的数据集是否是复杂sql
        """
        route_url = '/openapi/fresh/dataset/is_complex'
        params = {}
        return self._api_request(route_url, params, request_type='GET', timeout=120)

    def init_hd_local_project(self, from_init="", app_code_list=None, init_1_5_app=None):
        """
        初始化HD本地模式相关表数据
        """
        route_url = '/openapi/hd/local/init'
        if not app_code_list:
            app_code_list = []
        params = {
            "from_init": from_init, "app_code_list": app_code_list,
            "init_1_5_app": init_1_5_app, "others": {"saas_url": DBINSTALLER_SAAS_ENV_URL}
        }
        return self._api_request(route_url, params, timeout=120)

    def init_dataset_run(self):
        """
        触发数据集自动调度
        """
        route_url = '/openapi/init/dataset/run'
        return self._api_request(route_url, request_type='GET')

    def sort_self_service_report_list(self, **kwargs):
        """
        给自助 报表刷默认排序
        :param kwargs:
        :return:
        """
        route_url = '/openapi/self_service/report_list/sort'
        return self._api_request(route_url, kwargs, request_type='GET')

    def flow_delete_schedule(self, flow_id):
        """
        获取表数据
        :param flow_id
        :return:
        """
        route_url = '/openapi/flow/delete/schedule'
        params = {}
        if flow_id:
            params['flow_id'] = flow_id
        return self._api_request(route_url, params)

    def flow_enable_ignore_error(self, flow_id, queue_name=None):
        try:
            self.flow_enable(flow_id, queue_name)
        except Exception as e:
            logging.error(f'flow_enable调用失败:{str(e)}')

    def flow_enable(self, flow_id, queue_name):
        """
        获取表数据
        :param flow_id
        :param queue_name
        :return:
        """
        route_url = '/openapi/flow/enable'
        params = {}
        if flow_id:
            params['flow_id'] = flow_id
        if queue_name:
            params['queue_name'] = queue_name
        return self._api_request(route_url, params)

    def flow_run_ignore_error(self, flow_id, queue_name=None):
        try:
            self.flow_run(flow_id, queue_name)
        except Exception as e:
            logging.error(f'flow_run调用失败:{str(e)}')

    def flow_run(self, flow_id, queue_name=None):
        """
        执行流程
        :param flow_id
        :param queue_name
        :return:
        """
        route_url = '/openapi/flow/run'
        params = {}
        if flow_id:
            params['flow_id'] = flow_id
        if queue_name:
            params['queue_name'] = queue_name
        return self._api_request(route_url, params)

    def rabbitmq_purge(self, queue):
        """
        purge queue
        """
        route_url = '/openapi/rabbitmq/purge'
        params = {"queue_name": queue}
        return self._api_request(route_url, params)

    def delete_dashboard(self, dashboard_ids, retry_count=0):
        """
        删除报告，回收报告
        :param dashboard_ids
        :return:
        """
        if not dashboard_ids:
            raise UserError(message="参数dashboard_ids不能为空")

        route_url = '/openapi/dashboard_chart/delete_dashboard'
        params = {"dashboard_ids": dashboard_ids}
        try:
            return self._api_request(route_url, params)
        except Exception as e:
            raise UserError(message=str(e))

    def _api_request(self, route_url, parameters=None, timeout=None, request_type='POST', retry_count = 0):
        """
        请求API
        :param parameters:
        :param int timeout:
        :return:
        """

        if not self.validation_url(self.host):
            raise UserError(message='无效的url：' + self.host)
        try:
            headers = {'X-TENANT': self.project_code}
            self.host = self.host[:len(self.host) - 1] if self.host.endswith('/') else self.host
            if request_type == 'POST':
                response = requests.post(self.host + route_url, json=parameters, timeout=timeout or 30, headers=headers)
            else:
                response = requests.get(self.host + route_url, params=parameters, timeout=timeout or 30,
                                        headers=headers)
            if response.status_code != 200:
                raise UserError(message='url：{url},error:{error},'
                                        'status_code:{status_code}'.format(url=self.host + route_url,
                                                                           error=response.reason,
                                                                           status_code=response.status_code))
            return response.json()
        except ValueError:
            raise UserError(message='密钥错误。')
        except BaseException as be:
            if self.retries >= retry_count:
                retry_count += 1
                logging.error(f'请求DMP失败，正在第{retry_count}次重试，URL:{self.host + route_url}, error:{str(be)}')
                return self._api_request(route_url, parameters, timeout, request_type, retry_count)
            else:
                raise UserError(message='连接失败:' + str(be))


    @staticmethod
    def validation_url(url):
        """
        校验url合法性
        :param url:
        :return:
        """
        if re.match(r'^https?:/{2}\w.+$', url):
            return True
        else:
            return False

    def refresh_self_service(self, retry_count=0):
        """
        刷新自助报告元数据
        :param retry_count
        :return:
        """
        route_url = '/openapi/self_service/refresh_all'
        params = {"project_code": self.project_code, "async": True}
        try:
            return self._api_request(route_url, params, request_type="GET")
        except Exception as e:
            retry_count += 1
            if retry_count <= 3:
                self.refresh_self_service(retry_count)
            else:
                raise UserError(message=str(e))

    def get_dashboard_released_data(self, dashboard_id, retry_count=3):
        """
        刷新自助报告元数据
        :param retry_count
        :return:
        """
        route_url = '/openapi/get_release_dashboard_data'
        params = {"dashboard_id": dashboard_id}
        try:
            return self._api_request(route_url, params, request_type="GET", timeout=60)
        except Exception as e:
            retry_count += 1
            if retry_count <= 3:
                return self.get_dashboard_released_data(retry_count)
            else:
                raise UserError(message=str(e))

    def move_dataset(self, dataset_id, target_dataset_id, retry_count=3):
        """
        移动数据集
        :param dataset_id:
        :param target_dataset_id:
        :param retry_count:
        :return:
        """
        route_url = '/openapi/move/dataset'
        params = {"dataset_id": dataset_id, "target_dataset_id": target_dataset_id}
        try:
            return self._api_request(route_url, params, request_type="POST", timeout=60)
        except Exception as e:
            retry_count += 1
            if retry_count <= 3:
                return self.move_dataset(dataset_id, target_dataset_id)
            else:
                raise UserError(message=str(e))

    def move_dashboard(self, dash_id, target_dash_id, retry_count=3):
        """
        移动数据集
        :param dash_id:
        :param target_dash_id:
        :param retry_count:
        :return:
        """
        route_url = '/openapi/move/dashboard'
        params = {"dash_id": dash_id, "target_dash_id": target_dash_id}
        try:
            return self._api_request(route_url, params, request_type="POST", timeout=60)
        except Exception as e:
            retry_count += 1
            if retry_count <= 3:
                return self.move_dashboard(dash_id, target_dash_id)
            else:
                raise UserError(message=str(e))

    def pulsar_dataset_sync(self, retry_count=3):
        """
        数芯数据集同步
        :param project_code:
        :param retry_count:
        :return:
        """
        route_url = '/openapi/pulsar_dataset/sync'
        params = {"tenant_code": self.project_code}
        try:
            return self._api_request(route_url, params, request_type="get", timeout=60)
        except Exception as e:
            retry_count += 1
            if retry_count <= 3:
                return self.pulsar_dataset_sync(self.project_code)
            else:
                raise UserError(message=str(e))

    def reg_key_dashboard_task(self):
        """
        开启租户重点大屏管理功能时，注册该环境所有租户重点大屏统计任务
        :return:
        """
        route_url = '/openapi/reg/key_dashboard_task'
        return self._api_request(route_url)

    def get_old_erp_report_list(self):
        """
        数芯数据集同步
        :param project_code:
        :param retry_count:
        :return:
        """
        route_url = '/openapi/old_erp_report_list'
        return self._api_request(route_url)

    def flow_enable_by_project(self, action=None):
        """
        租户所有启用的数据集, 看板拍照, 手工填报 调度任务注册
        :param action: 租户操作场景。init：开租户，enable：租户启用，disable：租户禁用
        :return:
        """
        route_url = '/openapi/flow/enable_by_project'
        # 默认开租户场景
        if not action:
            action = ProjectAction.Init.value
        params = {"action": action}
        return self._api_request(route_url, params)

    def add_tenant_mip_config(self, data):
        """
        添加租户的集成平台参数配置
        :return:
        """
        route_url = '/openapi/add/tenant_mip_config'
        return self._api_request(route_url, request_type='POST', parameters=data)

    def add_superportal_info(self, data):
        """
        添加超级APP参数
        :return:
        """
        route_url = '/openapi/add_superportal_info'
        return self._api_request(route_url, request_type='POST', parameters=data)

    def fix_dashboard_level_code(self, params):
        """
        修复level_code
        :return:
        """
        route_url = '/openapi/fix_dashboard_level_code'
        return self._api_request(route_url, request_type='GET', parameters=params)

    def fix_dashboard_level_code_batch(self, params):
        """
        批量修复level_code
        :return:
        """
        route_url = '/openapi/fix_dashboard_level_code_batch'
        return self._api_request(route_url, request_type='POST', parameters=params)

    def sync_application(self, application_id_list):
        """
        批量发布的第三方鉴权门户
        :param application_id_list
        :return:
        """
        if not application_id_list:
            raise UserError(message="参数application_id_list不能为空")

        route_url = '/openapi/portal/sync_application'
        params = {"application_ids": application_id_list}
        try:
            result = self._api_request(route_url, params, 90)
            logging.info(f'批量发布门户结果：{str(result)}')
            return result
        except Exception as e:
            raise UserError(message=f'调用DMP同步门户异常:{str(e)}')

    def call_export(self, data):
        """
        批量修复level_code
        :return:
        """
        route_url = '/openapi/call_export'
        return self._api_request(route_url, request_type='POST', parameters=data)

    def dataset_sync_mip(self):
        route_url = '/openapi/dataset/dataset_auth_sync_mip'
        return self._api_request(route_url, request_type='POST', parameters={})
