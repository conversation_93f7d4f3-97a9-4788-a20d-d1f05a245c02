import functools
from typing import Callable

from dmplib.context.globals import _AppCtxGlobals, _app_ctx_stack, g
from dmplib.hug.context import DBContext


def set_g_property(to_g, **g_kwargs):
    # 在不同的协程或者线程中，需要处理g的传递（g只能保证线程程安全）
    # 有些属性不能直接带过去，特别是在同时在不同的租户之间切换的时候
    ignores = ['DBContext', 'cache', 'get', 'pop', 'redis_conns', 'storage']
    for key, val in g_kwargs.items():
        if key in ignores:
            continue
        setattr(to_g, key, val)
    to_g.customize_roles = (to_g.customize_roles or []) if hasattr(to_g, 'customize_roles') else []


def get_g_property():
    # 获取g的关键属性
    return {key: getattr(g, key, None) for key in dir(g) if not (key.startswith('__') and key.endswith('__'))}


def handle_g_auto(func: Callable) -> Callable:
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread_local_g = _AppCtxGlobals()
        g_kwargs = kwargs.pop('g', {})
        set_g_property(thread_local_g, **g_kwargs)  # noqa
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()

    return wrapper
