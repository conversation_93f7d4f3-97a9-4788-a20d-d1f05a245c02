import requests
from loguru import logger
from requests import Session

from dmplib.hug import g  # noqa: E402
from base.enums import FlowStatus
from base.models import BaseModel
from base.enums import FlowType
from components.base_schedule import BaseScheduler
from dmplib import config, redis
from dmplib.utils.errors import UserError

DEFAULT_GROUP_ID_CACHE_KEY = 'XXLJOB_DEFAULT_GROUP_ID'


class XxlScheduler(BaseScheduler):

    def __init__(self, flow_model=None):
        super().__init__(flow_model)
        self.xxljob = XxlJob.get()
        self.executor_handler = ''
        self.alarm_email = config.get('XXL-JOB.alarm_email', '')
        self.executor_timeout = config.get('XXL-JOB.executor_timeout', 60)
        self.executor_fail_retry = config.get('XXL-JOB.executor_fail_retry', 3)

    @staticmethod
    def add_default_group():
        group = JobGroupModel()
        group.appname = config.get('XXL-JOB.default_group_appname', 'dmp-task-executor')
        group.title = config.get('XXL-JOB.default_group_title', '异步任务')
        group.addressType = 0
        try:
            xxljob = XxlJob.get()
            res = xxljob.add_group(group)
        except Exception as e:
            logger.error(f'xxljob默认执行器添加失败:{str(e)}')
            # raise e

    @property
    def default_group_id(self):
        conn = redis.RedisCache()
        default_group_id = conn.get(DEFAULT_GROUP_ID_CACHE_KEY)
        if default_group_id:
           return default_group_id
        default_group_name = config.get('XXL-JOB.default_group_appname', 'dmp-task-executor')
        records = self.xxljob.query_group(default_group_name)
        if not records:
            raise UserError(message=f'默认执行器[{default_group_name}]不存在')
        default_group_id = records[0].get('id')
        conn.set(DEFAULT_GROUP_ID_CACHE_KEY, default_group_id, 7 * 24 * 3600)
        return default_group_id

    def parser_command(self, command):
        command = command or ''
        if (
            not command and self.flow_model.type in [
            FlowType.DashboardSnap.value, FlowType.Download.value, FlowType.ManualFilling.value
        ]) or 'celery_producer.py' in command:
            executor_handler = 'celeryJobHandler'
            params = command.split('celery_producer.py')[-1] if command else f'{self.project_code} {self.flow_model.id} {self.queue_name or ""}'
        elif not command and self.queue_name:
            executor_handler = 'flowJobHandler'
            params = f'{self.project_code} {self.flow_model.id} {self.queue_name or ""}'
        else:
            executor_handler = 'flowJobHandler'
            params = command.split('app_producer.py')[-1] if command else f'{self.project_code} {self.flow_model.id} {self.queue_name or ""}'
        return executor_handler, params.strip()

    def _convert_to_job_model(self, command=None, job_id=None):
        executor_handler, params = self.parser_command(command)
        job = JobModel()
        job.jobDesc = job_id or self.job_id
        job.jobGroup = self.default_group_id
        job.author = getattr(g, 'account', 'dmp')
        job.alarmEmail = self.alarm_email
        job.cronGen_display = self.flow_model.schedule
        job.scheduleConf = self.flow_model.schedule
        job.schedule_conf_CRON = self.flow_model.schedule
        job.executorHandler = executor_handler
        job.executorParam = params
        job.executorTimeout = self.executor_timeout
        job.executorFailRetryCount = self.executor_fail_retry
        return job

    @property
    def job_id(self):
        return self.project_code + '_' + self.flow_model.id

    def job_is_exists(self, job_id=None):
        job_id = self.job_id if not job_id else job_id
        return self.xxljob.is_job_exists(job_id, self.default_group_id)

    def add_job(self, command=None, job_id=None):
        job = self._convert_to_job_model(command, job_id)
        if not job.schedule_conf_CRON:
            return
        return self.xxljob.add_job(job)

    def update_job(self, command=None, job_id=None):
        if self.flow_model.status == FlowStatus.Disable.value:
            return self.stop_job(job_id)
        else:
            job = self._convert_to_job_model(command, job_id)
            return self.xxljob.update_job(job)

    def stop_job(self, job_id=None):
        job_id = self.job_id if not job_id else job_id
        return self.xxljob.stop_job(job_id, self.default_group_id)

    def delete_job(self, job_id=None):
        job_id = self.job_id if not job_id else job_id
        return self.xxljob.remove_job(job_id, self.default_group_id)

    def run_job(self):
        return self.xxljob.trigger_now(self.job_id, self.default_group_id, None)

    def upset_job(self, job_id, name, schedule, command, schedule_enabled: bool = True, **kwargs):
        executor_handler, params = self.parser_command(command)
        job = JobModel()
        job.jobDesc = job_id
        job.jobGroup = self.default_group_id
        job.author = getattr(g, 'account', 'dmp')
        job.alarmEmail = self.alarm_email
        job.cronGen_display = schedule
        job.scheduleConf = schedule
        job.schedule_conf_CRON = schedule
        job.executorHandler = executor_handler
        job.executorParam = params
        job.executorTimeout = self.executor_timeout
        job.executorFailRetryCount = self.executor_fail_retry
        return self.xxljob.add_job(job)

class JobModel(BaseModel):

    def __init__(self, **kwargs):
        self.id = None
        self.jobGroup = None
        self.jobDesc = ""
        self.author = ""
        self.alarmEmail = ""
        self.scheduleType = "CRON"
        self.scheduleConf = ""
        self.cronGen_display = ""
        self.schedule_conf_CRON = ""
        self.glueType = "BEAN"
        self.executorHandler = ""
        self.executorParam = ""
        self.executorRouteStrategy = "ROUND"
        self.misfireStrategy = "FIRE_ONCE_NOW"
        self.executorBlockStrategy = "SERIAL_EXECUTION"
        self.executorTimeout = 60
        self.executorFailRetryCount = 3
        super().__init__(**kwargs)


class JobGroupModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = None
        self.appname = ""
        self.title = ""
        self.addressType = 0
        self.addressList = ""


LOGIN_URI = '/xxl-job-admin/login'
JOB_QUERY_URI = '/xxl-job-admin/jobinfo/pageList'
JOB_ADD_URI = '/xxl-job-admin/jobinfo/add'
JOB_UPDATE_URI = '/xxl-job-admin/jobinfo/update'
JOB_REMOVE_URI = '/xxl-job-admin/jobinfo/remove'
JOB_STOP_URI = '/xxl-job-admin/jobinfo/stop'
JOB_START_URI = '/xxl-job-admin/jobinfo/start'
JOB_TRIGGER_URI = '/xxl-job-admin/jobinfo/trigger'
GROUP_ADD_URI = '/xxl-job-admin/jobgroup/save'
GROUP_UPDATE_URI = '/xxl-job-admin/jobgroup/update'
GROUP_QUERY_URI = '/xxl-job-admin/jobgroup/pageList'
XXLJOB_ADMIN_LOGIN_URI = '/xxl-job-admin/toLogin'


TOKEN_COOKIE_KEY = 'XXL_JOB_LOGIN_IDENTITY'

class XxlJob():

    __instance = None

    @staticmethod
    def get():
        if XxlJob.__instance:
            return XxlJob.__instance
        else:
            XxlJob.__instance = XxlJob()
            return XxlJob.__instance

    def __init__(self):
        self.host=config.get('XXL-JOB.admin_host')
        self.username=config.get('XXL-JOB.admin_username')
        self.password=config.get('XXL-JOB.admin_password')
        self.token = ''
        self.session:Session = None
        self._do_login()

    def add_group(self, group:JobGroupModel):
        records = self.query_group(group.appname)
        uri = GROUP_ADD_URI
        if records:
            #如果执行器已存在则进行修改
            group.id = records[0].get('id')
            uri = GROUP_UPDATE_URI

        resp = self._do_request(uri, data=group.get_dict())
        res = resp.json()
        if res.get('code') == 200:
            return True
        else:
            raise UserError(message=f'xxljob执行器添加失败:code={res.get("code")},msg={res.get("msg")}"')

    def query_group(self, appname):
        resp = self._do_request(GROUP_QUERY_URI, data={'appname': appname, 'start': 0, 'length': 10})
        res = resp.json()
        return res.get('data', [])

    def add_job(self, job:JobModel):
        records = self.query_job(job.jobDesc, job.jobGroup)
        uri = JOB_ADD_URI
        if records:
            uri = JOB_UPDATE_URI
            job.id = records[0].get('id')
        resp = self._do_request(uri, data=job.get_dict(), method='POST')
        res = resp.json()
        if res.get('code') == 200:
            if job.id:
                self.start_job_by_id(job.id)
            else:
                self.start_job_by_id(int(res.get("content")))
            return True
        else:
            raise UserError(message=f'xxljob任务[{job.jobDesc}]添加失败:code={res.get("code")},msg={res.get("msg")}"')

    def is_job_exists(self, desc:str, group_id:int):
        return len(self.query_job(desc, group_id)) > 0

    def query_job(self, desc:str, group_id:int):
        resp = self._do_request(JOB_QUERY_URI, data={'jobGroup': group_id, 'triggerStatus': -1, 'jobDesc': desc, 'start':0, 'length': 10}, method='POST')
        res = resp.json()
        return res.get('data', [])

    def update_job(self, job: JobModel):
        records = self.query_job(job.jobDesc, job.jobGroup)
        if not records:
            raise UserError(message=f'xxljob任务[{job.jobDesc}]修改失败:任务不存在')

        for record in records:
            job.id = record.get('id')
            resp = self._do_request(JOB_UPDATE_URI, data=job.get_dict(), method='POST')
            res = resp.json()
            if res.get('code') != 200:
                raise UserError(message=f'xxljob任务[{job.jobDesc}]修改失败:code={res.get("code")},msg={res.get("msg")}"')
            self.start_job_by_id(job.id)
        return True

    def remove_job(self, desc:str, group_id:int):
        records = self.query_job(desc, group_id)
        if not records:
            return True
        for record in records:
            job_id = record.get('id')
            resp = self._do_request(JOB_REMOVE_URI, data={'id': job_id}, method='DELETE')
            res = resp.json()
            if res.get('code') != 200:
                raise UserError(message=f'xxljob任务[{desc}]删除失败:code={res.get("code")},msg={res.get("msg")}"')
        return True


    def stop_job(self, desc:str, group_id:int):
        records = self.query_job(desc, group_id)
        if not records:
            return True
        for record in records:
            job_id = record.get('id')
            resp = self._do_request(JOB_STOP_URI, data={'id': job_id}, method='POST')
            res = resp.json()
            if res.get('code') != 200:
                raise UserError(message=f'xxljob任务[{desc}]停止失败:code={res.get("code")},msg={res.get("msg")}"')
        return True

    def start_job_by_id(self, job_id):
        resp = self._do_request(JOB_START_URI, data={'id': job_id}, method='POST')
        res = resp.json()
        if res.get('code') != 200:
            raise UserError(message=f'xxljob任务[{job_id}]启动失败:code={res.get("code")},msg={res.get("msg")}"')
        return True


    def start_job(self, desc:str, group_id:int):
        records = self.query_job(desc, group_id)
        if not records:
            return True
        for record in records:
            job_id = record.get('id')
            resp = self._do_request(JOB_START_URI, data={'id': job_id}, method='POST')
            res = resp.json()
            if res.get('code') != 200:
                raise UserError(message=f'xxljob任务[{desc}]启动失败:code={res.get("code")},msg={res.get("msg")}"')
        return True

    def trigger_now(self, desc:str, group:int, params:str):
        records = self.query_job(desc, group)
        if not records:
            raise UserError(message=f'任务[{desc}]不存在')
        for record in records:
            job_id = record.get('id')
            resp = self._do_request(JOB_TRIGGER_URI, data={'id': job_id, 'executorParam': params}, method='POST')
            res = resp.json()
            if res.get('code') != 200:
                raise UserError(message=f'xxljob任务[{desc}]执行失败:code={res.get("code")},msg={res.get("msg")}"')

    def _do_login(self):
        #登录清空session
        if self.session:
            self.session = None
        resp = self._do_request(LOGIN_URI, {'userName': self.username, 'password': self.password})
        self.token = resp.cookies.get(TOKEN_COOKIE_KEY)
        #设置session cookie
        self._set_session_cookie()

    def _set_session_cookie(self):
        #设置token到cookie
        self.session.cookies.set(TOKEN_COOKIE_KEY,self.token)

    def _do_request(self, uri, data=None, params=None ,json=None, header={}, method:str='POST', refreshed_token=False):
        url = self.host + uri
        if not self.session:
            self.session = requests.session()
        try:
            if method.upper() == 'POST':
                resp = self.session.post(url, data=data, params=params, json=json, headers=header)
            elif method.upper() == 'DELETE':
                resp = self.session.delete(url, data=data, params=params, json=json, headers=header)
            else:
                resp = self.session.get(url, data=data, params=params, json=json, headers=header)
        except Exception as e:
            err = f'xxljob请求异常:{str(e)}'
            logger.error(err)
            raise UserError(message=err)

        if XXLJOB_ADMIN_LOGIN_URI in resp.url:
            if not refreshed_token:
                logger.error('xxljob token无效被重定向到登录页，正在重新登录')
                self._do_login()
                return self._do_request(uri, data, params, json, header, method, True)
            else:
                raise UserError(message='xxljob token无效')

        if resp.status_code != 200:
            err = f'xxljob请求响应异常: {resp.text}'
            logger.error(err)
            #如果token过期登录后重试
            if not refreshed_token and resp.status_code == 401:
                self._do_login()
                return self._do_request(uri, data, params, json, header, method, True)
            else:
                raise UserError(message=err)

        return resp
