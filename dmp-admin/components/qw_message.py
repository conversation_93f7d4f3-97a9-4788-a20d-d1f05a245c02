import datetime
import os
import time

import requests

from base.constants import PUBLISHCENTER_EMAILS
from components import mail
from dmplib import config
from loguru import logger

from dmplib.components import auth_util
from dmplib.redis import RedisCache


class QWMessage:
    __slots__ = ['url', 'message']

    def __init__(self, url, message):
        self.url = url
        self.message = message

    def send(self):
        parameters = {
            "msgtype": "markdown",
            "markdown": {
                "content": self.message
            }
        }
        requests.post(self.url, json=parameters, timeout=10)

publish_center_lock_cache = RedisCache(key_prefix='publish_center_send_msg_lock')


def send_publish_center_error(task_id, tenant, app_code, error, error_traceback=""):
    identifier = str(time.time())
    if publish_center_lock_cache.setnx(task_id, identifier):
        publish_center_lock_cache.expire(task_id, 300)  # 五分钟

        env_code = auth_util.get_env_code()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        send_qy_message(env_code, current_time, task_id, tenant, app_code, error)
        send_email(env_code, current_time, task_id, tenant, app_code, error_traceback or error)


def send_qy_message(env_code, current_time, task_id, tenant, app_code, error, ):
    msg = """
            # <font color=\"red\">【更新中心推送-更新失败】</font>\n
            ————————详情————————\n
            >环境：{0} \n
            >更新任务ID: {1} \n
            >应用code：{3} \n
            >租户code：{2} \n
            >时间：{4} \n
            ><font color=\"red\">异常信息：{5}</font> \n
            """.format(env_code, task_id, app_code, tenant, current_time, error)
    try:
        url = config.get("PublishCenter.qy_url", "")
        if url:
            QWMessage(url, msg).send()
    except Exception as e:
        logger.error(f"企业微信消息发送异常，errs：{str(e)} , ")


def send_email(env_code, current_time, task_id, tenant, app_code, error):
    msg = """
                # <font color=\"red\">【更新中心推送-更新失败】</font><br>
                ————————详情————————<br>
                >环境：{0} <br>
                >更新任务ID: {1} <br>
                >应用code：{3} <br>
                >租户code：{2} <br>
                >时间：{4} <br>
                ><font color=\"red\">异常信息：{5}</font> <br>
                """.format(env_code, task_id, app_code, tenant, current_time, error)
    try:
        emails = PUBLISHCENTER_EMAILS
        if emails:
            if not isinstance(emails, list):
                emails = emails.split(';')
            receiver = [mail.MailContact(name='', mail=email) for email in emails]
            mail.send(
                mail.Mail(
                    subject="更新中心-推送更新-异常",
                    body=msg,
                    receiver=receiver
                ),
                code=tenant,
                subtype='html'
            )
    except Exception as e:
        logger.error(f'邮件发送异常:{str(e)}')