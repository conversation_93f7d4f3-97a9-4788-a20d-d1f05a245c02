import json
import logging
import os
import re
import time

import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3 import Retry

from base import repository
from base.constants import APP_NAME
from dmplib.conf_constants import RECORD_API_LOG, DEFAULT_SELFSERVICE_KEY, DEFAULT_SELFSERVICE_SECRET
from dmplib.saas.project import get_db
from base.enums import DataSourceType
from dmplib import config
from dmplib.components import auth_util
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.hug import g
from dmplib.locale import trans_msg
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


class Pulsar15Api(object):

    def __init__(self, _from="datasource", host="", app_key="", app_secret="", pulsar_key="", pulsar_code="",
                 set_token_cache=True, datasource_name="",
                 user_id="", account="", tenant_code=""):
        self._from = _from
        self.tenant_code = tenant_code
        self.host = host or self.shuxin_conn_str.get("api_host")
        self.pattern = r"(?P<value>\$\{(.*?)\})"
        if auth_util.is_env_enable_skyline_auth():
            # 如果host为空或为变量表达式，则从配置中心获取
            if not self.host or re.match(self.pattern, self.host):
                self.host = AppHosts.get(SkylineApps.DAP, True) or self.host
        self.app_key = app_key or ''
        self.app_secret = app_secret or ''
        self.pulsar_key = pulsar_key or ''
        self.pulsar_code = pulsar_code or ''
        self.cache = RedisCache(key_prefix="indicator_model")
        self.__check_params()
        self.set_api_attr()
        # 请求接口是否缓存token
        self.set_token_cache = set_token_cache
        self.datasource_name = datasource_name
        self.user_id = user_id
        self.account = account

    def _try_parse_host(self):
        if self.host:
            if re.match(self.pattern, self.host):
                self.host = re.sub(self.pattern, self.replace_func, self.host) or self.host
                return True

    def __check_params(self):
        if not self.host:
            raise UserError(message=trans_msg("缺少数芯地址参数"))
        self.host = re.sub(self.pattern, self.replace_func, self.host) or self.host

    def __get_secret_of_project(self):
        data = repository.get_data(
            'dap_bi_tenant_setting', {'code': g.code}, ['pulsar_app_key', 'pulsar_app_secret']
        )
        key = DEFAULT_SELFSERVICE_KEY if not data.get('pulsar_app_key') else data.get('pulsar_app_key')
        secret = DEFAULT_SELFSERVICE_SECRET if not data.get('pulsar_app_secret') else data.get(
            'pulsar_app_secret')
        return key, secret, ""

    def __get_secret_of_datasource(self):
        app_id = self.shuxin_conn_str.get("app_id")
        app_secret = self.shuxin_conn_str.get("app_secret")
        app_key = self.shuxin_conn_str.get("app_key")
        return app_id, app_secret, app_key

    @staticmethod
    def replace_func(match):
        key = match.group(2) or ''
        value = config.get('ThirdDatasource.{}'.format(key))
        if value:
            return value

    @property
    def shuxin_conn_str(self):
        cache = RedisCache()
        key = 'MysoftShuXin15:data_source'
        data_source = cache.get(key)
        if data_source:
            data_source = json.loads(data_source.decode())
        else:
            data_source = get_db(self.tenant_code).query_one("select * from `dap_m_data_source` where type=%(type)s",
                                                             {'type': DataSourceType.MysoftShuXin15.value})
        if not data_source:
            raise UserError(message=trans_msg("未配置数芯数据源"))
        cache.set(key=key, value=json.dumps(data_source), time=3600)
        try:
            conn_str = json.loads(data_source.get("conn_str"))
            return conn_str
        except Exception as e:
            raise UserError(message=trans_msg("数芯数据源conn_str配置错误")) from e

    def set_api_attr(self):
        if self._from == 'datasource':
            app_id, app_secret, app_key = self.__get_secret_of_datasource()
        elif self._from in ["test_connect", "del_source"]:
            app_id, app_secret, app_key = self.app_key, self.app_secret, self.pulsar_key
        else:
            app_id, app_secret, app_key = self.__get_secret_of_project()

        self.app_key = app_id
        self.app_secret = app_secret
        self.pulsar_key = app_key
        # # 做替换操作
        if not auth_util.is_env_enable_skyline_auth():
            self.app_key = re.sub(self.pattern, self.replace_func, self.app_key) or self.app_key
            self.app_secret = re.sub(self.pattern, self.replace_func, self.app_secret) or self.app_secret
            self.pulsar_key = re.sub(self.pattern, self.replace_func, self.pulsar_key) or self.pulsar_key

    @property
    def api_token(self):
        # key = f"{self.pulsar_code}:pulsar_api:{self.app_key}:token"
        key = f"{self.pulsar_code}:pulsar_api{self.app_key}:{self.pulsar_key}:token"
        token = self.cache.get(key)
        if token:
            return token.decode()

        params = {
            "app_key": self.pulsar_key,
            "app_secret": self.app_secret,
            "app_id": self.app_key
        }
        session = self.__retry_session(max_retry=2)
        try:
            timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
        except Exception as e:
            logger.error(f"get timeout error: {e}")
            timeout = 30
        try:
            rsp = session.get(
                url=f'{self.host}/open-api/tag/archive-data/token_api/get',
                params=params, timeout=timeout,
                headers=self.headers)
            rsp.encoding = "utf-8"
            res = rsp.json()
            token, expires_in = res.get('data', {}).get("token"), int(res.get("data", {}).get("expires_in"))
            # token是否缓存。在数芯数据源编辑时，可能会输入错误的秘钥，这是不缓存token值
            # 默认设置缓存
            if self.set_token_cache:
                self.cache.set(key, token, int(expires_in / 2))
            return token
        except Exception as e:
            logger.error(f"请求数芯老接口open-api/tag/archive-data/token_api/get报错: {e}，切换到api_token_v15接口")
            return self.api_token_v15

    @property
    def api_token_v15(self):
        # key = f"{self.pulsar_code}:pulsar_api:{self.app_key}:token_v15"
        key = f"{self.pulsar_code}:pulsar_api{self.app_key}:{self.pulsar_key}:token_v15"
        token = self.cache.get(key)
        if token:
            return token.decode()
        params = {
            "app_key": self.pulsar_key,
            "app_secret": self.app_secret,
            "app_id": self.app_key
        }

        session = self.__retry_session(max_retry=2)
        try:
            timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
        except Exception as e:
            logger.error(f"get timeout error: {e}")
            timeout = 30
        rsp = session.get(
            url=f'{self.host}/token_api/get',
            params=params, timeout=timeout,
            headers=self.headers)
        rsp.encoding = "utf-8"
        res = rsp.json()
        token, expires_in = res.get('data', {}).get("token"), int(res.get("data", {}).get("expires_in"))

        # token是否缓存。在数芯数据源编辑时，可能会输入错误的秘钥，这是不缓存token值
        # 默认设置缓存
        if self.set_token_cache:
            self.cache.set(key, token, int(expires_in / 2))
        return token

    @staticmethod
    def add_params(params: dict, **kwargs):
        """
        添加参数
        :param params:
        :param kwargs:
        :return:
        """
        if params is None:
            params = {}
        for key, value in kwargs.items():
            if not key or not value:
                continue
            if isinstance(value, list):
                value = ",".join(value)
            params[key] = value

    @property
    def headers(self):
        headers = {
            "Content-Type": "application/json"
        }
        if auth_util.is_env_enable_skyline_auth():
            headers[auth_util.AUTHORIZATION_KEY] = auth_util.gen_auth_token()
        return headers

    @staticmethod
    def __retry_session(max_retry: int = 2):
        retry = Retry(
            total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1,
            status_forcelist=(500, 503)
        )
        adapter = HTTPAdapter(max_retries=retry)
        session = requests.session()
        session.mount('https://', adapter)
        session.mount('http://', adapter)
        return session

    def __get(self, url: str, params: dict, cookies: dict = None, max_retry: int = 2, timeout: int = None,
              token_version: str = "v1"):
        result = ''
        is_success = False
        st = time.time()
        if not params:
            params = {}
        try:
            if not auth_util.is_env_enable_skyline_auth() or not self.app_key.startswith("${"):  # 兼容开发环境配置的数芯地址
                _token = self.api_token_v15 if token_version == 'v1.5' else self.api_token
                params.update({"jwt": _token})
            if not timeout:
                timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
            session = self.__retry_session(max_retry)
            rsp = session.get(url=url, params=params, cookies=cookies, timeout=timeout, headers=self.headers)
            rsp.encoding = "utf-8"
            if rsp.status_code != 200:
                msg = f"call pulsar api error: {rsp.text}"
                logger.exception(msg)
                raise UserError(message=msg)
            result = rsp.text
            res_data = rsp.json()
            if res_data.get('errCode') not in [0, "0"]:
                raise UserError(
                    message=trans_msg("明源数芯1.5数据源：{} 请求异常 ：{}").format(self.datasource_name, res_data))
            is_success = True
            return res_data
        except UserError as e:
            result = trans_msg('明源数芯1.5数据源：{} 请求异常 {}').format(self.datasource_name, str(e))
            raise e from e
        except Exception as e:
            msg = trans_msg('明源数芯1.5数据源：{} 请求异常 , 错误原因: {},{}').format(self.datasource_name, str(e),
                                                                                      result)
            logger.exception(msg)
            result = msg
            raise UserError(message=msg) from e
        finally:
            ed = time.time()
            self.fast_logger_record(url, params, cookies, result, is_success, st, ed)

    def get_business_subject(self):
        """
        获取所有类目
        https://apifox.com/apidoc/shared-e9726831-ba0c-4de9-934d-2600ae8b1e7f/api-25841738
        :param class_id:
        :return:
        """
        params = {
            'tenant_code': self.tenant_code
        }
        res = self.__get(
            url=f"{self.host}/openapi/dmp/get_business_subject",
            params=params
        )
        return res.get("data") or []

    @staticmethod
    def fast_logger_record(url, params, cookies, result, is_success, st, ed):
        """
        日志记录天眼
        :param url:
        :param params:
        :param cookies:
        :param result:
        :param is_success:
        :return:
        """
        try:
            start_time = int(st * 1000)
            end_time = int(ed * 1000)
            duration = end_time - start_time

            log_data = {
                "action": "request_pulsar",
                "api_url": url,
                "start_time": str(start_time),
                "end_time": str(end_time),
                "duration": str(duration),
                "api_param": json.dumps(params, ensure_ascii=False) if params else '',
                "is_success": "1" if is_success else "0",
                "api_result": result,
                "cookies": json.dumps(cookies, ensure_ascii=False) if cookies else '',
                "org_code": g.code if hasattr(g, 'code') else "",
                "account": g.account if hasattr(g, 'account') else "",
                "dashboard_id": getattr(g, 'dashboard_id_of_query', ''),
                "dashboard_name": getattr(g, 'log_dashboard').get('name', '') if getattr(g, 'log_dashboard',
                                                                                         '') else '',
                "dataset_id": getattr(g, 'dataset_id_of_query', ''),
                "trace_id": getattr(g, 'trace_id_of_query', ''),
                "sql_from": getattr(g, 'sql_from', 'viewreport') or 'viewreport'
            }
            is_record_log = RECORD_API_LOG
            is_yunqing = int(config.get('App.is_deploy_in_yunqing', 0))
            if is_record_log and is_yunqing:
                log_data["env_code"] = auth_util.get_env_code()
                log_data["app_name"] = APP_NAME
                from app_celery import upload_log_to_aliyun
                upload_log_to_aliyun.apply_async(
                    kwargs={
                        "log_data": [tuple([k, v]) for k, v in log_data.items()]
                    },
                    queue='celery'
                )
            from dmplib.components.fast_logger import FastLogger
            FastLogger.ApiFastLogger(**log_data).record()
        except Exception as e:
            logger.error("记录API请求日志失败：" + str(e))
