#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/22.
"""
import copy
from urllib.parse import urljoin
from loguru import logger
import requests

import xmltodict
from requests.exceptions import HTTPError
from rundeck.client import Rundeck
from dmplib import config
from dmplib.conf_constants import DEFAULT_RUNDECK_PORT, DEFAULT_RUNDECK_TOKEN, DEFAULT_RUNDECK_SERVER_NAME, \
    DEFAULT_RUNDECK_PROJECT_NAME
from dmplib.utils.errors import UserError

from base.models import BaseModel


class Job(BaseModel):
    __slots__ = [
        'id',
        'name',
        'description',
        'group',
        'schedule',
        'sequence',
        'retry',
        'dispatch',
        'logging',
        'schedule_enabled',
        'execution_enabled',
        'nodes_selected_by_default',
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = None
        self.group = None
        self.schedule = None
        self.sequence = None
        self.retry = 1
        self.dispatch = JobDispatch()
        self.logging = JobLog()
        self.schedule_enabled = True
        self.execution_enabled = True
        self.nodes_selected_by_default = True
        super().__init__(**kwargs)
        self.schedule_convert_model()

    def schedule_convert_model(self):
        if not isinstance(self.schedule, str) or isinstance(self.schedule, JobSchedule):
            return
        self.schedule = JobSchedule(crontab=self.schedule)

    def get_definition_xml_str(self):
        job = copy.deepcopy(self)
        if job.sequence and isinstance(job.sequence, JobSequence) and isinstance(job.sequence.command, JobCommand):
            job.sequence.command = job.sequence.command.get_dict()
            job.sequence = job.sequence.get_dict()
        if job.schedule and isinstance(job.schedule, JobSchedule):
            job.schedule = job.schedule.get_definition_dict()
        if job.dispatch and isinstance(job.dispatch, JobDispatch):
            job.dispatch = job.dispatch.get_definition_dict()

        tmp_dict = job.get_dict(['id', 'name', 'description', 'group', 'schedule', 'sequence', 'retry', 'dispatch'])
        tmp_dict['uuid'] = job.id
        tmp_dict['scheduleEnabled'] = str(job.schedule_enabled).lower()
        tmp_dict['executionEnabled'] = str(job.execution_enabled).lower()
        tmp_dict['nodesSelectedByDefault'] = str(job.nodes_selected_by_default).lower()

        if job.logging and isinstance(job.logging, JobLog):
            tmp_dict['logging'] = job.logging.get_definition_dict()
            tmp_dict['loglevel'] = job.logging.level
            job.logging = job.logging.get_definition_dict()
        tmp_dict['logging'] = {'limit': '100M', 'limitAction': 'halt'}

        return xmltodict.unparse({'joblist': {'job': tmp_dict}})


class JobSequence(BaseModel):
    __slots__ = ['command']

    def __init__(self, **kwargs):
        self.command = None
        super().__init__(**kwargs)


class JobCommand(BaseModel):
    __slots__ = ['exec', 'description']

    def __init__(self, **kwargs):
        """
        命令内容
        :param kwargs:
        """
        self.exec = None
        self.description = None
        super().__init__(**kwargs)


class JobSchedule(BaseModel):
    __slots__ = ['crontab']

    def __init__(self, **kwargs):
        """
        Job调度
        :param kwargs:
        """
        self.crontab = None
        super().__init__(**kwargs)

    def get_definition_dict(self):
        """
        crontab需要设置为xml 属性
        :return:
        """
        return {'@crontab': self.crontab}


class JobLog(BaseModel):
    __slots__ = ['level', 'limit', 'limit_action']

    def __init__(self, **kwargs):
        self.level = 'INFO'
        self.limit = '10M'
        self.limit_action = 'halt'
        super().__init__(**kwargs)

    def get_definition_dict(self):
        return {'limit': self.limit, 'limitAction': self.limit_action}


class JobDispatch(BaseModel):
    __slots__ = ['exclude_precedence', 'keep_going', 'rank_order', 'thread_count']

    def __init__(self, **kwargs):
        self.exclude_precedence = True
        self.keep_going = True
        self.rank_order = 'ascending'
        self.thread_count = 1
        super().__init__(**kwargs)

    def get_definition_dict(self):
        return {
            'excludePrecedence': str(self.exclude_precedence).lower(),
            'keepgoing': str(self.keep_going).lower(),
            'rankOrder': self.rank_order,
            'threadcount': self.thread_count,
        }


class RundeckNew:

    def __init__(self, rundeck_url, token=None, api_version=18):
        self.token = token
        self.rundeck_url = rundeck_url
        self.api_version = api_version

    def __request(
        self, method, url, params=None, upload_file=None, format="xml"
    ):
        """
        rundck接口文档：https://docs.rundeck.com/docs/api/rundeck-api.html
        """
        url = url.strip() if url else url
        if not url.startswith("http"):
            url = urljoin(self.rundeck_url, url)
        logger.info("{} {} Params: {}".format(method, url, params))
        h = {
            "Content-Type": "application/{}".format(format),
            "X-Rundeck-Auth-Token": self.token,
        }
        options = {
            "headers": h,
        }
        if method == "GET":
            options["params"] = params
        elif upload_file is not None:
            options["data"] = upload_file
            options["headers"]["Content-Type"] = "octet/stream"
        else:
            options["data"] = params

        r = requests.request(method, url, **options)
        logger.debug(r.text)
        r.raise_for_status()
        if format == "json":
            try:
                return r.json()
            except ValueError as e:
                logger.error(e)
                return r.text
        else:
            return r.text

    def __get(self, url, params=None, format="xml"):
        valid_format = ["json", "xml", "yaml"]
        if format not in valid_format:
            raise ValueError(
                "Invalid Format. Possible Values are: {}".format(
                    " ,".join(valid_format)
                )
            )
        return self.__request("GET", url, params, format=format)

    def __post(self, url, params=None, upload_file=None):
        return self.__request("POST", url, params, upload_file, format='xml')

    def __delete(self, url, params=None):
        return self.__request('DELETE', url, params, format='xml')

    def export_job(self, job_id):
        return self.__get(url=f"/api/11/job/{job_id}")

    def import_job(self, job_definition, project, dupeOption='update'):
        return self.__post(
            url=f"/api/14/project/{project}/jobs/import?dupeOption={dupeOption}",
            params=job_definition.encode("utf-8")
        )

    def delete_job(self, job_id):
        return self.__delete(url=f"/api/11/job/{job_id}")

    def get_project_history(self, project):
        return self.__get(url=f"/api/14/project/{project}/history", format='json')

    def system_info(self):
        return self.__get(url='/api/14/system/info', format='json')

    def run_job(self, job_id):
        return self.__post(url=f"/api/11/job/{job_id}/run")

    def list_job_executions(self, job_id):
        return self.__get(url=f"/api/11/job/{job_id}/executions")


def get_rundeck_client():
    server = DEFAULT_RUNDECK_SERVER_NAME
    port = DEFAULT_RUNDECK_PORT
    token = DEFAULT_RUNDECK_TOKEN
    client = RundeckNew(
        rundeck_url=f"http://{server}:{port}",
        token=token
    )
    return client


class CommonRunDeckScheduler:
    """
    通用注册RunDeck Job类
    """

    def __init__(self):
        self.rundeck_project_name = DEFAULT_RUNDECK_PROJECT_NAME
        self.rundeck = get_rundeck_client()

    def job_is_exists(self, job_id):
        """
        调度任务是否存在
        :return:
        """
        try:
            self.rundeck.export_job(job_id)
            return True
        except HTTPError as e:
            if e.response.status_code == 404:
                return False
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))

    def delete_job(self, job_id):
        """
        删除调度任务
        :return:
        """
        if not self.job_is_exists(job_id):
            return True
        try:
            self.rundeck.delete_job(job_id)
            return True
        except AttributeError as e:
            # 客户端内部bug，实际任务已经被删除
            print(str(e))
            return True
        except HTTPError as e:
            if e.response.status_code == 404:
                return True
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))

    def upset_job(self, job_id, name, schedule, command, schedule_enabled: bool = True, **kwargs):
        """
        注册或更新RunDeck Job定时调度任务
        :param id: 唯一ID，标识Job
        :param name: Job名称
        :param schedule: 调度信息，如每个月的1号凌晨2点定时执行："0 0 2 1 * ? *"
        :param command: 调度执行的命令
        :param schedule_enabled: 是否开启调度: True开启, False禁用
        :param kwargs:
            description : str
                Job描述
            group: str
                Job分组
        :return:
        """

        dupe_option = 'update' if self.job_is_exists(job_id) else 'create'
        job_data = {
            'id': job_id,
            'name': name,
            'group': kwargs.get('group', '') or name,
            'description': kwargs.get('description', '') or name,
            'schedule': schedule,
            'schedule_enabled': schedule_enabled,
        }
        job = Job(**job_data)
        job.sequence = JobSequence(command=JobCommand(description=job_data['description'], exec=command))
        try:
            self.rundeck.import_job(
                job.get_definition_xml_str(), project=self.rundeck_project_name, dupeOption=dupe_option
            )
        except HTTPError as e:
            raise UserError(message='添加调度任务失败 ' + str(e))
        except Exception as e:
            raise UserError(message='添加调度任务失败 ' + str(e))

    def run_job(self, job_id=None):
        """
        立即执行job
        :param job_id:
        :return:
        """
        if not job_id:
            return False
        try:
            res = self.rundeck.run_job(job_id)
            return res
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
