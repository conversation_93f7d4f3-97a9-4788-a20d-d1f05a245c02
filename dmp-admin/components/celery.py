#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/21.
"""
import sys
import os
import logging

from celery import Celery
from dmplib import config
from dmplib.redis import redis_mode
from dmplib.constants import REDIS_SENTINEL, REDIS_CLUSTER


logger = logging.getLogger(__name__)


def get_backend(db=6, is_result=False):
    if (config.get('App.celery_broker') == 'rabbitmq' and config.get('RabbitMQ.type') != 'tonghtp') or redis_mode() == REDIS_CLUSTER:
        backend = "{transport}://{user}:{password}@{host}:{port}/{vhost}".format(
            transport='amqp' if not is_result else 'rpc',
            user=config.get('RabbitMQ.user'),
            password=config.get('RabbitMQ.password'),
            host=config.get('RabbitMQ.host'),
            port=config.get('RabbitMQ.port'),
            vhost=config.get('RabbitMQ.vhost')
        )
    else:
        if redis_mode() == REDIS_SENTINEL:
            backend = "sentinel://{username}:{password}@{host}:{port}/{db}".format(
                password=config.get('Redis.password') or '',
                username='',
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                db=db,
            )
        else:
            backend = 'redis://{username}:{password}@{host}:{port}/{db}'.format(
                password=config.get('Redis.password') or '',
                username=config.get('Redis.username') or '',
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                db=db,
            )
    return backend


class CeleryTask:
    def __init__(self, name=None, backend=None, broker=None):
        self.name = name or 'dmp_admin_celery'
        self.broker = backend or get_backend(config.get('Redis.db') or 6)
        self.backend = 'rpc://{username}:{password}@{host}:{port}//'.format(
            username=config.get('RabbitMQ.user'), password=config.get('RabbitMQ.password'),
            host=config.get('RabbitMQ.host'), port=config.get('RabbitMQ.port'))
        self._celery = None

    @property
    def celery(self):
        if self._celery:
            return self._celery
        self._celery = Celery(self.name, backend=self.backend, broker=self.broker)
        broker_transport_options = {'visibility_timeout': 18000, "confirm_publish": True,
                                    "worker_max_tasks_per_child": 200}
        result_transport_options = {}
        if config.get('App.celery_broker') != 'rabbitmq' and redis_mode() == REDIS_SENTINEL:
            svc_name = config.get("Redis.svc_name") or "mymaster"
            broker_transport_options.update({'master_name': svc_name})
            result_transport_options.update({'master_name': svc_name})
        self._celery.conf.update(
            redis_scheduler_url=self.backend,
            task_ignore_result=True,
            worker_max_tasks_per_child=200,
            broker_transport_options=broker_transport_options,
            result_backend_transport_options=result_transport_options,
            broker_connection_retry_on_startup=True
        )
        if config.get('App.celery_broker') != 'rabbitmq' and redis_mode() == REDIS_SENTINEL:
            svc_name = config.get("Redis.svc_name") or "mymaster"
            self._celery.conf.update(
                broker_transport_options={'master_name': svc_name},
                result_backend_transport_options={'master_name': svc_name},
            )
        default_queue_name = 'admin-celery'
        if default_queue_name:
            self._celery.conf.update(task_default_queue=default_queue_name)

        try:
            # body大于10M则丢掉
            send_task_message = self._celery.amqp.send_task_message

            def new_send_task_message(producer, name, message, *args, **kwargs):
                size = sys.getsizeof(message)/1024/1024
                limit = int(config.get('RabbitMQ.body_limit') or 10)
                if size > limit:
                    logger.error(f"current celery message size: {size}M, message: {message}")
                    return
                return send_task_message(producer, name, message, *args, **kwargs)

            self._celery.amqp.send_task_message = new_send_task_message
        except Exception as e:
            logger.error(f"add celery body limit error: {e}")

        return self._celery


celery = CeleryTask().celery


def get_task_id(module_name, task_name, project_code, flow_id):
    return 'dmp-admin:{module_name}:{task_name}:{project_code}:{flow_id}'.format(module_name=module_name,
                                                                                 task_name=task_name,
                                                                                 project_code=project_code,
                                                                                 flow_id=flow_id)
