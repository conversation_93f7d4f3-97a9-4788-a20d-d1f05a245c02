from dmplib.components import auth_util
from dmplib.nacos_client import NacosClient


def get_lang_tag():
    # 业务的归档包目前并未区分（与作辉沟通确认过），暂时先注释
    # if auth_util.is_env_enable_skyline_auth():
    #     res = NacosClient.get('i18n_enable')
    #     #目前只区分中文和英文，中文返回空
    #     if res and res in ['true', True]:
    #         return 'en_US'
    return ''


def splicing_lang_tag(s:str, separator='_'):
    tag = get_lang_tag()
    if not tag:
        return s
    return s + separator + tag
