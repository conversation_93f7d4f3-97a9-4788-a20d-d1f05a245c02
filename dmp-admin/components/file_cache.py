import os
from threading import Lock


class FileCache:
    def __init__(self, cache_dir='.cache'):
        self.cache_dir = cache_dir
        self.lock = Lock()
        self._mkdir()

    def _mkdir(self):
        os.makedirs(self.cache_dir, exist_ok=True)

    def _get_cache_path(self, key):
        return os.path.join(self.cache_dir, str(key))

    def set(self, key, value):
        path = self._get_cache_path(key)

        with self.lock:
            fd = os.open(path, os.O_WRONLY | os.O_CREAT | os.O_TRUNC)
            with os.fdopen(fd, 'w') as f:
                f.write(value)

    def get(self, key):
        path = self._get_cache_path(key)

        with self.lock:

            try:
                with open(path, 'r') as f:
                    return f.read()
            except FileNotFoundError:
                pass

        return None

