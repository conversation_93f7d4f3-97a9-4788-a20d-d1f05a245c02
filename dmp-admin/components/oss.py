#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/21.
"""
import os
import io
from datetime import timedelta
from abc import ABCMeta, abstractmethod
from loguru import logger

from oss2.api import Bucket
from oss2.auth import Auth
from oss2.exceptions import OssError
from minio import Minio
from minio.error import InvalidResponseError
from urllib.parse import urlparse, unquote
from dmplib import config
from dmplib.hug.middlewares import Parser
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from obs import ObsClient, Object, DeleteObjectsRequest, GetObjectHeader
from components.mime_types import get_mime_type


class BaseOSS(metaclass=ABCMeta):
    """
    存储对象服务基类
    """

    def url_to_obj_key(self, oss_file_url, bucket=None):
        """
        从OSS URL地址获取object key
        :param str oss_file_url:
        :return:
        """
        if bucket:
            start_index = oss_file_url.find(bucket + '/')
            # 如果找到了，返回从起始值开始到结束的部分
            if start_index != -1:
                return (oss_file_url[start_index:]).lstrip('/')
        return urlparse(oss_file_url).path.lstrip('/')

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    @abstractmethod
    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def download_file_list(self, root, local_dir, max_keys=None):
        raise NotImplementedError('Not implemented!')

    @staticmethod
    def guess_mime_type(file_name):
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        return get_mime_type(suffix=extension)

    @abstractmethod
    def object_list(self, root, max_keys=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def delete_file_list(self, root, max_keys=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def get_object(self, key, is_url=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def delete(self, key, is_url=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def sign_url(self, key, is_url=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def get_object_meta(self, key):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def get_file_size(self, key):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def get_partial_object(self, key, begin, end):
        raise NotImplementedError('Not implemented!')


class OSSFileProxy:
    def __init__(self, access_key_id=None, access_key_secret=None, bucket=None, endpoint=None, service=None):
        self.service = service or config.get('OSS_Config.service')
        self._auth = None
        self._bucket = None
        self.proxy = None
        if self.service == 'OSS':
            self.proxy = OSSFile(
                access_key_id=access_key_id, access_key_secret=access_key_secret, bucket=bucket, endpoint=endpoint
            )
        elif self.service.upper() in ['MINIO', 'OBS']:
            self.proxy = MinioService(
                access_key_id=access_key_id, access_key_secret=access_key_secret, bucket=bucket, endpoint=endpoint
            )
        # elif self.service == "OBS":
        #     self.proxy = OBSService(access_key_id=access_key_id, access_key_secret=access_key_secret, bucket=bucket, endpoint=endpoint)
        else:
            raise UserError("配置不存在")

    @property
    def auth(self):
        return self.proxy.auth

    @property
    def bucket(self):
        if self.service == 'OSS':
            return self.proxy.bucket
        elif self.service.upper() in ['MINIO', 'OBS']:
            return self.proxy.minio
        # elif self.service == 'OBS':
        #     return self.proxy.obs
        else:
            raise UserError("配置不存在")

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        return self.proxy.upload(
            file, root=root, file_name=file_name, max_size=max_size, allow_extensions=allow_extensions, **kwargs
        )

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if is_url:
            key = unquote(key)
        return self.proxy.delete(key, is_url=is_url)

    def object_list(self, root=None, max_keys=None):
        return self.proxy.object_list(root=root, max_keys=max_keys)

    @staticmethod
    def url_to_obj_key(oss_file_url):
        """
        从OSS URL地址获取object key
        :param str oss_file_url:
        :return:
        """
        return urlparse(oss_file_url).path.lstrip('/')

    def download_file_list(self, root, local_dir, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        return self.proxy.download_file_list(root, local_dir, max_keys=max_keys)

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        return self.proxy.delete_file_list(root, max_keys=max_keys)

    def get_object(self, key, is_url=None):
        """get oss object

        Args:
            key (string): url or key
            is_url (bool, optional): Defaults to None. if url

        Returns:
            oss2.models.GetObjectResult:
        """
        if is_url:
            key = unquote(key)
        return self.proxy.get_object(key, is_url=is_url)

    def get_sigh_url(self, key, timeout=None, is_url=None, **kwargs):
        if is_url:
            key = unquote(key)
        return self.proxy.sign_url(key, timeout, is_url=is_url, **kwargs)

    def get_object_content(self, resource_url):
        return self.proxy.get_content(resource_url)

    def get_object_meta(self, key):
        """
        获取元数据
        :param key:
        :return:
        """
        return self.proxy.get_object_meta(key)

    def get_file_size(self, key):
        """
        获取文件大小
        :param key:
        :return:
        """
        return self.proxy.get_file_size(key)

    def get_partial_object(self, key, begin, end):
        """
        分段获取文件
        :param key:
        :param begin:
        :param end:
        :return:
        """
        return self.proxy.get_partial_object(key, begin, end)


class OSSFile(BaseOSS):
    def __init__(self, access_key_id=None, access_key_secret=None, bucket=None, endpoint=None):
        super(OSSFile, self).__init__()
        self.access_key_id = access_key_id or config.get('OSS.access_key_id')
        self.access_key_secret = access_key_secret or config.get('OSS.access_key_secret')
        self.bucket_name = bucket or config.get('OSS.bucket')
        self.endpoint = endpoint or config.get('OSS.endpoint')
        self._auth = None
        self._bucket = None

    @property
    def auth(self):
        if self._auth:
            return self._auth
        self._auth = Auth(self.access_key_id, self.access_key_secret)
        return self._auth

    @property
    def bucket(self):
        if self._bucket:
            return self._bucket
        self._bucket = Bucket(self.auth, self.endpoint, self.bucket_name)
        return self._bucket

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        key = kwargs.get('key')
        _file_name = ''
        if isinstance(file, Parser):
            _file_name = file.filename
            file = file.file
        if not file_name:
            file_name = seq_id() + os.path.splitext(os.path.split(_file_name or file.name)[-1])[-1]
        self.validate_file_extension(file_name, allow_extensions)
        if not key:
            key = ('%s/%s' % (root or '', file_name)).strip('/')
        try:
            data = file.read()
            if max_size and len(data) > max_size:
                raise UserError(message="文件大小超过{}M限制".format(max_size / (1024 * 1024)))
            headers = {}
            if kwargs.get('private'):
                headers["x-oss-object-acl"] = "private"
            put_obj_result = self.bucket.put_object(key, data, headers=headers)
        except OssError as e:
            raise UserError(message='上传文件失败：' + e.message)
        if put_obj_result.status == 200:
            return unquote(put_obj_result.resp.response.url)
        raise UserError(message='文件上传失败')

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if not key:
            return False
        if is_url:
            key = self.url_to_obj_key(key)
        try:
            if not self.bucket.object_exists(key):
                return False
            self.bucket.delete_object(key)
            return True
        except OssError as e:
            raise UserError(message='删除文件失败：' + e.message)

    def object_list(self, root, max_keys=None):
        return self.bucket.list_objects(root, max_keys=max_keys or 100).object_list

    @staticmethod
    def url_to_obj_key(oss_file_url):
        """
        从OSS URL地址获取object key
        :param str oss_file_url:
        :return:
        """
        return urlparse(oss_file_url).path.lstrip('/')

    def download_file_list(self, root, local_dir, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        object_infos = []
        for file_info in self.object_list(root, max_keys):
            file_name = os.path.join(local_dir, os.path.split(file_info.key)[-1])
            if not self.bucket.get_object_meta(file_info.key).content_length:
                continue
            self.bucket.get_object_to_file(file_info.key, file_name)
            object_infos.append(file_info)
        return object_infos

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        key_list = [o.key for o in self.object_list(root, max_keys)]
        if not key_list:
            return False
        self.bucket.batch_delete_objects(key_list)
        return True

    def get_object(self, key, is_url=None):
        """get oss object

        Args:
            key (string): url or key
            is_url (bool, optional): Defaults to None. if url

        Returns:
            oss2.models.GetObjectResult:
        """

        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key)
        if not self.bucket.object_exists(key):
            return None
        return self.bucket.get_object(key)

    def sign_url(self, key, is_url=None, **kwargs):
        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key)
        if not self.bucket.object_exists(key):
            return None
        return self.bucket.sign_url('GET', key, int(config.get('JWT.expires', 600)))

    def get_content(self, resource_url):
        return self.get_object(resource_url, True).resp.response.content

    def get_object_meta(self, key):
        if not key:
            return None
        return self.bucket.get_object_meta(key)

    def get_file_size(self, key):
        if not key:
            return None
        meta_data = self.get_object_meta(key)
        if not meta_data:
            return None
        return meta_data.content_length

    def get_partial_object(self, key, begin, end):
        if not key:
            return None
        return self.bucket.get_object(key, byte_range=(begin, end))


class MinioService(BaseOSS):
    def __init__(self, access_key_id=None, access_key_secret=None, bucket=None, endpoint=None):
        super(MinioService, self).__init__()
        self.access_key_id = access_key_id or config.get('Minio.access_key_id')
        self.access_key_secret = access_key_secret or config.get('Minio.access_key_secret')
        self.bucket_name = bucket or config.get('Minio.bucket')
        self.endpoint = endpoint or config.get('Minio.endpoint')
        self.secure = bool(self.endpoint.startswith('https'))
        self._auth = None
        self._bucket = None
        if not self.has_path(self.endpoint):
            #带path的地址实例化会报错
            self.minio = Minio(
                endpoint=self.endpoint.split('//')[1],
                access_key=self.access_key_id,
                secret_key=self.access_key_secret,
                secure=self.secure,
            )
        # 如果配置了内网地址，上传优先使用内网地址
        self.intranet_host = config.get('Minio.inside_endpoint', None)
        self.intranet_minio = None
        if self.intranet_host:
            intranet_secure = bool(self.intranet_host.startswith('https'))
            self.intranet_minio = Minio(
                endpoint=self.intranet_host.split('//')[1],
                access_key=self.access_key_id,
                secret_key=self.access_key_secret,
                secure=intranet_secure,
            )


    def has_path(self, url):
        # 解析 URL
        parsed_url = urlparse(url)
        # 检查路径部分是否存在
        return bool(parsed_url.path)



    @property
    def endpoint_sub(self):
        obj = urlparse(self.endpoint)
        if obj.path != '/' and obj.path.startswith('/'):
            return obj.path
        else:
            return ''

    @property
    def prue_endpoint(self):
        return urlparse(self.endpoint).netloc

    @property
    def outer_minio(self):
        # 配置https://myerp.v55-standard.mycyjg.com/mfs这样的地址使用
        region = self.intranet_minio._get_region(self.bucket_name)
        path = self.endpoint_sub
        if path:
            # 说明带路径
            prue_endpoint = self.prue_endpoint
            mio = Minio(
                endpoint=prue_endpoint,
                access_key=self.access_key_id,
                secret_key=self.access_key_secret,
                secure=self.secure,
                region=region,
            )
            old_open = mio._http.urlopen

            def new_urlopen(method, url, redirect=True, **kw):
                url = url.replace(prue_endpoint, prue_endpoint + path)
                return old_open(method, url, redirect=redirect, **kw)

            mio._http.urlopen = new_urlopen
            return mio
        else:
            return Minio(
                endpoint=self.endpoint.split('//')[1],
                access_key=self.access_key_id,
                secret_key=self.access_key_secret,
                secure=self.secure,
            )

    @property
    def auth(self):
        return self._auth

    @property
    def bucket(self):
        if not self._get_intranet_client_first().bucket_exists(self.bucket_name):
            self._get_intranet_client_first().make_bucket(self.bucket_name)
        return self.bucket_name

    def _get_intranet_client_first(self):
        return self.intranet_minio if self.intranet_minio else self.minio

    @staticmethod
    def get_proxy_url(sign_url, proxy_domain):
        try:
            sign_url_netloc = urlparse(sign_url).netloc
            proxy_netloc = urlparse(proxy_domain).netloc
            proxy_url = sign_url.replace(sign_url_netloc, proxy_netloc)
        except Exception as e:
            raise UserError(str(e))
        return proxy_url

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        key = kwargs.get('key')
        return_intranet_url = kwargs.get('return_intranet_url', False)
        _file_name = ''
        if isinstance(file, Parser):
            _file_name = file.filename
            file = file.file
        if not file_name:
            file_name = seq_id() + os.path.splitext(os.path.split(_file_name or file.name)[-1])[-1]
        self.validate_file_extension(file_name, allow_extensions)
        if not key:
            key = ('%s/%s' % (root or '', file_name)).strip('/')
        # 添加前缀目录
        key_path = config.get("Minio.root_path", "dp") or 'dp'
        key_path = key_path.strip('/')
        key = f'{key_path}/{key}'.strip('/')
        try:
            data = file.read()  # get bytes type data
            raw_file = io.BytesIO(data)  # minio accept BytesIO object
            if max_size and len(data) > max_size:
                raise UserError(message="文件大小超过{}M限制".format(max_size / (1024 * 1024)))
            headers = {}
            if kwargs.get('private'):
                headers["x-oss-object-acl"] = "private"
            content_type = "application/octet-stream"
            auto_mime = kwargs.pop('auto_mime', None)
            if auto_mime:
                content_type = self.guess_mime_type(file_name)
            #优先使用内网上传
            self._get_intranet_client_first().put_object(self.bucket_name, key, raw_file, len(data), content_type=content_type)

            #如果指定返回内网url
            endpoint = self.endpoint
            if return_intranet_url and self.intranet_host:
                endpoint = self.intranet_host
            minio_url = "{endpint}/{bucket}/{key}".format(endpint=endpoint, bucket=self.bucket_name, key=key)
            unquote_url = unquote(minio_url)
            return unquote_url
        except InvalidResponseError as e:
            raise UserError(code=e.code, message='上传文件失败：' + str(e))
        raise UserError(message='文件上传失败')

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    def parse_key(self, key):
        # 原oss逻辑root可能带有bucket_name, Minio自带bucket_name参数, 需要去掉
        key = key[1:] if key.startswith('/') else key
        if key and key.startswith(self.bucket_name):
            key = key.split('/', 1)[1] if '/' in key else key
        return key

    def object_list(self, root=None, max_keys=None):
        # 原oss逻辑root可能带有bucket_name, Minio自带bucket_name参数, 需要去掉
        root = self.parse_key(root)
        objects = self._get_intranet_client_first().list_objects_v2(self.bucket_name, prefix=root, recursive=True)
        return objects

    def download_file_list(self, root=None, local_dir=None, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        object_infos = []
        for file_info in self.object_list(root=root):
            file_name = os.path.join(local_dir, os.path.split(file_info.object_name)[-1])
            # 保持和原oss逻辑一致, 如果文件大小为空则不下载
            if not file_info.size:
                continue
            object_info = self._get_intranet_client_first().fget_object(self.bucket_name, file_info.object_name, file_name)
            object_infos.append(object_info)
        return object_infos

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        key_list = [o.object_name for o in self.object_list(root, max_keys)]
        if not key_list:
            return False
        return self._get_intranet_client_first().remove_objects(self.bucket_name, key_list)

    def get_object(self, key, is_url=None):
        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key, self.bucket_name)
            key = self.parse_key(key)
        try:
            return self._get_intranet_client_first().get_object(self.bucket_name, key)
        except InvalidResponseError as e:
            raise UserError(code=500, message=str(e))

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if not key:
            return False
        if is_url:
            key = self.url_to_obj_key(key, self.bucket_name)
            key = self.parse_key(key)
        try:
            if not self._get_intranet_client_first().get_object(self.bucket_name, object_name=key):
                return False
            self._get_intranet_client_first().remove_object(self.bucket_name, key)
            return True
        except InvalidResponseError as e:
            raise UserError(message='删除文件失败：' + str(e))

    def sign_url(self, key, timeout, is_url=None, **kwargs):
        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key, self.bucket_name)
            key = self.parse_key(key)
            # url去掉？后面的参数
            key = key.split('?')[0] if key.split('?') else key
        try:
            # 会有时区问题
            sign_outer_url = kwargs.get('sign_outer_url')
            timeout = timedelta(seconds=timeout) if timeout else timedelta(days=7)
            if self.intranet_minio and not sign_outer_url:
                minio = self.intranet_minio
            else:
                minio = self.outer_minio
            url = minio.presigned_get_object(
                self.bucket_name, key, expires=timeout
            )
            if sign_outer_url:
                # 指定了生成外部的加签地址
                endpoint_sub_path = self.endpoint_sub
                full_endpoint = self.prue_endpoint + endpoint_sub_path
                # 检查生成的纯域名没有带上后缀，就带上后缀
                if endpoint_sub_path and self.prue_endpoint in url and full_endpoint not in url:
                    url = url.replace(self.prue_endpoint, full_endpoint)
            return url
        except InvalidResponseError as e:
            raise Exception(code=500, message=str(e))

    def get_content(self, resource_url):
        try:
            content = self.get_object(resource_url, True).read()
        except InvalidResponseError as e:
            raise UserError(message='获取资源文件失败：' + str(e))
        return content

    def get_object_meta(self, key):
        if not key:
            return None
        return self._get_intranet_client_first().get_object(self.bucket_name, key)

    def get_file_size(self, key):
        if not key:
            return None
        meta_data = self.get_object_meta(key)
        if not meta_data:
            return None
        return dict(meta_data.headers).get('Content-Length')

    def get_partial_object(self, key, begin, end):
        if not key:
            return None
        object_data = self._get_intranet_client_first().get_partial_object(self.bucket_name, key, offset=begin, length=end - begin)
        return io.BytesIO(object_data.read())


class OBSService(BaseOSS):
    def __init__(self, access_key_id=None, access_key_secret=None, bucket=None, endpoint=None):
        super(OBSService, self).__init__()
        self.access_key_id = access_key_id or config.get('OBS.access_key_id')
        self.access_key_secret = access_key_secret or config.get('OBS.access_key_secret')
        self.bucket_name = bucket or config.get('OBS.bucket')
        self.endpoint = endpoint or config.get('OBS.endpoint')
        self.obs = ObsClient(access_key_id=self.access_key_id, secret_access_key=self.access_key_secret, server=self.endpoint)
        #self.bucket,用户启用obs时，手动创建一个bucket

    @property
    def bucket(self):
        if self.obs.headBucket(self.bucket_name).status == 404:
            self.obs.createBucket(self.bucket_name)
        return self.bucket_name

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        key = kwargs.get('key')
        _file_name = ''
        if isinstance(file, Parser):
            _file_name = file.filename
            file = file.file
        if not file_name:
            file_name = seq_id() + os.path.splitext(os.path.split(_file_name or file.name)[-1])[-1]
        self.validate_file_extension(file_name, allow_extensions)
        if not key:
            key = ('%s/%s' % (root or '', file_name)).strip('/')
        try:
            data = file.read()
            if max_size and len(data) > max_size:
                raise UserError(message="文件大小超过{}M限制".format(max_size / (1024 * 1024)))
            resp = self.obs.putContent(self.bucket_name, key, content=data)
        except OssError as e:
            raise UserError(message='上传文件失败：' + e.message)
        if resp.status < 300:
            return unquote(resp.body.objectUrl)
        raise UserError(message='文件上传失败')

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if not key:
            return False
        if is_url:
            key = self.url_to_obj_key(key)
        try:
            if not self.object_is_exist(key):
                return False
            resp = self.obs.deleteObject(self.bucket_name, key)
        except OssError as e:
            raise UserError(message='删除文件失败：' + e.message)
        if resp.status < 300:
            return True
        raise UserError(message='删除文件失败：' + resp.errorMessage)

    def object_list(self, root, max_keys=None):
        try:
            resp = self.obs.listObjects(self.bucket_name, root, max_keys=max_keys or 100)
        except Exception as e:
            raise UserError(message='获取对象失败：' + e.message)
        if resp.status < 300:
            return resp.body.contents
        raise UserError(message='获取对象失败：')

    def download_file_list(self, root, local_dir, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        object_infos = []
        for file_info in self.object_list(root, max_keys):
            file_name = os.path.join(local_dir, os.path.split(file_info.key)[-1])
            if not file_info.size:
                continue
            resp = self.obs.getObject(self.bucket_name, file_info.key, file_name)
            if resp.status < 300:
                object_infos.append(file_info)
        return object_infos

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        obj_list = [Object(o.key, None) for o in self.object_list(root, max_keys)]
        if not obj_list:
            return False
        resp = self.obs.deleteObjects(self.bucket_name, DeleteObjectsRequest(quiet=False, objects=obj_list))
        if resp.status < 300:
            return True
        return False

    def get_object(self, key, is_url=None):
        """get oss object

        Args:
            key (string): url or key
            is_url (bool, optional): Defaults to None. if url

        Returns:
            oss2.models.GetObjectResult:
        """

        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key)
        if not self.object_is_exist(key):
            return None
        resp = self.obs.getObject(self.bucket_name, key)
        if resp.status < 300:
            return resp.body.response
        return None

    def sign_url(self, key, is_url=None):
        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key)
        if not self.object_is_exist(key):
            return None
        return self.obs.createSignedUrl('GET', self.bucket_name, key, expires=int(config.get('JWT.expires', 7 * 24 * 3600))).signedUrl

    def object_is_exist(self, key):
        return self.obs.getObjectMetadata(self.bucket_name, key).status < 300

    def get_object_meta(self, key):
        if not key:
            return None
        return self.obs.getObjectMetadata(self.bucket_name, key)

    def get_file_size(self, key):
        if not key:
            return None
        meta_data = self.get_object_meta(key)
        if not meta_data:
            return None
        return meta_data.body.contentLength

    def get_partial_object(self, key, begin, end):
        if not key:
            return None
        resp = self.obs.getObject(self.bucket_name, key, headers=GetObjectHeader(range='%d-%d' % (begin, begin)))
        if resp.status < 300:
            return io.BytesIO(resp.body.response.read())
        return None
