#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib import config
from dmplib.config import get_env_code
from project_app.repositories import project_app_repository
from base.constants import INFO_DISABLE_ALL_TENANT_DELIVER_ENV


def get_project_app_list(query_model):
    """
    获取应用配置列表
    :param query_model:
    :return:
    """
    return project_app_repository.get_project_app_list(query_model)


def get_project_app(id):
    if not id:
        raise UserError(message='缺少应用ID')

    project_app = repository.get_data('dap_bi_project_app', {'id': id}, ['id', 'name', 'code', 'default_page_url'])
    project_app_oauth = repository.get_data('dap_bi_project_app_oauth', {'app_id': id}, ['app_id', 'platform', 'corpid', 'app_secret'])
    if not project_app or not project_app_oauth:
        raise UserError(message='应用不存在')

    return project_app + project_app_oauth


def add_project_app(model):
    """
    添加应用配置
    :param mail_template.models.ProjectAppQueryModel model:
    :return: model.id
    """
    # 判断code和name是否已经有值
    add_project = False
    if not repository.data_is_exists('dap_bi_project_app', {'name': model.name, 'code': model.code}):
        model.id = model.app_id = seq_id()
        add_project = True
    else:
        model.id = model.app_id = repository.get_data('dap_bi_project_app', {'name': model.name, 'code': model.code},
                                                      ['id']).get('id')
    model.validate()
    if add_project:
        repository.add_model('dap_bi_project_app', model, ['id', 'name', 'code', 'default_page_url'])
    project_app_oauth = repository.add_model('dap_bi_project_app_oauth', model, ['app_id', 'platform', 'corpid', 'app_secret'])
    if not project_app_oauth:
        raise UserError(message='添加应用失败')
    return model.id


def update_project_app(model):
    """
    更新应用配置
    :param model:
    :return:
    """
    model.validate()
    if not repository.data_is_exists('dap_bi_project_app', {'id': model.id}):
        raise UserError(message='应用不存在')
    repository.update_model('dap_bi_project_app', model, {'id': model.id}, ['id', 'name', 'code', 'default_page_url'])
    repository.update_model('dap_bi_project_app_oauth', model, {'app_id': model.id},
                            ['app_id', 'platform', 'corpid', 'app_secret'])
    return model.id


def delete_project_app(app_id, platform):
    """
    删除应用配置
    :param model:
    :return:
    """
    if not app_id or not repository.data_is_exists('dap_bi_project_app', {'id': app_id}):
        raise UserError(message='缺少应用ID')
    repository.delete_data('dap_bi_project_app_oauth', {'app_id': app_id, 'platform': platform})
    # 判断 project_app_oauth 是否还有值
    if not repository.data_is_exists('dap_bi_project_app_oauth', {'app_id': app_id}):
        repository.delete_data('dap_bi_project_app', {'id': app_id})
    return app_id


def get_project_config(keys):
    data = {}
    for key in keys:
        data[key] = config.get(key)

    data['disable_all_tenant_deliver'] = check_disable_all_tenant_deliver()
    data['one_domain'] = config.get('App.one_domain')
    return data


def check_disable_all_tenant_deliver():
    """
    产业saas，房开saas禁用全部租户分发入口
    :return:
    """
    env_code = get_env_code()
    # 禁用全部租户分发入口的环境列表配置
    disable_all_tenant_deliver_env = INFO_DISABLE_ALL_TENANT_DELIVER_ENV
    env_list = disable_all_tenant_deliver_env.split(',')
    env_code_list = [item.lower() for item in env_list if item]
    if env_code.lower() in env_code_list:
        return True
    return False

