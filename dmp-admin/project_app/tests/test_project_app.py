#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import unittest
from project_app.models import ProjectAppModel, ProjectAppQueryModel
from project_app.services import project_app_service
import logging

from tests.base import BaseTest

logger = logging.getLogger(__name__)


class TestProject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_add_project_app(self):
        dict = {"name": "ceshi2", "code": "gu_testcopy06112", "default_page_url": "default_page_url2", "platform": "platform2",
                "corpid": "corpid2", "app_secret": "app_secret2"}
        model = ProjectAppModel(**dict)
        reuslt = project_app_service.add_project_app(model)
        print(reuslt)

    def test_get_project_app_list(self):
        result = project_app_service.get_project_app_list(ProjectAppQueryModel()).get_result_dict()
        print(result)

    def test_get_project_app(self):
        template_id = '39e709ec-e907-1d9b-5839-89e4edd0864a'
        result = project_app_service.get_project_app(template_id)
        print(result)

    def test_update_project_app(self):
        dict = {"name": "ceshi", "code": "test", "default_page_url": "default_page_url2", "platform": "platform2",
                "corpid": "corpid2", "app_secret": "app_secret2"}
        result = project_app_service.update_project_app(ProjectAppModel(**dict))
        print(result)

    def test_delete_project_app(self):
        result = project_app_service.delete_project_app("39e733bd-991d-9145-3fcc-182b48a76b4b")
        print(result)

    def test_check_disable_all_tenant_deliver(self):


        result = project_app_service.check_disable_all_tenant_deliver()
        print(result)


if __name__ == '__main__':
    unittest.main()
