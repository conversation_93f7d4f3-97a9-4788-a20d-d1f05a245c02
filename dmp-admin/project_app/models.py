#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base.models import BaseModel, QueryBaseModel


class ProjectAppModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = ''
        self.name = ''
        self.code = ''
        self.default_page_url = ''
        self.platform = ''
        self.corpid = ''
        self.app_secret = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        return rules


class ProjectAppQueryModel(QueryBaseModel):
    pass
