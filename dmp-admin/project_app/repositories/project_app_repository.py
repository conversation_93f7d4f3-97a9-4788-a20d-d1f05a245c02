#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.db.mysql_wrapper import get_db


def get_project_app_list(query_model):
    sql = 'SELECT project_app.id,project_app.name,project_app.code,' \
          'project_app.default_page_url,project_app_oauth.platform,project_app_oauth.corpid,' \
          'project_app_oauth.app_secret FROM dap_bi_project_app as project_app INNER JOIN dap_bi_project_app_oauth as project_app_oauth on ' \
          'project_app_oauth.app_id = project_app.id'
    params = {}
    wheres = []
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY project_app.created_on DESC'
    with get_db() as db:
        query_model.total = db.query_scalar('select count(*) as total from ({}) a'.format(sql), params)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model
