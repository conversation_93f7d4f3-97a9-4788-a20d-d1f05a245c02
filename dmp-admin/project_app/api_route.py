#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.hug import APIWrapper
from project_app.models import ProjectAppModel, ProjectAppQueryModel
from project_app.services import project_app_service

api = APIWrapper(__name__)


@api.admin_route.get('/list')
def get_project_apps(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_app/list
     @apiGroup  openapi
     @apiBodyParam {
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": [{
                     "id": "id",
                     "name{名称}": "name",
                     "code":"code",
                     "default_page_url":"default_page_url",
                     "platform":"background",
                     "corpid":"statement",
                     "app_secret": "1",
     }]
     */
     """
    return True, None, project_app_service.get_project_app_list(ProjectAppQueryModel(**kwargs)).get_result_dict()


@api.admin_route.get('/get')
def get_project_app(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_app/get
     @apiGroup  openapi
     @apiBodyParam {
        "id": "id"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": {
                     "id": "id",
                     "name{名称}": "name",
                     "code":"code",
                     "default_page_url":"default_page_url",
                     "platform":"background",
                     "corpid":"statement",
                     "app_secret": "1",
     }
     */
     """
    return True, None, project_app_service.get_project_app(kwargs.get('id'))


@api.admin_route.post('/add')
def add_project_app(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_app/add
     @apiGroup  openapi
     @apiBodyParam {
                     "name{名称}": "name",
                     "code":"code",
                     "default_page_url":"default_page_url",
                     "platform":"background",
                     "corpid":"statement",
                     "app_secret": "1",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "id"
     }
     */
    """
    return True, '添加成功', project_app_service.add_project_app(ProjectAppModel(**kwargs))


@api.admin_route.post('/update')
def update_project_app(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_app/update
     @apiGroup  openapi
     @apiBodyParam {
                     "id": "id",
                     "name{名称}": "name",
                     "code":"code",
                     "default_page_url":"default_page_url",
                     "platform":"background",
                     "corpid":"statement",
                     "app_secret": "1",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "id"
     }
     */
     """
    return True, '修改成功', project_app_service.update_project_app(ProjectAppModel(**kwargs))


@api.admin_route.post('/delete')
def delete_project_app(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_app/delete 删除
     @apiGroup  openapi
     @apiBodyParam {
                     "id": "id",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "id"
     }
     */
     """
    return True, '删除成功', project_app_service.delete_project_app(kwargs.get('id'), kwargs.get('platform'))


@api.admin_route.post('/config')
def get_project_config(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/project_app/config 获取配置
     @apiGroup  openapi
     @apiBodyParam {
            "keys": ["Domain.dmp", "Domain.dmp_admin"]
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "data"
     }
     */
     """
    return True, '获取成功', project_app_service.get_project_config(kwargs.get('keys'))

