import logging
import operator
import time
import traceback
import concurrent.futures
from datetime import datetime, timedelta

import requests
from loguru import logger
import json

from base import repository
from base.enums import OmpTenantsTaskStatus, TenantFrom
from dmplib.components import auth_util
from components.celery import get_task_id
from components.i18n_util import splicing_lang_tag
from components.dmp_api import DMPAPI
from dmplib import config, redis
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps, DBType
from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from project.models import ProjectModel, ProjectInitResultModel, InitializeTenantModel
from project.services import project_tag_service
from upload.services import upload_service


def do_initialize_additional_purchase(model: ProjectModel):
    """
    增购
    """
    from project.services import project_init_service
    from project.services.project_init_service import save_product_tags
    from tenant.services import tenant_service

    if model.from_init == TenantFrom.ERPOP.value:
        return
    record_id = model.create_record_id
    project_code = model.code
    if not record_id:
        # 保存开户记录
        add_project_create_record(model)
        record_id = model.create_record_id

    # model.task_id为omp回调使用的任务id， cache_task_id为dmp缓存中的开户状态
    cache_task_id = get_task_id('project', "additional", project_code, record_id)
    tenant_service.init_open_status(task_id=cache_task_id)
    try:
        if not model.app_code_list:
            msg = '子系统列表为空,增购完成'
            project_init_service.set_omp_task_status(msg, model.task_id, status=OmpTenantsTaskStatus.Successful.value)
            update_create_record_completed(record_id, msg=msg)
            additional_success(cache_task_id, model.task_id, f'租户[{project_code}]无新增子系统')
            return
        model.app_code_list = assign_suit_app(model.app_code_list)
        code_list = [x['app_code'] for x in model.app_code_list]
        exists_codes = []
        if model.overwrite_1_5_app != 1:
            # 只新增之前没有填写的子系统内容
            exists_codes = get_completed_record_appcodes(project_code)
            if exists_codes is None :
                # 为了避免开户失败重试走增购依然失败，如果是最近新增的租户，这里不判断是否存在历史开户记录继续增购
                if is_new_project(project_code):
                    exists_codes = []
                else:
                    msg = f'租户[{project_code}]无历史开户记录，不做增购'
                    logging.error(msg)
                    project_init_service.set_omp_task_status(msg, model.task_id, status=OmpTenantsTaskStatus.Successful.value)
                    update_create_record_completed(record_id, msg=msg)
                    additional_success(cache_task_id, model.task_id, msg)
                    return
            codes = list(set(code_list).difference(set(exists_codes)))
            if not codes:
                msg = f'租户[{project_code}]无新增子系统, 增购完成'
                project_init_service.set_omp_task_status(msg, model.task_id, status=OmpTenantsTaskStatus.Successful.value)
                update_create_record_completed(record_id, msg=msg)
                additional_success(cache_task_id, model.task_id, msg)
                return
        else:
            codes = code_list
        new_app_list = assign_app_name(codes, model.app_code_list)
        project_init_service.set_omp_task_status(f'租户[{project_code}]新增的子系统:{codes}', model.task_id)
        # 分发模板库数据
        tenant_init_model = convert_to_initialize_tenant_model(model)
        tenant_init_model.app_list = new_app_list
        exported_files = do_initialize_data(tenant_init_model)
        # 移除不存在的app_code
        app_list = remove_no_file_appcode(exported_files, new_app_list)
        # 修改开户记录状态
        update_create_record_completed(record_id, app_list)
        # 设置产品标签
        save_product_tags(project_code, merge_exists_app_codes(app_list, exists_codes), model.init_1_5_app)
        # 设置redis状态
        project_init_service.set_omp_task_status('增购完成', model.task_id, status=OmpTenantsTaskStatus.Successful.value)
        additional_success(cache_task_id, model.task_id)
    except Exception as e:
        logging.error(f'租户[{project_code}]进行增购失败:{str(e)}')
        update_create_record_error(record_id, str(e))
        project_init_service.set_omp_task_status(f'增购失败:{str(e)}', model.task_id, status=OmpTenantsTaskStatus.Failure.value, trace=traceback.format_exc())
        tenant_service.update_open_status(task_id=cache_task_id, status='failed', err_msg=str(e))

def is_new_project(tenant_code):
    seven_days_ago = (datetime.now() - timedelta(days=3))
    data = repository.get_data('dap_p_tenant', {'code': tenant_code}, ['created_on'])
    if not data:
        return True
    created_on = data.get('created_on')
    return created_on > seven_days_ago

def merge_exists_app_codes(app_list, exists_codes):
    if not app_list or not exists_codes:
        return app_list
    for e in exists_codes:
        app_list.append({'app_code': e})
    return app_list


def additional_success(cache_task_id, task_id, msg=None):
    from tenant.services import tenant_service
    # 设置redis状态
    set_additional_purchase_cache_status(cache_task_id)
    tenant_service.update_open_status(task_id=cache_task_id, status='success')


def get_files_from_rdc(apps, env_code, customer_guid, db_type):
    codes = [app.get("app_code") for app in apps]
    params = {'db_type': db_type}
    env_condition = ''
    if env_code:
        params['env_code'] = env_code or ''
        env_condition = " and env_code = %(env_code)s"

    # envcode可为空，如果不指定envcode可能会出现一个产品有多个环境的包，按更新时间取最近一条
    data = []
    for app_code in codes:
        params['code'] = app_code
        sql = f"""
            select * from dap_bi_publish_center_template 
                where app_code = %(code)s and db_type = %(db_type)s {env_condition} 
                order by modified_on desc limit 1
        """
        data += repository.get_data_by_sql(sql, params) or []

    result, result_codes = get_files_from_rdc_format_result(data)
    diff_code = list(set(set(codes) - set(result_codes)))
    if not diff_code:
        return result
    sql = """ 
        select * from dap_bi_publish_center_template 
        where app_code in %(code)s and db_type = %(db_type)s and customer_guid = %(customer_guid)s 
        order by modified_on desc
    """
    params = {'code': diff_code, 'customer_guid': customer_guid or '', 'db_type': db_type}
    data = repository.get_data_by_sql(sql, params) or []
    diff_result, result_codes = get_files_from_rdc_format_result(data)
    result.extend(diff_result)

    # 如果获取不到，则尝试获取env_code 是system级别的
    diff_code = list(set(set(diff_code) - set(result_codes)))
    if not diff_code:
        return result
    sql = """ 
        select * from dap_bi_publish_center_template 
        where app_code in %(code)s and db_type = %(db_type)s and env_code = %(env_code)s 
        order by modified_on desc
        """
    params = {'code': diff_code, 'env_code': 'system', 'db_type': db_type}
    data = repository.get_data_by_sql(sql, params) or []
    diff_result, diff_result_codes = get_files_from_rdc_format_result(data)

    result.extend(diff_result)

    return result


def get_files_from_rdc_format_result(data):
    result = []
    result_codes = []
    for row in data:
        if row.get('app_code') not in result_codes:
            result_codes.append(row.get('app_code'))
            result.append({
                'id': row.get('id'),
                'url': row.get('file_url'),
                'app_code': row.get('app_code'),
                'title': '更新服务-'+row.get("app_code")+'-'+row.get("app_key")
            })
    return result, result_codes

def _get_db_type():
    # return config.get('DB.db_type', 'mysql').lower()
    return 'mysql'


def delivery_record_of_app(model, files, apps):
    """
    status: 0 已创建 1 运行中 2 完成
    :param model:
    :param files:
    :param apps:
    :return:
    """
    model = model.get_dict()
    task_id = model.get('task_id')
    app_codes = [export.get('app_code') for export in files]
    exists = repository.get_data('dap_bi_dmp_open_tenant_init_app_record',
                                   {'task_id': task_id, 'app_code': app_codes, 'status':[0, 1]}, ['app_code'], multi_row=True)
    model_json = json.dumps(model)
    apps_json = json.dumps(apps)
    to_add = []
    if not exists:
        to_add = [
                {'task_id': task_id,
                 'app_code': export.get('app_code'),
                 'model': model_json,
                 'export': json.dumps(export),
                 'apps': apps_json,
                 'status': 0
        } for export in files]
    else:
        exists_codes = [item.get('app_code') for item in exists] or []
        for export in files:
            if export.get('app_code') not in exists_codes:
                to_add.append({
                    'task_id': task_id,
                    'app_code': export.get('app_code'),
                    'model': model_json,
                    'export': json.dumps(export),
                    'apps': apps_json,
                    'status': 0
                })

    repository.add_list_data('dap_bi_dmp_open_tenant_init_app_record', to_add, fields=['task_id', 'app_code', 'model', 'export', 'apps', 'status'])



def update_delivery_record_of_app(task_id, app_code, status, msg=None):
    """
    status: 0 已创建 1 运行中 2 完成 3 忽略
    :param task_id:
    :param app_code:
    :param status:
    :param msg:
    :return:
    """
    repository.update_data(
        'dap_bi_dmp_open_tenant_init_app_record',
        {'status': status},
        {'task_id': task_id, 'app_code': app_code}
    )


def query_not_run_task():
    from dmplib.db.mysql_wrapper import get_db

    with get_db() as db:
        return db.query('select * from dap_bi_dmp_open_tenant_init_app_record where `status` in (0, 1)')

def merge_definition_app(codes, apps):
    result = []
    app_map = {}
    for app in apps:
        app_map[app.get('app_code')] = app.get('app_name')

    for code in codes:
        app_name = app_map.get(code) or code
        app = {'app_code': code, 'app_name': app_name}
        result.append(app)
    return result

def do_initialize_data(model:InitializeTenantModel, spec_files=[]):
    # 从模板库初始化
    if not model.app_list:
        return
    from project.services import project_init_service
    select_codes = []
    exports_list = []
    #把数芯当作已分发的应用
    if is_contain_sxdzyy(model.app_list):
        project_init_service.set_omp_task_status(f'已包含数芯定制应用，之后该租户不会更新从RDC推送的内容', model.task_id)
        exports_list.append({'app_code': 'sxdzyy'})

    apps, files = get_rdc_files(model.task_id, model.app_list, model.env_code, model.customer_id)
    apps = remove_no_file_appcode(files, apps)
    model.app_list = apps
    if spec_files:
        files = spec_files
    else:
        files = [] if files is None else files
    project_init_service.set_omp_task_status(f'获取到的1.5报表文件的子系统:{convert_to_code(files)}', model.task_id,)
    if not files:
        project_init_service.set_omp_task_status(f'获取的报表文件为空，分发结束', model.task_id, status = OmpTenantsTaskStatus.Successful.value)
        return exports_list

    #分发模板库文件
    try:
        delivery_record_of_app(model, files, apps)
        is_completed, exports = do_delivery_template_data(model, files, apps)
    except Exception as e:
        from project.services import project_init_service
        project_init_service.set_omp_task_status(f'分发模板库文件出错:{str(e)}', model.task_id, status = OmpTenantsTaskStatus.Failure.value)
        raise e

    if is_completed:
        exports_list += exports
    # 更新项目的select_code
    project = repository.get_data('dap_bi_tenant_setting', {'code': model.tenant_code}, ['select_code'], False)
    if not project:
        return exports_list
    if select_codes:
        exists_codes = []
        exists = project.get('select_code', '')
        if exists:
            exists_codes += exists.split(',')
        if operator.eq(select_codes, exists_codes):
            select_codes = list(set(select_codes + exists_codes))
            repository.update_data('dap_bi_tenant_setting', {'select_code': ','.join(select_codes)}, {'code': model.tenant_code})
    return exports_list

def get_rdc_files(task_id, apps, env_code, customer_id):
    from project.services import project_init_service
    codes = [x['app_code'] for x in apps]

    apps = repository.get_data('dap_bi_product_application_relation', {'app_code': codes},
                                   ['app_name', 'app_code'], multi_row=True)
    #合并已定义的和未定义的系统信息
    apps = merge_definition_app(codes, apps)
    #套装app处理，买一送一之类的(3201送7250)
    apps = assign_suit_app(apps)
    remain_export_app = apps
    project_init_service.set_omp_task_status(f'开始获取对应报表文件, 需要分发的子系统包括:{convert_to_code(remain_export_app)}', task_id)
    files = get_files_from_rdc(apps, env_code, customer_id, splicing_lang_tag(_get_db_type()))
    logging.info(f'从推送记录获取的产品:{files}')
    return apps, files

def convert_to_code(apps):
    if not apps:
        return []
    codes = []
    for app in apps:
        code = app.get('app_code')
        if code:
            codes.append(code)
    return codes

def is_contain_sxdzyy(apps):
    for app in apps:
        if app['app_code'] == 'sxdzyy':
            return True
    return False

def sort_template_configs(template_configs):
    if not template_configs or len(template_configs) == 1:
        return template_configs

    # 把信创的模板库放到最后
    xc_conf = None
    xc_idx = None
    for idx, conf in enumerate(template_configs):
        if conf['template_code'].startswith('xc_'):
            xc_conf = conf
            xc_idx = idx
            break
    if xc_conf:
        del template_configs[xc_idx]
        template_configs.append(xc_conf)
    return template_configs

def get_remain_to_export_app(exported_files, apps):
    if not exported_files:
        return apps
    to_export_app = []
    file_map = {e['app_code']: e for e in exported_files}
    # 生成一个exported_files中不存在的app的list
    for app in apps:
        exists = file_map.get(app['app_code'])
        if not exists:
            to_export_app.append(app)
    return to_export_app


def file_app_mapping(exported_files, apps):
    if not exported_files:
        return
    for export in exported_files:
        app_code = export.get('app_code')
        export_name = export.get('title')
        # app_code与对应文件建立关系
        if not app_code:
            export['app_code'] = exported_file_code_mapping(export_name, apps)


def request_template_server(url, template_tenant, to_download_apps, specific_name_prefix):
    params = {'sign': 'auto_export', 'code': template_tenant, 'apps': to_download_apps,
              'specific_name_prefix': specific_name_prefix}
    response = requests.post(url, json=params, timeout=30)
    if response.status_code == 200:
        result = response.json() if response.text else {'result': 0, 'msg': '没有获取到数据'}
        if not result.get('result'):
            logging.info(result.get('msg'))
        return result.get('data') or {}
    return {}


def do_delivery_template_data(model: InitializeTenantModel, exported_files, apps):
    if config.get('DB.db_type') == DBType.DM.value:
        return do_delivery_template_data_multi(model, exported_files, apps)
    else:
        return do_delivery_template_data_ori(model, exported_files, apps)


def do_delivery_template_data_ori(model:InitializeTenantModel, exported_files, apps):
    # 执行分发
    from project.services import project_init_service
    tenant_code = model.tenant_code
    project_init_service.set_omp_task_status(f'开始分发报表文件，共{len(exported_files)}个文件', model.task_id,)
    succ_app = []
    fail_app = []
    for export in exported_files:
        export_id = export.get('id')
        desc = export.get('title', '')
        deliver_id = seq_id()
        file_url = export.get('url', '')
        app_code = export.get('app_code', '')
        try:
            update_delivery_record_of_app(model.task_id, app_code, 1)
            data = {
                'id': deliver_id, "export_id": export_id, "title": '自动分发任务',
                "description": desc, "source_url": file_url, "source_project": "dmp_template",
                "dest_projects": tenant_code, "is_all_projects": 0, "distribute_type": 1, "replace_data_source": 0,
                "operate_type": 1, "is_lock_dataset": 1
            }
            repository.add_data('dap_bi_deliver_dashboard', data)
            from user.services.user_service import DEFAULT_USER_ID
            from issue_dashboard.services import deliver_service
            project_init_service.set_omp_task_status(f'正在分发报表文件[{app_code} -> {file_url}]', model.task_id)
            deliver_with_retry(deliver_id, model.overwrite_1_5_app)
            # app_code与对应文件建立关系
            if not app_code:
                export['app_code'] = exported_file_code_mapping(desc, apps)
            succ_app.append(app_code)
            project_init_service.set_omp_task_status(f'{app_code}分发报表文件完成[{file_url}]', model.task_id)
        except Exception as e:
            # 分发失败一个整个开户流程都算失败
            project_init_service.set_omp_task_status(f'分发报表文件出错[{file_url}]:{str(e)}', model.task_id, status=OmpTenantsTaskStatus.Failure.value)
            raise e
        finally:
            update_delivery_record_of_app(model.task_id, app_code, 2)
    project_init_service.set_omp_task_status(f'报表初始化完成，已成功分发的子系统:{succ_app}，分发失败的子系统:{fail_app}', model.task_id, status=OmpTenantsTaskStatus.Successful.value)
    # 并发的场景需要统一掉一次重新生成level_code
    fix_level_code(tenant_code)
    return True, exported_files


def do_delivery_template_data_multi(model:InitializeTenantModel, exported_files, apps):
    # 执行分发
    from project.services import project_init_service
    tenant_code = model.tenant_code
    project_init_service.set_omp_task_status(f'开始分发报表文件，共{len(exported_files)}个文件', model.task_id,)
    succ_app = []
    fail_app = []

    def sub_export(_export, _tenant_code, _mode, _apps, _succ_app):

        g = _AppCtxGlobals()
        g.code = _tenant_code
        g.account = 'skylineadmin'
        _app_ctx_stack.push(g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(g)

        app_code = _export.get('app_code', '')
        try:
            update_delivery_record_of_app(model.task_id, app_code, 1)
            export_id = _export.get('id')
            desc = _export.get('title', '')
            deliver_id = seq_id()
            file_url = _export.get('url', '')
            data = {
                'id': deliver_id, "export_id": export_id, "title": '自动分发任务',
                "description": desc, "source_url": file_url, "source_project": "dmp_template",
                "dest_projects": _tenant_code, "is_all_projects": 0, "distribute_type": 1, "replace_data_source": 0,
                "operate_type": 1, "is_lock_dataset": 1
            }
            repository.add_data('dap_bi_deliver_dashboard', data)
            project_init_service.set_omp_task_status(f'正在分发报表文件[{app_code} -> {file_url}]', model.task_id,)
            deliver_with_retry(deliver_id, _mode.overwrite_1_5_app)
            # app_code与对应文件建立关系
            if not app_code:
                _export['app_code'] = exported_file_code_mapping(desc, _apps)
            _succ_app.append(app_code)
            project_init_service.set_omp_task_status(f'{app_code}分发报表文件完成[{file_url}]', model.task_id)
        finally:
            update_delivery_record_of_app(model.task_id, app_code, 2)
            g = _app_ctx_stack.pop()
            # close all connections
            db_ctx = DBContext.instance(g)
            if db_ctx:
                db_ctx.close_all()

    worker = config.get('DBInstaller.delivery_worker') or 2
    with concurrent.futures.ThreadPoolExecutor(max_workers=int(worker)) as executor:
        tasks = []
        for export in exported_files:
            future = executor.submit(sub_export, export, tenant_code, model, apps, succ_app)
            tasks.append(future)

        try:
            for future in concurrent.futures.as_completed(tasks):
                # 获取任务执行结果（可选）
                result = future.result()
                print(f"Task finished: {result}")
        except Exception as e:
            print(f"An exception occurred: {e}")
            # 取消所有未开始的任务
            for task in tasks:
                task.cancel()
            # 等待已经开始的任务完成
            for future in tasks:
                if not future.cancelled():
                    try:
                        future.result()
                    except Exception as sub_e:
                        print(f"Task failed: {sub_e}")
            raise e

    project_init_service.set_omp_task_status(f'报表初始化完成，已成功分发的子系统:{succ_app}，分发失败的子系统:{fail_app}', model.task_id, status=OmpTenantsTaskStatus.Successful.value)
    # 并发的场景需要统一掉一次重新生成level_code
    fix_level_code(tenant_code)
    dataset_sync_mip(tenant_code)

    return True, exported_files


def fix_level_code(tenant_code):
    try:
        # 报表
        res = DMPAPI(tenant_code, retries=2).fix_dashboard_level_code({'dashboard_id': '3a070aa4-afdf-0266-be51-e8fe2f4b0b3b'})
        # logger.error(f'报表 fix_dashboard_level_code:{res}')
        # 大屏
        res = DMPAPI(tenant_code, retries=2).fix_dashboard_level_code({'dashboard_id': '00000000-1111-0000-2222-02420a0c0002'})
        # logger.error(f'大屏 fix_dashboard_level_code:{res}')
        # 数据集
        res = DMPAPI(tenant_code, retries=2).fix_dashboard_level_code_batch({"table": "dap_bi_dataset", "data_id": "ROOT"})
        # logger.error(f'数据集 fix_dashboard_level_code_batch:{res}')
    except Exception as e:
        logger.error(f'fix_dashboard_level_code error:{e}')

def dataset_sync_mip(tenant_code):
    try:
        DMPAPI(tenant_code, retries=2).dataset_sync_mip()
    except Exception as e:
        logger.error(f'sync_dataset_mip error:{e}')

def deliver_with_retry(deliver_id, overwrite_1_5_app=0, retry=0):
    from issue_dashboard.services import deliver_service
    from user.services.user_service import DEFAULT_USER_ID
    # 如果是升级1.5不分发简讯
    try:
        deliver_service.deliver_split(deliver_id, 1, 1, 0, DEFAULT_USER_ID, False, overwrite_1_5_app, 1)
    except Exception as e:
        retry = retry + 1
        if retry < 3:
            logger.error(f'分发报表异常，十秒后开始第{retry}次重试')
            time.sleep(10)
            return deliver_with_retry(deliver_id, overwrite_1_5_app, retry + 1)
        else:
            raise e


def exported_file_code_mapping(export_name: str, apps: list):
    for item in apps:
        name_ = item['app_name']
        if export_name.endswith(name_):
            return item['app_code']
    return ''


def assign_suit_app(app_list: list):
    if not app_list:
        return
    codes = [x['app_code'] for x in app_list]
    sql = """ select app_code,app_name,suit_cond,suit_codes from dap_bi_product_application_relation where suit_codes != '' and suit_codes is not null """
    data = repository.get_data_by_sql(sql, params=None)
    if not data:
        return app_list
    for item in data:
        cond_ = item['suit_cond']
        codes_ = item['suit_codes']
        item_code = item['app_code']
        suit_codes = codes_.split(',')
        if cond_ == 'AND':
            if set(suit_codes).issubset(set(codes)) and item_code not in codes:
                app_list.append(item)
        elif cond_ == 'OR':
            intersection = list(set(codes) & set(suit_codes))
            if intersection and item_code not in codes:
                app_list.append(item)
    return app_list


def update_create_record_completed(record_id, app_list=None, msg=None):
    if not record_id:
        return
    try:
        codes = []
        if not app_list:
            codes = ['']
        else:
            codes = [x['app_code'] for x in app_list]
            codes = list(set(codes))
        repository.update_data('dap_bi_project_create_record',
                               {'status': 'COMPLETED', 'app_codes': ','.join(codes), 'msg': msg}, {'id': record_id})
    except Exception as e:
        logging.error(f'更新开户记录[{record_id}]失败:{str(e)}')


def update_create_record_error(record_id, err):
    try:
        if record_id:
            repository.update_data('dap_bi_project_create_record', {'status': 'FAILURE', 'msg': err}, {'id': record_id})
    except Exception as e:
        logging.error(f'更新开户记录[{record_id}]失败:{str(e)}')


def add_project_create_record(model: ProjectModel):
    try:
        params = json.dumps(model.get_dict(), ensure_ascii=False)
        app_codes = ''
        id = seq_id()
        if model.app_code_list:
            app_codes = ','.join([x['app_code'] for x in model.app_code_list])
        updated = repository.add_data('dap_bi_project_create_record',
                                      {'id': id, 'code': model.code, 'from_init': model.from_init, 'params': params,
                                       'app_codes': app_codes, 'status': 'INITED', 'source':'ERP'})
        if updated:
            model.create_record_id = id
    except Exception as e:
        logging.error(f'保存开户记录异常:{str(e)}')


def get_completed_record_appcodes(code):
    sql = """ select app_codes from dap_bi_project_create_record where status = 'COMPLETED' and code = %(code)s and source = 'ERP' """
    params = {'code': code}
    data = repository.get_data_by_sql(sql, params)
    app_codes = []
    if data:
        for row in data:
            s = row.get('app_codes')
            if not s:
                continue
            app_codes += s.split(',')
        return list(set(app_codes))
    return None


def assign_app_name(codes, apps):
    app_map = {}
    for app in apps:
        code = app['app_code']
        if app_map.get(code):
            app_map[code].append(app)
        else:
            app_map[code] = [app]
    app_list = []
    for code in codes:
        app_list += app_map.get(code, [])
    return app_list


def set_additional_purchase_cache_status(task_id):
    try:
        result = ProjectInitResultModel()
        result.task_id = task_id
        result.status = 1
        redis.conn().set(result.task_id, value=redis.RedisCache._dumps(result.get_dict()), time=3 * 24 * 1 * 60 * 60)
    except Exception as e:
        logging.error(f'增购任务状态redis设置异常:{str(e)}')


def remove_no_file_appcode(exported_files, app_list):
    if not exported_files:
        return []
    exists_codes = [e['app_code'] for e in exported_files]
    exists_apps = []
    for app in app_list:
        if app['app_code'] in exists_codes or app['app_code'] == 'sxdzyy':
            exists_apps.append(app)
            continue
    return exists_apps


def get_files_from_template_dmp(template_config, apps):
    template_tenant = template_config['template_code']
    delivery_file_api = template_config['delivery_file_api']
    domain = template_config['domain']
    if not domain or not delivery_file_api:
        return False, []
    is_local_server = domain.rstrip('/').find(AppHosts.get(SkylineApps.DMP_ADMIN_GROUP, False).rstrip('/')) == 1
    exported_files = []
    to_download_apps = [x['app_name'] for x in apps]
    # 是否有配置初始化版本 如930 1230
    url = domain + delivery_file_api
    if not is_local_server:
        # 如果模板库不是当前环境通过http获取
        exported_files += request_template_server(url, template_tenant, to_download_apps, '')
    else:
        # 本地环境直接查询
        exported_files = upload_service.get_template_export_file(template_tenant, to_download_apps, '')
    if not exported_files:
        logging.info(f"模板库数据文件获取为空:code={template_tenant}, url={url}")
    return exported_files


def get_template_config(from_init, erp_language, env = None):
    if not env:
        mode = auth_util.get_business_mode()
        mode = mode if mode else ''
        if mode.upper() == 'PRIVATE':
            env = 'erp_private'
        else:
            env = 'erp_saas'
    erp_language = None if erp_language != 2 else erp_language
    params = {'env_code': env, 'condition_from': from_init}
    sql = """ select template_code,publish_way,`domain`,delivery_file_api from dap_bi_env_template_config 
        where env_code = %(env_code)s and condition_from = %(condition_from)s """
    if erp_language:
        params['condition_erp_language'] = erp_language
        sql += ' and condition_erp_language = %(condition_erp_language)s '
    else:
        sql += ' and condition_erp_language is null'
    template_configs = repository.get_data_by_sql(sql, params)
    return template_configs

def convert_to_initialize_tenant_model(project_model:ProjectModel):
    init_model = InitializeTenantModel()
    init_model.from_init = project_model.from_init
    init_model.erp_lang = project_model.erp_language
    init_model.tenant_code = project_model.code
    init_model.task_id = project_model.task_id
    init_model.app_list = project_model.app_code_list
    init_model.omp_tenants_log = project_model.omp_tenants_log
    init_model.init_1_5_app = project_model.init_1_5_app
    init_model.overwrite_1_5_app = project_model.overwrite_1_5_app
    init_model.env_code = project_model.env_code
    init_model.customer_id = project_model.customer_id
    init_model.record_id = project_model.create_record_id
    return init_model

