#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/1.
"""
import logging
import random
import string
import traceback
from _sha1 import sha1
from loguru import logger
import json
from urllib.parse import urlparse,urljoin

import bcrypt

from base import repository
from base import tenant_repository
from base.constants import RDS_ID, MIGRATE_TENANT_DISABLE_TIPS, RDC_ENABLE_AUTH
from base.db_wrap import DbWrap
from base.enums import ErpApiId, ErpApiType, ProjectEnabled, ProjectAction, OperationLogModule, OmpTenantsTaskStatus, \
    TenantFrom
from base.errors import EmailSendError
from components.celery import get_task_id
from components import mail
from components.i18n_util import splicing_lang_tag
from dmplib import redis
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.utils.errors import UserError
from dmplib.utils.jwt_login import LoginToken
from dmplib.utils.strings import seq_id
from dmplib.db.mysql_wrapper import get_db, SimpleMysql
from dmplib.redis import conn as conn_redis, RedisCache
from dmplib import config
import dmplib.saas.project
from dmplib.saas.project import get_db as get_project_db
from dmplib.hug import g
from project.models import ProjectModel, ProjectInitResultModel, TenantLicenseModel
from project.repositories import project_repository, email_repository, project_setting_repository
from project.services import project_tag_service, project_init_service
from project.services.operation_log_service import extract_log_info, save_log
from project.services.project_db_install_service import get_db_installer
from app_celery import sync_project_data
from enum import Enum, unique
from components.dmp_api import DMPAPI
from components.redis_tool import RCache

password_special_symbols = '!@#$%^&*'


@unique
class EmailTemplateType(Enum):
    """
    邮件模板类型
    """

    # 新增用户模板
    AddUser = 1
    # 重置密码模板
    ResetPassword = 2
    # 注册组件开发者用户模板
    RegisterDeveloper = 4

    # 重置邮箱
    ResetEmail = 5

    # 初始化密码模板
    InitPassword = 8
    # 管理员秘钥邮件模板
    AdminDeveloper = 10


def _project_db_cache_key(code):
    """
    增加code前綴，跟dmp保持統一
    临时方案,更新dmplib后不再使用
    与dmplib.saas.project._db_cache_key保持同步
    :param code:
    :return:
    """
    return code + ':Project:DB:Config:' + code


def get_project_list(query_model):
    """
    获取项目列表
    :param project.models.ProjectQueryModel query_model:
    :return: project.models.ProjectQueryModel
    """
    return project_repository.get_project_list(query_model)


def get_project_list_with_tags(query_model):
    """
    获取项目列表
    :param project.models.ProjectQueryModel query_model:
    :return: project.models.ProjectQueryModel
    """
    project_list = project_repository.get_project_list(query_model)
    if not project_list.items:
        return project_list
    codes = [item['code'] for item in project_list.items]
    project_tags = project_tag_service.get_project_tags(codes)
    if not project_tags:
        return project_list
    project_tag_map = {}
    for item in project_tags:
        code_ = item['project_code']
        if project_tag_map.get(code_):
            project_tag_map.get(code_).append(item)
        else:
            project_tag_map[code_] = [item]
    for item in project_list.items:
        tags = project_tag_map.get(item['code'])
        if not tags:
            continue
        item['tags'] = tags
    return project_list


def get_project_by_code(code):
    """
    根据code获取项目信息
    :param code:
    :return:
    """
    if not code:
        return None

    sql = '''
        select t1.name,t1.customer_id,t1.rds_id,t1.tenant_db_name,t1.title, t2.* from dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code where t1.code =  %(code)s
    '''
    data = repository.get_data_by_sql(sql, {'code': code})
    if not data:
        raise UserError(message='项目不存在')
    data = data[0]
    return ProjectModel(**data)


def get_project(project_id):
    if not project_id:
        raise UserError(message='缺少项目ID')
    sql = '''
            select t1.name,t1.customer_id,t1.rds_id,t1.tenant_db_name,t1.title, t2.* from dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code where t1.id =  %(id)s
        '''
    data = repository.get_data_by_sql(sql, {'id': project_id})
    if not data:
        raise UserError(message='项目不存在')
    data = data[0]
    project = ProjectModel(**data)
    auth_key = ''
    setattr(project, 'openapi_auth_key', auth_key)
    project.value_added_func = get_project_value_added_func(project.code)
    project.is_data_cloud_1_5_enabled \
        = project_setting_repository.get_project_setting(project.code, 'is_data_cloud_1_5_enabled', 0)
    project.is_metadata_export_enabled \
        = project_setting_repository.get_project_setting(project.code, 'is_metadata_export_enabled', 1)
    return project


def check_project_code(project_code):
    """
    检查项目编码
    :param str project_code:
    :return:
    """
    if not project_code:
        raise UserError(message='缺少code')
    if repository.data_is_exists('dap_p_tenant', {'code': project_code}):
        raise UserError(message='项目编码(' + project_code + ')已经存在')
    return True


def add_data_account(project_id):
    """
    添加data用户
    :param str project_id:
    """
    if project_id:
        model = get_project(project_id)
        get_db_installer(model, config.get('DBInstaller.project_empty_db')).install_data_account()
        conn = conn_redis()
        cache_key = _project_db_cache_key(model.code)
        conn.delete(cache_key)
    else:
        sql = '''
            select t1.name,t1.customer_id,t1.rds_id,t1.tenant_db_name,t1.title, t2.* from dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code 
        '''
        data_list = repository.get_data_by_sql(sql, params = {})
        for data in data_list:
            model = ProjectModel(**data)
            get_db_installer(model, config.get('DBInstaller.project_empty_db')).install_data_account()
            conn = conn_redis()
            cache_key = _project_db_cache_key(model.code)
            conn.delete(cache_key)
    return True


def add_project(model, need_extra=False):  # NOSONAR
    """
    添加项目
    :param project.models.ProjectModel model:
    :param need_extra:  是否还有额外的字段要保存
    :return: project.models.ProjectModel
    """
    from project.services.template_sync_service import add_project_create_record, update_create_record_error, \
        get_completed_record_appcodes
    project_init_service.set_omp_task_status('任务已创建，正在执行开户...', task_id=model.task_id, log=model.omp_tenants_log,
                                             status=OmpTenantsTaskStatus.InProgress.value)
    # 保存开户记录
    record_id = model.create_record_id
    if not record_id:
        add_project_create_record(model)
        record_id = model.create_record_id

    if not need_extra:
        model.id = seq_id()
    model.form_mode = 'add'
    if model.code == 'empty':
        raise_error_and_update_record(model, record_id, '项目编码不能为(' + model.code + ')，请更换为其他编码!')

    # 产业建管的环境，则将租户的授权模式改为1（基础数据的权限体系）
    _set_cyjg_auth_code(model)
    # 设置app对应名称
    if model.is_rdc_auth is None:
        model.is_rdc_auth = 1 if RDC_ENABLE_AUTH in [1, '1'] else 0

    if model.select_code:
        select_project = get_project_by_code(model.select_code)
        if not select_project:
            raise_error_and_update_record(model, record_id, '源项目编码(' + model.select_code + ')不存在')

        model.rds_id = select_project.get("rds_id")
        model.type = select_project.get("type")
        model.odps_proj = select_project.get("odps_proj")
        model.odps_access_id = select_project.get("odps_access_id")
        model.odps_access_secret = select_project.get("odps_access_secret")
        model.odps_oss_role_id = select_project.get("odps_oss_role_id")
        model.odps_endpoint = select_project.get("odps_endpoint")
        model.account_mode = select_project.get("account_mode")
        model.allow_dashboard_type = select_project.get('allow_dashboard_type')

    model.validate()

    if model.code != ''.join(model.code.split()):
        raise_error_and_update_record(model, record_id, '项目编码(' + model.code + ')不允许空格')
    if not repository.data_is_exists('dap_bi_rds', {'id': model.rds_id}):
        raise_error_and_update_record(model, record_id, 'RDS实例配置不存在')
    # if repository.data_is_exists('dap_p_tenant', {'code': model.code}):
    #     raise_error_and_update_record(model, record_id, '项目编码(' + model.code + ')已经存在')
    project_db_name_prefix = config.get('DBInstaller.project_db_prefix')
    if not project_db_name_prefix:
        raise_error_and_update_record(model, record_id, '项目库前缀未配置')
    model.tenant_db_name = project_db_name_prefix + '_' + model.code
    main_fields = [
        'id',
        'title',
        'name',
        'code',
        'tenant_db_name',
        'rds_id',
        'customer_id',
    ]
    fields = [
        'id',
        'code',
        'logo_uri',
        'odps_proj',
        'odps_access_id',
        'odps_access_secret',
        'odps_oss_role_id',
        'odps_endpoint',
        'description',
        'is_domain_account',
        'account_mode',
        'default_account',
        'erp_data_source_code',
        'admin_pwd',
        'admin_email',
        'type',
        'select_code',
        'external_secret_key',
        'allow_dashboard_type',
        'erpapi_host',
        'erpapi_access_id',
        'erpapi_access_secret',
        'dmp_env_sign',
        'storage_type',
        'is_rdc_auth',
        'dataset_permission_model',
        'pulsar_project_code',
        'pulsar_app_key',
        'pulsar_app_secret',
        'area_id',
        'mobile_subscribe_filed_disable_break',
        'local_dataset_clean_max_time_type',
        'flow_queue_name',
        'auth_mode',
        'is_expire',
    ]

    if need_extra:
        fields.extend([
            'cust_name',
            'top_customer_id',
            'top_customer_name',
            # 'contract_number',
            'admin_name',
            'admin_phone',
            'dmp_env_sign',
            'use_license',
            'buguid'
        ])

    if not model.external_secret_key and g.account == 'openapi':
        model.external_secret_key = '1qaz!QAZ'

    if isinstance(model.value_added_func, str):
        model.value_added_func = model.value_added_func.split(",")

    # 区分saas和op租户
    if model.from_init == TenantFrom.ERPOP.value:  # NOSONAR
        model.flow_queue_name = config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op'
    elif model.from_init == TenantFrom.ERPSAAS.value:
        # 本地模式数据集清洗区分
        model.local_dataset_clean_max_time_type = 1
        if model.value_added_func:
            model.value_added_func.append('enable-old-erp-report')
            model.value_added_func = list(set(model.value_added_func))
        else:
            model.value_added_func = ['enable-old-erp-report']

    # 兼容老版本的域账户登录模式
    model.is_domain_account = 1 if model.account_mode == 'DOMAIN' else 0
    result = save_tenant(model, main_fields, fields)
    # 新接口开户开启1.5
    if model.init_1_5_app == 1:
        project_setting_repository.save_project_setting(model.code, 'is_data_cloud_1_5_enabled', 1, model.account)
    if not result:
        raise_error_and_update_record(model, record_id, '添加项目失败')
    # 判断from_init参数的值
    if model.from_init and not model.erp_api_info:
        model.erp_api_info = [
            {
                'erp_api_host': model.erpapi_host, 'erp_api_access_id': model.erpapi_access_id,
                'erp_api_access_secret': model.erpapi_access_secret, 'erp_api_type': 'saas' if model.from_init == 'erpsaas' else 'op',
                'local_dataset_clean_max_time_type': 1 if model.from_init == TenantFrom.ERPSAAS.value else 0
            }
        ]
    #是否默认开启套打功能
    open_print_enable = config.get("ReportCenter.open_print_enable", 0) or 0
    if open_print_enable in (1, '1'):
        if model.value_added_func:
            model.value_added_func.append('printing-report')
            model.value_added_func = list(set(model.value_added_func))
        else:
            model.value_added_func = ['printing-report']
    # 保存增值功能配置
    save_project_value_added_func(model.code, model.value_added_func)
    # 异步初始化租户数据
    task_id = get_task_id('project', "init", model.code, model.id)
    model_dict = model.get_dict()
    model_dict.pop("odps")
    sync_project_data.delay(task_id, model_dict)

    if model.license:
        add_tenant_license(model.code, model.license)

    return {'task_id': task_id}

def save_tenant(model, main_fields, fields):
    exists = repository.get_data("dap_p_tenant", {'code': model.code}, ['code', 'id'], multi_row=False)
    if exists:
        id = exists.get('id')
        model.id = id
        result = repository.update_model('dap_p_tenant', model, {'code': model.code}, main_fields)
        repository.delete_data('dap_bi_tenant_setting', {'code': model.code})
        result = repository.add_model('dap_bi_tenant_setting', model, fields)
    else:
        result = repository.add_model('dap_p_tenant', model, main_fields)
        result = repository.add_model('dap_bi_tenant_setting', model, fields)
    return result



def raise_error_and_update_record(model, record_id, err):
    from project.services.template_sync_service import add_project_create_record, update_create_record_error, \
        get_completed_record_appcodes
    update_create_record_error(record_id, err)
    project_init_service.set_omp_task_status(err, model.task_id, model.omp_tenants_log, status=OmpTenantsTaskStatus.Failure.value)
    raise UserError(message=err)


def get_system_default_rds():
    """
    获取系统默认rds
    :return:
    """
    rds_id = RDS_ID
    if repository.data_is_exists('dap_bi_rds', {'id': rds_id}):
        return rds_id
    # 不存在则初始化一个rds
    repository.add_data(
        'dap_bi_rds',
        data={
            "id": rds_id,
            "name": "系统默认rds",
            "host": config.get("DB.host"),
            "port": int(config.get("DB.port")),
            "pwd": '***********',
            "account": '***********'
        }
    )
    return rds_id


def _set_cyjg_auth_code(model: ProjectModel):
    """
    产业建管的环境，则将租户的授权模式改为1
    :param model:
    :return:
    """
    if model.from_init == TenantFrom.ERPSAAS.value:
        model.auth_mode = 1
        logger.error(f"租户的授权模式，基础数据的权限体系")
    else:
        logger.error(f"租户的授权模式，默认模式")


def add_tenant_license(code, license):
    """添加租户级license"""
    license['code'] = code
    license_model = TenantLicenseModel(**license)
    license_model.validate()

    result = repository.add_model('dap_bi_tenant_license', license_model, [
        'product_start',
        'product_end',
        'report_num',
        'code',
    ])
    if not result:
        raise UserError(message='添加租户license失败')


def delete_tenant_license(code):
    """删除租户对应的license"""
    repository.delete_data('dap_bi_tenant_license', {'code': code})


def get_task_data(task_id):
    """
    获取项目异步结果数据
    :param task_id:
    :return:
    """
    data = redis.conn().get_data(task_id)
    if data:
        redis.conn().del_data(task_id)
        return data
    return ProjectInitResultModel().get_dict()


def get_user_email_content(name, code, account, pwd, template_type, is_domain_account=None):
    """
    获取添加用户邮件内容
    :param name:
    :param code:
    :param account:
    :param pwd:
    :param template_type:
    :param is_domain_account: 是否域账户登录
    :return:
    """
    # 从数据库中获取是否域账户登录
    if is_domain_account == 1:
        pwd = '请使用明源云域账户密码登录'

    # 获取邮件模板
    email_template_data = email_repository.get_email_template(template_type)
    if not email_template_data:
        raise UserError(message='邮件模板未找到')
    replace_dict = {
        '{姓名}': name,
        '{企业域地址}': AppHosts.get(SkylineApps.DP, False),
        '{企业代码}': code,
        '{用户名}': account,
        '{密码}': pwd,
        '{域名}': urljoin(AppHosts.get(SkylineApps.DP, False) , 'bi-visual/'),
    }
    return email_template_data, mail.replace_content(email_template_data.get('content'), replace_dict)


def get_reset_email_content(name, code, account, reset_passwd_url, template_type):
    """
    获取添加用户邮件内容
    :param code:
    :param name:
    :param account:
    :param reset_passwd_url:
    :param template_type:
    :return:
    """

    # 获取邮件模板
    email_template_data = email_repository.get_email_template(template_type)
    if not email_template_data:
        raise UserError(message='邮件模板未找到')
    url = AppHosts.get(SkylineApps.DP, False)
    replace_dict = {
        '{姓名}': name,
        '{企业域地址}': url,
        '{企业代码}': code,
        '{用户名}': account,
        '{链接}': reset_passwd_url,
        '{域名}': (url if url.endswith('/') else url + "/") + 'bi-visual/',
    }
    return email_template_data, mail.replace_content(email_template_data.get('content'), replace_dict)


def _generate_password():
    clist = [string.ascii_letters, string.digits, password_special_symbols]
    rest = clist.copy()
    random.shuffle(clist)

    chars = []
    for _ in range(16):
        cset = rest.pop()
        chars.extend(random.sample(cset, 1))
        if not rest:
            rest = clist.copy()
            random.shuffle(rest)
    return ''.join(chars)


def reset_user_password(code, account=None):
    """
    重设用户密码（随机密码）
    :param str user_id:
    :param str password:
    :return:
    """

    # 生成随机16位密码, 保证至少有一位字母, 一位数字, 一位特殊字符, 不包含用户名, 不包含连续字符和数字
    # 每次将字符集合列表乱序, 从数字/字母/特殊字符中选取一个, 因为包含特殊字符, 所以不包含用户名
    admin_pwd = _generate_password()
    while code in admin_pwd:
        admin_pwd = _generate_password()

    project_info = get_project_by_code(code)

    # 添加account参数, 接口级别支持重置指定账号的密码
    if not account:
        # 5.5配置无了, 默认认为是cy类型
        if config.get('Product.business_type', 'cy') == 'cy':
            # 获取管理员账号
            with get_project_db(project_info.code) as project_db:
                from dmplib.constants import ADMINISTRATORS_ID
                sql = "select account from dap_p_user where id = '{}'".format(ADMINISTRATORS_ID)
                account = project_db.query_scalar(sql) or project_info.code
        else:
            account = project_info.code

    if not project_info:
        raise UserError(message='项目不存在')

    if project_info.admin_email:
        email_template_data, content = get_user_email_content(
            project_info.name, project_info.code, account, admin_pwd, EmailTemplateType.ResetPassword.value
        )

        mail.send(
            mail.Mail(
                subject=email_template_data.get('subject'),
                body=content,
                receiver=[mail.MailContact(name=project_info.name, mail=project_info.admin_email)],
            ),
            code=project_info.code,
            subtype=email_template_data.get('send_mode') if email_template_data.get('send_mode') else 'html',
        )
    encrypted_pwd = bcrypt.hashpw(sha1(admin_pwd.encode()).hexdigest().encode('utf-8'), bcrypt.gensalt())
    # 更新config project里的初始密码
    repository.update_data('dap_bi_tenant_setting', {'admin_pwd': admin_pwd}, {'code': code})
    with get_project_db(code) as conn:
        rs = conn.update('dap_p_user', {'pwd': encrypted_pwd.decode('utf-8'), 'old_pwd': 0}, {'account': account})
        if rs:
            return admin_pwd
    return None


def reset_user_email(model):
    """
    重设用户邮箱
    :param str user_id:
    :param str password:
    :return:
    """

    if not model.code or not model.admin_email:
        raise UserError(message='账户或email不能为空')

    # 更新config project里的email
    repository.update_data('dap_bi_tenant_setting', {'admin_email': model.admin_email}, {'code': model.code})

    if model.admin_email and int(model.is_rdc_auth) == 1:
        default_user = {'name': '审核管理员', 'email': model.admin_email, 'account': '审核管理员', 'tenant_code': model.code, 'is_default': 1}
        repository.delete_data('dap_bi_developer_reviewer', {'tenant_code': model.code, 'is_default': 1, 'account': '审核管理员'})
        repository.add_data('dap_bi_developer_reviewer', default_user)

    with get_project_db(model.code) as conn:
        return conn.update('dap_p_user', {'email': model.admin_email}, {'account': model.code})


def get_update_fields(kwargs: dict):
    return [k for k, v in kwargs.items() if v]


def update_project(model):
    """
    更新项目
    :param model:
    :return:
    """
    model.form_mode = 'update'
    model.validate()
    if not repository.data_is_exists('dap_p_tenant', {'id': model.id}):
        raise UserError(message='项目不存在')
    old_data = repository.get_data('dap_bi_tenant_setting', {'code': model.code}, ['is_rdc_auth', 'is_expire']) or {}
    is_rdc_auth = old_data.get('is_rdc_auth') or 0
    is_expire = old_data.get('is_expire') or 0
    # 修复保存清空rds_id的bug
    main_fields = [
        'title',
        'name',
        'rds_id'
    ]
    fields = [
        'logo_uri',
        'small_logo',
        'portal_logo',
        'small_portal_logo',
        'site_icon',
        'site_title',
        'odps_proj',
        'odps_access_id',
        'odps_access_secret',
        'odps_oss_role_id',
        'odps_endpoint',
        'description',
        'is_domain_account',
        'account_mode',
        'erp_data_source_code',
        'external_secret_key',
        'type',
        'allow_dashboard_type',
        'erpapi_host',
        'erpapi_access_id',
        'erpapi_access_secret',
        'dmp_env_sign',
        'storage_type',
        'is_rdc_auth',
        'pulsar_project_code',
        'pulsar_app_key',
        'pulsar_app_secret',
        'area_id',
        'is_expire',
    ]
    fields = get_update_fields(model.get_dict(fields))
    image_fields = ['logo_uri', 'small_logo', 'portal_logo', 'small_portal_logo', 'site_title', 'site_icon']
    # 不论值是否为空都会更新的字段
    extend_fields = ['external_secret_key']
    fields = list(set(fields + image_fields + extend_fields))
    # 兼容老版本的域账户登录模式
    if model.account_mode == 'DOMAIN':
        model.is_domain_account = 1
    else:
        model.is_domain_account = 0

    """
    清除缓存. 原因来自 dmplib.saas.project.get_db_config
    TODO: 应该使用函数 dmplib.saas.project.rm_db_config_cache
    """  # pylint: disable=W0105
    clear_project_cache(model.code)
    # 保存erp接口管家配置
    save_erp_api_info(model)
    # 保存增值功能配置
    save_project_value_added_func(model.code, model.value_added_func)
    if model.admin_email and int(model.is_rdc_auth) == 1:
        default_user = {'name': '审核管理员', 'email': model.admin_email, 'account': '审核管理员', 'tenant_code': model.code, 'is_default': 1}
        repository.delete_data('dap_bi_developer_reviewer', {'tenant_code': model.code, 'is_default': 1, 'account': '审核管理员'})
        repository.add_data('dap_bi_developer_reviewer', default_user)
    if is_rdc_auth == 0 and int(model.is_rdc_auth) == 1 and model.admin_email:
        # 给管理员发送开发者邮件
        send_admin_email_content(model)
    if is_expire == 0 and int(model.is_expire) == 1:
        default_message = {
            'id': seq_id(), 'source': '通知', 'type': '系统消息', 'url': '',
            'title': '您的租户使用时效已到期，部分功能已受限制，请及时续费', 'level': '紧急',
        }
        with get_project_db(model.code) as project_db:
            project_db.insert('dap_p_message', default_message)
    repository.update_model('dap_p_tenant', model, {'id': model.id}, main_fields)
    repository.update_model('dap_bi_tenant_setting', model, {'id': model.id}, fields)
    project_setting_repository.save_project_setting(model.code, 'is_metadata_export_enabled',
                                                    getattr(model, 'is_metadata_export_enabled', 1),
                                                    getattr(g, 'account', None))


def forced_offline(user_id, code):
    if not code or not user_id:
        raise UserError(message=u'Lack of parameter: code or user_id')
    # 强制下线用户
    return LoginToken().delete_token(user_id, code)


def get_project_user(query_model):
    return project_repository.get_user_list(query_model)


def omp_tenant_cancellation(task_id, code):
    data = repository.get_data('dap_p_tenant', {'code': code}, ['id'], False)
    deleted = 0
    if data:
        try:
            project_init_service.set_omp_task_status('正在销户', task_id, None, status=OmpTenantsTaskStatus.InProgress.value)
            deleted = delete_project(data['id'])
        except Exception as e:
            logger.error(f'omp销户发生异常:{str(e)}')
    else:
        deleted = 1
    if deleted > 0 :
        project_init_service.set_omp_task_status('销户成功', task_id, None, status=OmpTenantsTaskStatus.Successful.value)
    else:
        project_init_service.set_omp_task_status('销户失败', task_id, None, status=OmpTenantsTaskStatus.Failure.value)


def delete_project(project_id):
    """
    删除项目
    :param project_id:
    :return:
    """
    project_model = get_project(project_id)
    get_db_installer(project_model, config.get('DBInstaller.project_empty_db')).uninstall()
    repository.delete_data('dap_bi_project_yzs_config', {'code': project_model.code})
    repository.delete_data('dap_bi_user_reset_passwd', {'code': project_model.code})
    delete_tenant_license(project_model.code)
    repository.delete_data('dap_bi_tenant_open_log', {'code': project_model.code})
    repository.delete_data('dap_bi_project_value_added_func', {'project_code': project_model.code})
    repository.delete_data('dap_bi_developer_auth_user', {'code': project_model.code})
    repository.delete_data('dap_bi_developer_reviewer', {'tenant_code': project_model.code})
    repository.delete_data('dap_bi_developer_login_log', {'tenant_code': project_model.code})
    repository.delete_data('dap_bi_admin_developer_secret', {'tenant_code': project_model.code})
    repository.delete_data('dap_bi_project_to_shuxin', {'code': project_model.code})
    repository.delete_data('dap_bi_project_tags_relation', {'project_code': project_model.code})
    repository.delete_data('dap_bi_tenant_setting', {'code': project_model.code})
    return repository.delete_data('dap_p_tenant', {'id': project_id})


def delete_project_by_code(code):
    project_id = repository.get_data_scalar('dap_p_tenant', {'code': code}, 'id')
    if not project_id:
        logging.error('没有找到对应的租户信息')
        return
    return delete_project(project_id)


def get_rds_of_min_using():
    """
    获取项目数最小的rds
    :return:
    """
    from rds.services import rds_service

    rds_service.get_rds_list()

    with get_db() as db:
        row = db.query_one(
            'SELECT count(*) as total, rds_id from dap_p_tenant as project inner join dap_bi_rds as rds on project.rds_id =rds.id '
            'GROUP BY rds_id ORDER BY total ASC limit 1'
        ) or db.query_one(
            '''
            SELECT
                count( * ) AS total,
                rds.id as rds_id
            FROM
                dap_p_tenant as project
                right JOIN dap_bi_rds as rds ON project.rds_id = rds.id 
            GROUP BY
                rds.id 
            ORDER BY
                total ASC 
                LIMIT 1
            '''
        )
        if not row:
            raise UserError(400, '缺少可用的rds')
        return row['rds_id']


def cache_project_conf_clean(project):
    conn = conn_redis()
    cache_key = _project_db_cache_key(project.code)
    conn.delete(cache_key)
    return {'status': True, 'name': project.name}


def get_all_projects_info():
    db = get_db()
    return db.query('select t1.`code`, t1.`name`, t1.`title`, t2.`description`, t2.`type` from dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code')


def get_all_project_codes():
    db = get_db()
    return db.query_columns('select code from dap_p_tenant')


def view_db(code, table, conditions, fields: list = None):
    # 只能查看特定数据相关的表
    allow_tables = ('dap_bi_component', 'dap_bi_flow')
    if 'dap_bi_dashboard' in table or 'dap_bi_dataset' in table or table in allow_tables:
        return tenant_repository.get_data(code, table, conditions, fields, True)
    return False


def get_has_default_value_charts(code):
    """
    获取dashboard_chart 中 default_value 不为空的单图
    :param code:
    :return:
    """
    sql = (
        "SELECT c.dashboard_id,d.name as dashboard_name,c.name,c.id,c.chart_code,c.default_value "
        "FROM dap_bi_dashboard_chart c left join dap_bi_dashboard d ON c.dashboard_id=d.id "
        "WHERE default_value is not null and default_value <> ''"
    )
    with get_db(code) as db:  # pylint: disable=E1121
        return db.query(sql)


def calc_component(env: str):
    code_list = repository.get_data('dap_p_tenant', {}, ['code'], True)
    ret = ["报告Id\t报告名\t发布状态\t平台\t单图名\t组件名\t是否是穿透\t环境\t租户代码"]
    for item in code_list:
        with dmplib.saas.project.get_db(item['code']) as db:
            sql = """
                select D.id, D.name as dashboard_name, D.status as dashboard_status, D.platform, C.name as chart_name,
                    chart_code, C.parent_id as pid
                from dap_bi_dashboard D join dap_bi_dashboard_chart C
                ON D.id = C.dashboard_id
                where D.level_code not like '9000-%'
            """
            component_list = db.query(sql) or []
            for v in component_list:
                v['env'] = env
                v['code'] = item['code']
                ret.append("\t".join(map(str, v.values())))
    return "\n".join(ret)


def get_data_list(**kwargs):
    code = kwargs.pop('code', None)
    table_name = kwargs.pop("table_name")
    conditions = kwargs.pop("conditions", None)
    fields = kwargs.pop("fields", None)
    order_by = kwargs.pop("order_by", None)
    limit = kwargs.pop("limit", None)
    return DbWrap(code).get_list(table_name, conditions, fields, order_by, limit, **kwargs)


def get_value_added_func():
    """
    获取系统增值功能
    :return:
    """
    value_added_func_list = repository.get_data("dap_bi_function", {"is_value_added_func": 1}, ["name", "func_code"], True, [("level_code", "asc")])
    value_added_func_list += [{'func_code': 'self-service', 'name': '自助报表'},
                              {'func_code': 'ppt', 'name': '自助报告'}]
    return [{"func_code": item.get('func_code'), "func_name": item.get('name')} for item in value_added_func_list if
            item.get('func_code') not in ['external-subject']]


def get_project_value_added_func(project_code):
    """
    获取当前租户系统增值功能
    :return:
    """
    project_value_added_func_list = repository.get_data("dap_bi_project_value_added_func", {"project_code": project_code}, ["func_code"], True)
    return [item.get('func_code') for item in project_value_added_func_list]


def save_project_value_added_func(project_code, value_added_func):
    """
    保存当前租户系统增值功能
    :return:
    """
    add_data = []
    value_added_func = value_added_func if value_added_func else []

    for func_code in value_added_func:
        item = {"id": seq_id(), "project_code": project_code, "func_code": func_code}
        add_data.append(item)

    repository.delete_data("dap_bi_project_value_added_func", {"project_code": project_code})
    if add_data:
        repository.add_list_data("dap_bi_project_value_added_func", add_data, ["id", "project_code", "func_code"])
    if "self-service" in value_added_func:
        DMPAPI(project_code).refresh_self_service()

    redis_cache = RedisCache()
    redis_cache.delete(f"project_value_added_function")


def get_all_projects_for_open_api():
    """
    获取所有租户信息
    :return:
    """
    db = get_db()
    return db.query('select `id`, `code`, `name`,"1" as is_enable from dap_p_tenant order by `created_on` desc')


def set_erp_api_info(project_code: str, erpapi_host: str, erpapi_access_id: str, erpapi_access_secret: str):
    """
    给租户设置接口管家信息
    :return:
    """
    project_id = repository.get_data_scalar('dap_p_tenant', {'code': project_code}, 'id')
    if not project_id:
        raise UserError(message='项目不存在')
    data = {"erpapi_host": erpapi_host, "erpapi_access_id": erpapi_access_id, "erpapi_access_secret": erpapi_access_secret}
    repository.update_data('dap_p_tenant', data, {"code": project_code})
    # 回填的接口管家地址更新到新表
    data.update({'id': project_id, 'code': project_code, 'local_dataset_clean_max_time_type': 1})
    model = ProjectModel(**data)
    data = get_erp_api_info(project_code)
    if not data:
        handel_history_erp_api(model)
    else:
        for row in data:
            if row.get('erp_api_host'):
                update_data = {"erp_api_host": erpapi_host, "erp_api_access_id": erpapi_access_id, "erp_api_access_secret": erpapi_access_secret}
                with get_project_db(project_code) as project_db:
                    project_db.update('dap_bi_erp_api_info', update_data, {'id': row.get('id')})
    return clear_project_cache(project_code)


def clear_project_cache(project_code):
    code = g.code
    g.code = None
    conn = conn_redis()
    cache_key = _project_db_cache_key(project_code)
    conn.delete(cache_key)
    conn.delete('Project:DB:Config:' + project_code)
    conn.delete('Project:Detail:Info:' + project_code)
    conn.delete('dmp:dmp_env_sign:' + project_code)
    conn.delete('dmp:storage:' + project_code)
    conn.delete('Project:DB:Config::' + project_code)
    conn.delete('Project:Detail:Info::' + project_code)
    conn.delete('dmp:dmp_env_sign::' + project_code)
    conn.delete('dmp:storage::' + project_code)
    conn.delete(project_code + ':Project:Detail:Info:' + project_code)
    conn.delete(project_code + ':dmp:dmp_env_sign:' + project_code)
    conn.delete(project_code + ':dmp:storage:' + project_code)
    conn.delete(project_code + ':project_value_added_function')
    # 删除接口管家access_token缓存
    erp_info_list = get_erp_api_info(project_code) or []
    for erp_info in erp_info_list:
        _url = urlparse(erp_info.get('erp_api_host'))
        host_without_path = _url.scheme + '://' + _url.netloc
        key = '{code}:Erp:Access:Token:{code}:{host_without_path}'.format(code=project_code, host_without_path=host_without_path)
        conn.delete(key)
    RCache().del_by_scan('*dmp-flow-erp*')
    g.code = code
    return True



def verify_erp_opening_tenant_customer_id(customer_id):
    # 校验ERP开通数见租户传递的custom_id
    if customer_id:
        # 当前customer_id已经存在了
        if repository.data_is_exists('dap_bi_project_yzs_config', {"customer_id": customer_id}):
            return False
        else:
            return True
    else:
        return True


def add_erp_opening_tenant_yzs_config_record(erp_customer_id, code, secret):
    # 属于定制需求。在erp调用开租户接口时候插入一条特定的记录
    repository.add_data('dap_bi_project_yzs_config',  data={
        "customer_id": erp_customer_id,
        "secret": '7Kh9uhMtcrfK6nk+l9JLf60zjA2UkbXH+xAI56aZBVY=' if not secret else secret,
        "id": seq_id(),
        "code": code,
        "tenant_id": "",
    })


def tenant_is_local_model(code):
    # from dmplib.saas.project import get_project_info
    #
    # g.code = code
    # project = get_project_info(code)
    from dmplib.db.mysql_wrapper import get_db
    logger.info("start to get project")

    with get_db() as db:
        project = db.query_one('select * from dap_p_tenant where code=%(code)s', params={"code": code})

    if not project:
        return True
    erpapi_host = project.get("erpapi_host") or ''
    return "mybigdata" in erpapi_host


def get_project_area_list():
    """
    获取租户区域表
    :return:
    """
    return repository.get_data("dap_bi_project_area", {}, ['id', 'area_name', 'sort'], True, [("sort", "asc")])


def trigger_user_group_sync(project_code):
    # 触发用户组织数据集同步
    from dmplib.saas.project import get_db as get_project_db
    dmp_api = DMPAPI(project_code)
    queue_name = config.get('RabbitMQ.queue_name_flow')
    conn = get_project_db(project_code)
    sql = "select * from dataset where name in ('用户数据集', '组织数据集')" # noqa
    datasets = conn.query(sql=sql)
    logger.info(f'用户组织数据集：{datasets}')

    for dataset in datasets:
        try:
            dmp_api.flow_run(dataset.get("id"), queue_name)
            logger.info(f'组织用户数据集触发了调度：{dataset}')
        except:
            logger.info(f'组织用户数据集触发调度失败：{traceback.format_exc()}')


def init_dataset_run(project_code):
    logger.info('触发数据集自动调度')
    try:
        dmp_api = DMPAPI(project_code)
        dmp_api.init_dataset_run()
        logger.info('数据集自动调度成功')
    except Exception as e:
        logger.info('数据集自动调度接口失败：{}'.format(str(e)))


def save_erp_api_info(model: ProjectModel):
    """
    保存租户对应的接口管家配置
    """
    erp_api = model.erp_api_info
    if not erp_api:
        return
    if len(erp_api) > 2:
        raise UserError(message='超过接口管家配置限制')
    with get_project_db(model.code) as project_db:
        for erp_info in erp_api:
            erp_info.pop('project_id', None)
            erp_info.pop('project_code', None)
            if erp_info.get('id'):
                project_db.update('dap_bi_erp_api_info', erp_info, {'id': erp_info.get('id')})
            else:
                erp_info['id'] = ErpApiId.SAAS.value if erp_info.get('erp_api_type') == ErpApiType.SAAS.value else ErpApiId.OP.value
                erp_info['local_dataset_clean_max_time_type'] = 1 if erp_info.get('erp_api_type') == ErpApiType.SAAS.value else 0
                project_db.replace_multi_data('dap_bi_erp_api_info', [erp_info], list(erp_info.keys()), condition_field=['id'])
    sync_erp_info_to_project(model.code)


def get_erp_api_info(project_code):
    """
    根据租户ID获取接口管家配置信息
    """
    sql = 'select id, erp_api_host, erp_api_access_id, erp_api_access_secret, erp_api_type from dap_bi_erp_api_info'
    with get_project_db(project_code) as project_db:
        return project_db.query(sql)


def handel_history_erp_api(model: ProjectModel):
    """
    处理租户历史的接口管家配置
    """
    data = get_erp_api_info(model.code)
    if data:
        return
    if not model.erpapi_host:
        return
    with get_project_db(model.code) as project_db:
        sql = "select id,db_type,conn_str from dap_m_data_source where app_level_code = '1000.1401'"
        data_source = project_db.query_one(sql=sql)
    if not data_source:
        return
    db_type = data_source.get('db_type')
    source_id = data_source.get('id')
    conn_str = data_source.get('conn_str')
    conn_str = json.loads(data_source.get('conn_str')) if conn_str else {}
    erp_api_id = ErpApiId.SAAS.value if db_type.lower() == 'mysql' else ErpApiId.OP.value
    erp_api_info = {
        'id': erp_api_id, 'erp_api_host': model.erpapi_host, 'erp_api_access_id': model.erpapi_access_id or '',
        'erp_api_access_secret': model.erpapi_access_secret or '', 'erp_api_type': ErpApiType.SAAS.value if db_type.lower() == 'mysql' else ErpApiType.OP.value,
        'local_dataset_clean_max_time_type': 1 if db_type.lower() == 'mysql' else 0,
    }
    if conn_str:
        conn_str['erp_api_info_id'] = erp_api_id
        conn_str['is_master_local_db'] = 1
        conn_str = json.dumps(conn_str, ensure_ascii=False)
        with get_project_db(model.code) as project_db:
            project_db.update('dap_m_data_source', {'conn_str': conn_str}, {'id': source_id})
            project_db.replace_multi_data('dap_bi_erp_api_info', [erp_api_info], list(erp_api_info.keys()), condition_field=['id'])
    # 清除数据源缓存
    key = '%s:%s' % ('data_source:', source_id)
    cache = conn_redis()
    cache.del_data(key)
    # 绑定所有的MysoftNewERP数据源
    handel_history_data_source(erp_api_id, model.code)


def handel_history_data_source(erp_info_id, project_code):
    with get_project_db(project_code) as project_db:
        sql = "select id,conn_str from dap_m_data_source where type = 'MysoftNewERP' and app_level_code != '1000.1401'"
        data_source_list = project_db.query(sql=sql)
        for data_source in data_source_list:
            if data_source.get('conn_str'):
                source_id = data_source.get('id')
                conn_str = json.loads(data_source.get('conn_str'))
                conn_str['erp_api_info_id'] = erp_info_id
                conn_str = json.dumps(conn_str, ensure_ascii=False)
                project_db.update('dap_m_data_source', {'conn_str': conn_str}, {'id': source_id})
                # 清除数据源缓存
                key = '%s:%s' % ('data_source:', source_id)
                cache = conn_redis()
                cache.del_data(key)


def sync_erp_info_to_project(code):
    with get_project_db(code) as project_db:
        sql = "select id,conn_str,db_type from dap_m_data_source where app_level_code = '1000.1401' and conn_str like '%\"is_master_local_db\": 1%';"
        data_source = project_db.query_one(sql=sql)
        if data_source:
            conn_str = json.loads(data_source.get('conn_str'))
            db_type = data_source.get('db_type')
            erp_api_id = conn_str.get('erp_api_info_id')
            # 没有找到绑定关系则重新生成绑定
            if not erp_api_id:
                erp_api_id = ErpApiId.SAAS.value if db_type.lower() == 'mysql' else ErpApiId.OP.value
                conn_str['erp_api_info_id'] = erp_api_id
                project_db.update('dap_m_data_source', {'conn_str': json.dumps(conn_str)}, {'id': data_source.get('id')})
            sql = """select erp_api_host as erpapi_host, erp_api_access_id as erpapi_access_id,
             erp_api_access_secret as erpapi_access_secret from dap_bi_erp_api_info where id = '{}'""".format(erp_api_id)
            data = project_db.query_one(sql)
            if data:
                repository.update_data('dap_bi_tenant_setting', data, {'code': code})
        else:
            sql = """select erp_api_host as erpapi_host, erp_api_access_id as erpapi_access_id,
             erp_api_access_secret as erpapi_access_secret from dap_bi_erp_api_info limit 1;"""
            erp_info = project_db.query_one(sql)
            if erp_info:
                repository.update_data('dap_bi_tenant_setting', erp_info, {'code': code})


def send_admin_email_content(model: ProjectModel):
    """
    获取管理员秘钥邮件内容
    :param model:
    :return:
    """
    # 查询对应租户是否已经生成过秘钥了
    # secret = repository.get_data_scalar('dap_bi_admin_developer_secret', {'tenant_code': model.code, 'is_active': 0}, 'secret')
    # if not secret:
    #     # 生成秘钥并插入表
    #     secret = seq_id()
    #     data = {'secret': secret, 'tenant_code': model.code}
    #     repository.add_data('dap_bi_admin_developer_secret', data)
    if not model.admin_email:
        raise UserError(message='管理员邮箱不能为空')
    # 获取邮件模板
    email_template_data = email_repository.get_email_template(EmailTemplateType.AdminDeveloper.value)
    if not email_template_data:
        raise UserError(message='邮件模板未找到')
    replace_dict = {
        '{企业域地址}': AppHosts.get(SkylineApps.DP, False),
        '{企业代码}': model.code,
    }
    content = mail.replace_content(email_template_data.get('content'), replace_dict)
    try:
        mail.send(
            mail.Mail(
                subject=email_template_data.get('subject'),
                body=content,
                receiver=[mail.MailContact(name=model.name, mail=model.admin_email)],
            ),
            code=model.code,
            subtype='html',
        )
    except EmailSendError as e:
        logger.error(f'邮件发送失败:{str(e)}')
    except Exception as e:
        logger.error(f'邮件发送异常:{str(e)}')
    return True


def get_protect_project_list(query_model):
    """
    获取开启重点大屏保护的租户列表
    :param project.models.ProjectQueryModel query_model:
    :return: project.models.ProjectQueryModel
    """
    query_model = project_repository.get_protect_project_list(query_model)
    project_list = query_model.items
    if project_list:
        area_list = get_project_area_list()
        area_map = {v.get("id"): v.get("area_name") for v in area_list}
        for project in project_list:
            project['area_name'] = area_map.get(project.get("area_id"), '其他')
        query_model.items = project_list
    return query_model


def set_protect_project(kwargs):
    """
    设置租户开启或关闭重点大屏
    :param kwargs:
    :return:
    """
    code = kwargs.get("code")
    # 是否开启重点大屏租户（0：不是，1：是）
    is_key_screen = int(kwargs.get("is_key_screen", 0))
    is_force = int(kwargs.get("is_force", 0))
    if not code:
        raise UserError(message='参数错误')

    project = get_project_by_code(code)
    if not project:
        raise UserError(message='租户不存在')

    # 检查租户是否存在重点大屏
    if is_key_screen == 0 and not is_force:
        key_dashboard_num = get_key_dashboard_num(code)
        if key_dashboard_num > 0:
            raise UserError(message='该租户已存在重点大屏，取消后功能将不可用，确认取消吗？', code=501)
    # 关闭租户的重点大屏功能
    if is_key_screen == 0:
        delete_version_cache_by_project(code)
    else:
        # 开启租户重点大屏管理功能
        _reg_key_dashboard_task(code)

    project.is_key_screen = is_key_screen
    # 租户信息缓存清除
    clear_project_cache(code)
    return repository.update_model('dap_bi_tenant_setting', project, {'id': project.id}, ['is_key_screen'])


def _reg_key_dashboard_task(project_code):
    """
    注册该环境所有租户重点大屏统计任务
    :param project_code:
    :return:
    """
    try:
        dmp_api = DMPAPI(project_code)
        dmp_api.reg_key_dashboard_task()
    except Exception as e:
        logger.error("初始化重点大屏统计任务失败，errs："+str(e))


def get_key_dashboard_num(project_code: str):
    """
    获取租户的重点大屏数量
    :param project_code:
    :return:
    """
    sql = """
    select count(*) as c from dap_bi_dashboard where build_in=0 and type = 'FILE' and platform = 'pc' 
    and is_key=1 and application_type = 0
    """
    with get_project_db(project_code) as db:
        return db.query_scalar(sql)


def delete_last_version_cache(dashboard_id, code):
    """
    删除上一版缓存
    :param dashboard_id:
    :param code:
    :return:
    """
    try:
        keys = f"last_version:{dashboard_id}"
        cache = RedisCache(key_prefix=code)
        key_list = cache.lrange(keys, 0, -1) or []
        for key in key_list:
            cache.delete(key)
    except Exception as e:
        logger.error(f"删除缓存失败：{e}")


def delete_version_cache_by_project(project_code):
    try:
        # 获取租户下的所有重点大屏
        with get_project_db(project_code) as db:
            dashboard_ids = db.query_columns('select id from dap_bi_dashboard where is_key=1') or []

        for dashboard_id in dashboard_ids:
            # 清空redis缓存
            delete_last_version_cache(dashboard_id, project_code)

        return "删除成功", True
    except Exception as e:
        logger.exception(e)
        return "删除失败", False


def enable_project(model: ProjectModel):
    """
    租户禁用、启用操作
    :param model:
    :return:
    """
    if not repository.data_is_exists('dap_p_tenant', {'code': model.code}):
        raise UserError(message='项目不存在')
    fields = [
        'id',
        'enabled',
        'project_disable_reason'
    ]
    data = {}
    code = model.code
    old_data = repository.get_data('dap_p_tenant', {'code': code}, fields) or {}
    model.id = old_data.get('id')
    if old_data and int(model.project_enabled) != old_data.get("enabled"):
        # 租户启用，判断是否启用开发者
        if model.project_enabled == ProjectEnabled.Enable.value:
            model.project_disable_reason = ''
            action = ProjectAction.Enable.value
        else:
            action = ProjectAction.Disable.value
            # 按租户code删除所有的 redis 缓存
            delete_cache_by_code(code)

        repository.update_model('dap_p_tenant', model, {'id': model.id}, fields)
        clear_project_cache(code)
        # 共用以前的流程，支持按不同场景调用不同方法。开租户场景，租户禁用，租户启用 场景
        rs = DMPAPI(code).flow_enable_by_project(action)
        logger.error(f"租户操作：{action} 请求结果："+json.dumps(rs, ensure_ascii=False))
        data = rs.get("data") or {}
        save_enable_project_log(action+'_project', model.code, model.id)

    logger.error("租户禁用、启用操作参数：" + json.dumps(model.get_dict(), ensure_ascii=False))
    return data


def get_project_is_enabled(code):
    return repository.get_data_scalar("dap_p_tenant", {"code": code}, "enabled")


def disable_migrated_project(request, **kwargs):
    logger.error(f'批量启/禁用租户:{kwargs}')
    codes = kwargs.get('codes')
    domain = kwargs.get('migrate_to')
    if not codes:
        raise UserError(message='请指定租户')
    project_enabled = ProjectEnabled.Disable.value
    if not domain:
        raise UserError(message='请指定新的登录地址')
    reason = MIGRATE_TENANT_DISABLE_TIPS.format(domain=domain)

    save_project_batch_disable_operate_log(request, **kwargs)
    result = []
    codes = list(set(codes))
    for code in codes:
        model = ProjectModel()
        model.code = code
        model.project_enabled = project_enabled
        model.project_disable_reason = reason
        res = enable_project(model)
        result.append(res.get('url', ''))
    return result


def save_enable_project_log(action, code, project_id):
    try:
        log_model = extract_log_info()
        log_model.action = action
        log_model.module = OperationLogModule.Project.value
        log_model.do_object = project_id
        log_model.org_code = code
        save_log(log_model)
    except Exception as e:
        logger.error(f'保存租户操作日志异常:{str(e)}')


def save_project_batch_disable_operate_log(request, **kwargs):
    try:
        log_model = extract_log_info(request)
        log_model.module = OperationLogModule.Project.value
        log_model.action = 'batch_disable_project'
        log_model.content = json.dumps(kwargs, ensure_ascii=False)
        save_log(log_model)
    except Exception as e:
        logger.error(f'保存租户操作日志异常:{str(e)}')


def delete_cache_by_code(code):
    """
    按租户code 删除所有的redis缓存
    :param code:
    :return:
    """
    pattern = f"{code}*"
    return RCache().del_by_scan(pattern, 100000)

def initialize_creating_record(**kwargs):
    logger.info('正在补全开户记录')
    from project.services.template_sync_service import add_project_create_record, update_create_record_error, \
        get_completed_record_appcodes
    result = {}
    for code, apps in kwargs.items():
        logger.info(f' {code} -> {apps}')
        exists_apps = get_completed_record_appcodes(code)
        if exists_apps:
            logger.info(f'租户[{code}]已存在的开户应用:{exists_apps}')
            new_apps = list(set(apps).difference(set(exists_apps)))
        else:
            new_apps = apps
        logger.info(f'补全开户记录 {code} -> {new_apps}')
        if new_apps:
            repository.add_data('dap_bi_project_create_record',
                                {'id': seq_id(), 'code': code, 'from_init': 'erpsaas', 'params': '{"action":"create"}',
                                 'app_codes': new_apps, 'status': 'INITED', 'source':'ERP'})
        result[code] = new_apps
    logger.info('补全开户记录已完成')
    return result

def resync_template_by_tag(**kwargs):
    tenants = kwargs.get('tenant_code', [])
    if not tenants:
        return
    env_code = kwargs.get('env_code', '')
    set_to_15 = kwargs.get('set_1_5', 1)
    logger.info(f'根据租户标签重新触发增购:{tenants}')
    template_apps = repository.get_data('dap_bi_publish_center_template', {'env_code': env_code, 'db_type': splicing_lang_tag('mysql')}, ['app_code'],
                               multi_row=True)
    template_apps = [item['app_code'] for item in template_apps]
    for tenant in tenants:
        try:
            if set_to_15 == 1:
                # 1.租户开启1.5配置
                try:
                    project_setting_repository.save_project_setting(tenant, 'is_data_cloud_1_5_enabled', 1, getattr(g, 'account', ''))
                except Exception as e:
                    logger.error(f'修改租户1.5设置异常:{str(e)}')
                #2.修改标签标识为1.5
                repository.update_data('dap_bi_project_tags_relation', {'is_data_cloud_1_5_enabled': 1}, {'project_code': tenant, 'tag_type': 'PRODUCT'})
            #3.查询标签
            tags = repository.get_data('dap_bi_project_tags_relation', {'project_code': tenant, 'tag_type':'PRODUCT'}, ['tag_code'],multi_row=True)
            if not tags:
                logger.info(f'租户[{tenant}]查询标签为空')
                continue
            tag_codes = [tag['tag_code'] for tag in tags]
            logger.info(f'租户[{tenant}]查询标签：{tag_codes}')
            #4.把开户记录中对应code去除
            merge_tag(tenant, set_to_15, tag_codes)
            #5.拼接开户参数
            records = repository.get_data('dap_bi_project_create_record', {'code': tenant, 'status': 'COMPLETED'}, ['params'], multi_row=False, order_by=[('created_on', 'desc')])
            params = {}
            if records:
                params = json.loads(records.get('params'))
            else:
                #如果没有成功的开户记录，补一条空的开户记录
                repository.add_data('dap_bi_project_create_record', {'id': seq_id(), 'code': tenant, 'from_init': 'erpsaas', 'params': '{"action":"create"}', 'app_codes': '0000', 'status': 'COMPLETED', 'source': 'ERP'})
            app_list = []
            code_str = ''
            for code in tag_codes:
                if code in template_apps:
                    app_list.append({'app_code': code, 'app_name': ''})
                    code_str += f',{code}'
            params = {}
            create_record_id = seq_id()
            params.update({'app_code_list': app_list})
            params.update({'init_1_5_app': set_to_15})
            params.update({'action': 'update'})
            params.update({'task_id': seq_id()})
            params.update({'env_code': env_code})
            params.update({'create_record_id': create_record_id})
            params.update({'code': tenant})
            params.update({'from_init': 'erpsaas'})
            repository.add_data('dap_bi_project_create_record', {'id': create_record_id, 'code': tenant, 'from_init': 'erpsaas',
                                                          'params': json.dumps(params, ensure_ascii=False), 'app_codes': code_str,
                                                          'status': 'INIT', 'source': 'ERP'})
            #6.调用增购接口
            import app_celery
            app_celery.additional_purchase.apply_async(kwargs=params)
            # app_celery.additional_purchase(**params)
            logger.info(f'租户[{tenant}]创建重新分发报表任务')
        except Exception as e:
            logger.error(f'租户[{tenant}]重新分发报表异常:{str(e)}')


def merge_tag(tenant_code, set_to_15, tag_codes):
    records = repository.get_data('dap_bi_project_create_record', {'code': tenant_code, 'status': 'COMPLETED'},
                                  ['id', 'app_codes'], multi_row=True)
    if not records:
        return
    for record in records:
        id = record.get('id')
        app_codes = record.get('app_codes')
        app_codes = app_codes.split(',')
        to_update = list(set(app_codes) - set(tag_codes))
        if len(app_codes) != len(to_update):
            if len(to_update) == 0:
                # 加一个不存在的默认值
                repository.update_data('dap_bi_project_create_record', {'app_codes': '0000'}, {'id': id})
            else:
                repository.update_data('dap_bi_project_create_record', {'app_codes': ','.join(to_update)}, {'id': id})
        if len(to_update) > 0:
            tags_list = []
            for app_code in to_update:
                tags_list.append({'id': seq_id(), 'project_code': tenant_code, 'tag_type': 'PRODUCT', 'tag_code': app_code,
                                  'is_data_cloud_1_5_enabled': set_to_15, 'created_by': getattr(g, 'userid', ''),
                                  'modified_by': getattr(g, 'userid', '')})
            logger.info(f'根据开户记录补全标签:{to_update}')
            repository.add_list_data('dap_bi_project_tags_relation', tags_list,
                                            ['id', 'project_code', 'tag_type', 'is_data_cloud_1_5_enabled', 'tag_code',
                                             'created_by', 'modified_by'])

    logger.info(f'租户[{tenant_code}]开户记录已修改完成')
