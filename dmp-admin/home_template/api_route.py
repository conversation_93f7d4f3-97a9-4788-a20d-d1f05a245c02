#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.hug import APIWrapper
from home_template.models import HomeTemplateModel, HomeTemplateQueryModel
from home_template.services import home_template_service

api = APIWrapper(__name__)


@api.admin_route.get('/list')
def get_home_template_list(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/home_page/update 修改首页模板
     @apiGroup  openapi
     @apiBodyParam {
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": [{
                     "id": "id",
                     "name{名称}": "name",
                     "icon":"icon",
                     "logo":"logo",
                     "background":"background",
                     "statement":"statement",
                     "account_memory{记住登录}": "1",
                     "enable{启用}":"0"}
     }]
     */
     """
    return True, None, home_template_service.get_home_template_list(HomeTemplateQueryModel(**kwargs)).get_result_dict()


@api.admin_route.get('/get')
def get_home_template(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/home_page/update 修改首页模板
     @apiGroup  openapi
     @apiBodyParam {
        "id": "id"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": {
                     "id": "id",
                     "name{名称}": "name",
                     "icon":"icon",
                     "logo":"logo",
                     "background":"background",
                     "statement":"statement",
                     "account_memory{记住登录}": "1",
                     "enable{启用}":"0"}
     }
     */
     """
    return True, None, home_template_service.get_home_template(kwargs.get('id'))


@api.admin_route.post('/add')
def add_home_template(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/home_page/add 添加首页模板
     @apiGroup  openapi
     @apiBodyParam {
        "name{名称}": "name",
        "icon":"icon",
        "logo":"logo",
        "background":"background",
        "statement":"statement",
        "account_memory{记住登录}": "1",
        "enable{启用}":"0"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "message_id"
     }
     */
    """
    return True, '添加成功', home_template_service.add_home_template(HomeTemplateModel(**kwargs))


@api.admin_route.post('/update')
def update_home_template(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/home_page/update 修改首页模板
     @apiGroup  openapi
     @apiBodyParam {
        "id": "id",
        "name{名称}": "name",
        "icon":"icon",
        "logo":"logo",
        "background":"background",
        "statement":"statement",
        "account_memory{记住登录}": "1",
        "enable{启用}":"0"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "message_id"
     }
     */
     """
    return True, '修改成功', home_template_service.update_home_template(HomeTemplateModel(**kwargs))
