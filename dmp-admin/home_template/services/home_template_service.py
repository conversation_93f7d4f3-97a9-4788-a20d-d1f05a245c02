#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base import repository
from dmplib.redis import conn_custom_prefix
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from home_template.models import HomeTemplateModel
from home_template.repositories import home_template_repository

# 与dmp项目缓存的key保持一致
CACHE_KEY_TEMPLATE = 'home_template'
CACHE_KEY_TEMPLATE_PREFIX = "dmp:"

def _delete_cache():
    redis_cache = conn_custom_prefix(CACHE_KEY_TEMPLATE_PREFIX)
    redis_cache.delete(CACHE_KEY_TEMPLATE)

def get_home_template_list(query_model):
    """
    获取首页模板列表
    :param query_model:
    :return:
    """
    return home_template_repository.get_home_template_list(query_model)


def get_home_template(template_id):
    if not template_id:
        raise UserError(message='缺少首页模板ID')

    fields = list(HomeTemplateModel().get_dict().keys())
    data = repository.get_data('dap_bi_home_template', {'id': template_id}, fields)
    if not data:
        raise UserError(message='首页模板不存在')

    return data


def add_home_template(model):
    """
    添加首页模板
    :param mail_template.models.HomeTemplateModel model:
    :return: model.id
    """
    model.id = seq_id()
    model.validate()
    fields = list(HomeTemplateModel().get_dict().keys())
    result = repository.add_model('dap_bi_home_template', model, fields)
    _delete_cache()
    if not result:
        raise UserError(message='添加首页模板失败')
    return model.id


def update_home_template(model):
    """
    更新首页模板
    :param model:
    :return:
    """
    model.validate()
    if not repository.data_is_exists('dap_bi_home_template', {'id': model.id}):
        raise UserError(message='首页模板不存在')
    fields = list(HomeTemplateModel().get_dict().keys())
    repository.update_model('dap_bi_home_template', model, {'id': model.id}, fields)
    _delete_cache()
    return model.id
