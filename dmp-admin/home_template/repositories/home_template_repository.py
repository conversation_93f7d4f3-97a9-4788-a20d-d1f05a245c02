#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.db.mysql_wrapper import get_db


def get_home_template_list(query_model):
    sql = 'SELECT `id`,`name`,`icon`,`logo`,`background`,`statement`,' \
          '`account_memory`,`enable`,`icp`,`tenant_code` FROM `dap_bi_home_template` '
    params = {}
    wheres = []
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY created_on DESC'
    with get_db() as db:
        query_model.total = db.query_scalar('select count(*) as total from ({}) a'.format(sql), params)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model
