#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base.models import BaseModel, QueryBaseModel


class HomeTemplateModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = ''
        self.name = ''
        self.icon = ''
        self.logo = ''
        self.background = ''
        self.statement = ''
        self.account_memory = ''
        self.enable = ''
        self.icp = ''
        self.tenant_code = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        return rules


class HomeTemplateQueryModel(QueryBaseModel):
    pass
