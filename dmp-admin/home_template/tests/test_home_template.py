#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import unittest
from home_template.models import HomeTemplateModel, HomeTemplateQueryModel
from home_template.services import home_template_service
import logging

from tests.base import BaseTest

logger = logging.getLogger(__name__)


class TestProject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_add_home_template(self):
        dict = {"name": "test", "icon": "icon", "logo": "logo", "background": "background",
                "statement": "statement", "account_memory": "1", "enable": "1"}
        model = HomeTemplateModel(**dict)
        reuslt = home_template_service.add_home_template(model)
        print(reuslt)

    def test_get_home_template_list(self):
        result = home_template_service.get_home_template_list(HomeTemplateQueryModel()).get_result_dict()
        print(result)

    def test_get_home_template(self):
        template_id = '39e709ec-e907-1d9b-5839-89e4edd0864a'
        result = home_template_service.get_home_template(template_id)
        print(result)

    def test_update_home_template(self):
        dict = {"name": "test2", "icon": "icon2", "logo": "logo2", "background": "background2",
                "id": '39e709ec-e907-1d9b-5839-89e4edd0864a',
                "statement": "statement2", "account_memory": "0", "enable": "0"}
        result = home_template_service.update_home_template(HomeTemplateModel(**dict))
        print(result)


if __name__ == '__main__':
    unittest.main()
