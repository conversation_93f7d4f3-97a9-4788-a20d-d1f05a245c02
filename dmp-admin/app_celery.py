# -*- coding: UTF-8 -*-
import json
import logging
import os

from base.enums import OmpTenantsTaskStatus

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)
os.environ.setdefault('PROMETHEUS_MULTIPROC_DIR', '/tmp')
from components.utils import jwt_patch  # noqa: E402

jwt_patch()
import traceback

from dmplib.hug import g
# 需要保证在import api_route前
from base.core_util import update_ssl_certifi  # noqa: E402
from issue_dashboard.services import import_service  # noqa: E402
from upgrade.services import upgrade_service, graph_upgrade_service  # noqa: E402
from components import celery_base  # noqa: E402
from components.celery_base import restore_all_unacknowledged_messages  # noqa: E402
from components.celery import celery  # noqa: E402
from dmplib.redis import RedisCache  # noqa: E402
from celery.signals import worker_init  # noqa: E402
from project.services import project_init_service, template_sync_service  # noqa: E402
from issue_dashboard.services import deliver_service  # noqa: E402
from utils_helper.utils.dataset_used_table_service import data_to_table  # noqa: E402
from issue_dashboard.services import template_service  # noqa: E402
from project.models import ProjectModel
from issue_dashboard.services import init_import_service  # noqa: E402

from components.fast_logger import FastLogger
from components.qw_message import send_publish_center_error

logger = logging.getLogger(__name__)

update_ssl_certifi()

redis_cache = RedisCache()

REDIS_TASK_KEY_PREFIX = 'celery-task-meta-'


# Not needed for RabbitMQ
@worker_init.connect
def configure(sender=None, conf=None, **kwargs):
    restore_all_unacknowledged_messages()


@celery.task(base=celery_base.BaseTask)
def sync_project_data(task_id, project_dict):
    """异步租户初始化

    Decorators:
        celery

    Args:
        task_id (str) 任务id
        project_model (project.models.ProjectModel): 项目MODEL
    """
    setattr(g, "omp_task_id", project_dict.get('task_id'))
    setattr(g, "omp_log", project_dict.get('omp_tenants_log'))
    project_init_service.async_project_init(task_id, project_dict)


@celery.task(base=celery_base.BaseTask)
def sync_project_data_from_property(**kwargs):
    """异步租户初始化

    Decorators:
        celery

    Args:
        task_id (str) 任务id
        project_model (project.models.ProjectModel): 项目MODEL
    """
    from project.services import project_service

    model = ProjectModel(**kwargs)
    project_service.add_project(model, need_extra=True)


@celery.task(base=celery_base.BaseTask)
def deliver_dashboard(**kwargs):
    """
    分发报告
    :param kwargs:
    :return:
    """
    deliver_id = kwargs.get("deliver_id")
    distribute_type = kwargs.get("distribute_type", 0)
    is_lock_dataset = kwargs.get("is_lock_dataset", 0)
    userid = kwargs.get("userid", None)
    deliver_service.deliver(deliver_id, distribute_type, is_lock_dataset, userid)


@celery.task(base=celery_base.BaseTask)
def upgrade_to_graph(**kwargs):
    task_id = kwargs.get("task_id")
    graph_upgrade_service.execute(task_id)

def exception_hook(func):
    def wrapper(*args, **kwargs):
        try:
            func(*args, **kwargs)
        except Exception as e:
            is_publish_center = kwargs.get("is_publish_center")
            task_id = kwargs.get("task_id")
            if is_publish_center:
                fast_log = FastLogger.PublishCenterFastLogger(**kwargs)
                fast_log.error_traceback = traceback.format_exc()
                fast_log.error_level = 'ERROR'
                fast_log.record()
                project_code = kwargs.get('project_code')
                send_publish_center_error(task_id, project_code, fast_log.biz_name, str(e), fast_log.error_traceback)
            raise e

    return wrapper


@celery.task(base=celery_base.BaseTask)
@exception_hook
def deliver_single_dashboard(**kwargs):
    """
    单租户分发
    :param kwargs:
    :return:
    """
    from issue_dashboard.services import backup_before_import

    from issue_dashboard.repositories import deliver_repository
    from issue_dashboard.services.deliver_service import parse_data

    deliver_id = kwargs.get('deliver_id')
    project_code = kwargs.get('project_code')
    distribute_type = kwargs.get('distribute_type')
    failed_projects = kwargs.get('failed_projects')
    is_lock_dataset = kwargs.get('is_lock_dataset')
    is_lock_application = kwargs.get('is_lock_application')
    error_list = kwargs.get('error_list')
    active_reports = kwargs.get('active_reports')
    is_report_center = kwargs.get('is_report_center')
    userid = kwargs.get('userid')
    is_async = kwargs.get('async')
    skip_feeds = kwargs.get('skip_feeds')
    from_template = kwargs.get('from_template', 0)
    export_data = kwargs.get('export_data')
    ppts = kwargs.get('ppts')
    deliver_data = deliver_repository.get_deliver_meta(deliver_id)
    is_backup_dashboard = kwargs.get('is_backup_dashboard')

    if not deliver_data:
        raise Exception("[分发报告] deliver数据不存在. id: '%s'" % deliver_id)

    if export_data:
        zip_meta = export_data
    else:
        zip_meta = parse_data(deliver_data['source_url'])
    if not zip_meta:
        raise Exception("[分发报告] 没有读取到zip包的数据, zip文件地址: %s" % deliver_data['source_url'])

    dashboards = zip_meta.get('dashboards')
    large_screens = zip_meta.get('large_screens')
    datasets = zip_meta.get('datasets')
    is_new_jump = zip_meta.get('is_new_jump')
    export_excel_data = zip_meta.get('export_excel_data') or []
    source_project = deliver_data['source_project']
    replace_data_source = bool(deliver_data['replace_data_source'])
    # 数据报表
    data_reporting = zip_meta.get('data_reporting', {})
    # 手工填报
    fillings = zip_meta.get('fillings', {})
    # 简讯/门户跳过
    if skip_feeds == 1:
        feeds = {}
        applications = None
    else:
        feeds = zip_meta.get('feeds', {})
        applications = zip_meta.get('applications')
    # 版本
    version = zip_meta.get('version')

    if is_backup_dashboard:
        # 备份导出的资源
        kwargs_copy = zip_meta.copy()
        kwargs_copy.pop('project_code',None)
        kwargs_copy['__backup_type__'] = 'deliver'
        backup_before_import.backup_import_resources(project_code, **kwargs_copy)

    args = dict(
        deliver_id=deliver_id,
        project_code=project_code,
        source_project=source_project,
        dashboards=dashboards,
        large_screens=large_screens,
        distribute_type=distribute_type,
        failed_projects=failed_projects,
        datasets=datasets,
        applications=applications,
        fillings=fillings,
        replace_data_source=replace_data_source,
        is_lock_dataset=is_lock_dataset,
        is_lock_application=is_lock_application,
        is_new_jump=is_new_jump,
        error_list=error_list,
        feeds=feeds,
        data_reporting=data_reporting,
        active_reports=active_reports,
        is_report_center=is_report_center,
        ppts=ppts,
        userid=userid,
        oss_file=deliver_data['source_url'],
        export_excel_data=export_excel_data,
        version=version,
        is_async=is_async,
        from_template=from_template
    )
    deliver_service.deliver_single(**args)
    # 重置level_code
    from project.services import template_sync_service
    template_sync_service.fix_level_code(project_code)


@celery.task(base=celery_base.BaseTask)
def auto_deliver_single_dashboard(**kwargs):
    deliver_service.auto_deliver_split(kwargs)


@celery.task(base=celery_base.BaseTask)
def import_dashboard_dataset(**kwargs):
    """
    导入报告和数据集
    :param kwargs:
    :return:
    """
    import_service.import_data(**kwargs)


@celery.task(base=celery_base.BaseTask)
def install_dashboard_dataset(**kwargs):
    """
    安装报告和数据集
    :param kwargs:
    :return:
    """
    template_service.install_template(**kwargs)


@celery.task(base=celery_base.BaseTask)
def upgrade_data(**kwargs):
    """
    数据升级
    :param kwargs:
    :return:
    """
    try:
        result, msg = upgrade_service.start_upgrade(
            **{
                "task_id": kwargs.get("task_id"),
                "command_name": kwargs.get("command_name"),
                "upgrade_type": kwargs.get("upgrade_type"),
                "tenants": kwargs.get("tenants"),
                "extra": kwargs.get("extra"),
                "project_codes": kwargs.get("project_codes"),
                "cache_class": kwargs.get("cache_class"),
                "cache_prefix": kwargs.get("cache_prefix"),
            }
        )
        if not result:
            logging.error(msg='升级失败，错误信息: {msg}'.format(msg=msg))
    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(msg=str(e))


@celery.task(base=celery_base.BaseTask)
def withdraw_dashboard(**kwargs):
    """
    分发报告
    :param kwargs:
    :return:
    """
    withdraw_id = kwargs.get("withdraw_id")
    from issue_dashboard.services import withdraw_service
    withdraw_service.withdraw_dashboard(withdraw_id)


@celery.task(base=celery_base.BaseTask)
def export_data_of_used_table(**kwargs):
    tenants = kwargs.get("tenants", "")
    table_name = kwargs.get("table_name", "")
    data_to_table(tenants, table_name)


@celery.task(base=celery_base.BaseTask)
def init_import_dashboard(**kwargs):
    """
    初始化内置的报告
    :param kwargs:
    :return:
    """
    init_import_service.init_dashboard(**kwargs)


@celery.task(base=celery_base.BaseTask)
def restore_dashboard_design_data(**kwargs):
    """
    异步将报告运行时数据还原到设计时
    :param kwargs:
    :return:
    """
    import_service.restore_dashboard_design_data(**kwargs)


@celery.task(base=celery_base.BaseTask)
def send_admin_email(**kwargs):
    """
    异步发送秘钥邮件
    :param kwargs:
    :return:
    """
    from project.services import project_service
    model = ProjectModel(**kwargs)
    project_service.send_admin_email_content(model)


@celery.task(base=celery_base.BaseTask)
def additional_purchase(**kwargs):
    """
    增购
    :param kwargs:
    :return:
    """
    kwargs['from_init'] = kwargs.get('from_init') if kwargs.get('from_init') else kwargs.get('from')
    kwargs['erp_language'] = kwargs.get('erp_language') if kwargs.get('erp_language') else kwargs.get('ERP_Language')
    setattr(g, "omp_task_id", kwargs.get('task_id'))
    setattr(g, "omp_log", kwargs.get('omp_tenants_log'))
    # 记录初始化任务状态
    project_init_service.set_task_record(kwargs.get('task_id'))
    template_sync_service.do_initialize_additional_purchase(ProjectModel(**kwargs))
    project_init_service.delete_task_record(kwargs.get('task_id'))


@celery.task(base=celery_base.BaseTask)
def tenant_cancellation(**kwargs):
    """
    销户
    :param kwargs:
    :return:
    """
    from project.services import project_service  # noqa: E402
    code = kwargs.get('tenantCode')
    task_id = kwargs.get('taskId')
    project_service.omp_tenant_cancellation(task_id, code)


@celery.task(base=celery_base.BaseTask)
def heartbeat_task(**kwargs):
    logger.info("heartbeat task")


@celery.task(base=celery_base.BaseTask)
def do_delivery_template_data_sync(**kwargs):
    from project.services.project_init_service import set_task_record, delete_task_record
    from project.models import InitializeTenantModel

    model = kwargs.get('model')
    model = InitializeTenantModel(**model)

    # 记录初始化任务状态
    set_task_record(model.task_id)

    files = kwargs.get('files')

    from project.services.project_init_service import set_omp_task_status, save_product_tags
    from project.services.template_sync_service import do_initialize_data, update_create_record_error,update_create_record_completed

    setattr(g, 'code', model.tenant_code)
    setattr(g, 'account', 'celery')
    try:
        do_initialize_data(model, files)
        set_omp_task_status('重试后已分发完成', model.task_id, status=OmpTenantsTaskStatus.Successful.value)
        update_create_record_completed(model.record_id, model.app_list, '重试后已分发完成')
        save_product_tags(model.tenant_code, model.app_list, model.init_1_5_app)
    except Exception as e:
        logger.error(f'分发文件异常：{e}')
        set_omp_task_status('重试后已分发完成', model.task_id, status=OmpTenantsTaskStatus.Failure.value)
        update_create_record_error(model.record_id, str(e))

    delete_task_record(model.task_id)


@celery.task(base=celery_base.BaseTask)
def rundeck_upgrade_to_xxljob(**kwargs):
    from utils_helper.services.rundeck_to_xxljob import rundeck_2_xxljob

    cache = RedisCache(key_prefix='rundeck_2_xxljob')
    key = 'record_task_status'
    cache.set(key, '运行中', 3600 * 12)
    result = rundeck_2_xxljob()
    cache.set(key, json.dumps(result), 3600 * 12)


@celery.task(base=celery_base.BaseTask)
def dm_core_dump_verify(**kwargs):
    from dmplib.db.mysql_wrapper import SimpleMysql
    from uuid import uuid4
    from dmplib import config
    import concurrent.futures
    from dmplib.hug.context import DBContext
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack

    g.account = 'replace_verify'

    def replace_into_verify(table_name):

        _g = _AppCtxGlobals()
        _g.account = 'skylineadmin'
        _app_ctx_stack.push(_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(g)

        def _gen_data(count=1000):
            _list_data = []
            for i in range(count):
                params = {
                    'area_id': i,
                    'area_name': f'中国区_{i}',
                    'parent_id': 0,
                    'order_id': i,
                    'memo': f'中国区_{i}',
                    'buguid': str(uuid4())
                }
                _list_data.append(params)
            return _list_data

        db = SimpleMysql(
            host=config.get('DB.host'),
            port=config.get('DB.port'),
            db='SYSDBA',
            user=config.get('DB.user'),
            passwd=config.get('DB.password')
        )
        try:
            for i in range(50):
                list_data = _gen_data(count=1000)
                db.replace_multi_data(table_name, list_data, list(list_data[0].keys()), condition_field=['id'])
        except Exception as e:
            logger.error(f'验证执行错误：{e}')
        finally:
            db.close()

    with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
        tasks = []
        for i in range(5):
            table_name = f'dap_bi_areas_{i+1}'
            future = executor.submit(replace_into_verify, table_name)
            tasks.append(future)

        try:
            for future in concurrent.futures.as_completed(tasks):
                # 获取任务执行结果（可选）
                result = future.result()
                print(f"Task finished: {result}")
        except Exception as e:
            print(f"An exception occurred: {e}")




