from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
import json

worker_g = _AppCtxGlobals()
worker_g.account = 'skylineadmin'
_app_ctx_stack.push(worker_g)
db_ctx = DBContext()
db_ctx.inject(worker_g)

from base import repository

repository.add_list_data(
    'dap_bi_dmp_open_tenant_init_app_record',
    [
        {'task_id': '1',
         'app_code': export.get('app_code'),
         'model': 'ss',
         'export': json.dumps(export),
         'apps': 'aa',
         'status': 0
         } for export in [{'app_code': 1}, {'app_code': 2}, {'app_code': 3}]],
    fields=['task_id', 'app_code', 'model', 'export', 'apps', 'status']
)

repository.update_data(
    'dap_bi_dmp_open_tenant_init_app_record',
    {'status': 1},
{'task_id': 1, 'app_code': 1}
)