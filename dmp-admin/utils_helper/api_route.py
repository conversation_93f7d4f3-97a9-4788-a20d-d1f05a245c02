import json

import hug

import app_celery
from dmplib.hug import APIWrapper
from dmplib.utils.errors import UserError
from components.dmp_api import DMPAPI
from utils_helper.services.exoprt_public_config_service import export_public_config_insert_sql_file
from utils_helper.services.export_config_sql_service import export_config_insert_sql_file
from utils_helper.services.export_rundeck_service import export_rundec_insert_sql_file
from utils_helper.services.sql_transform_service import SqlTransformService

from utils_helper.utils import dataset_used_table_service
from utils_helper.services.rundeck_to_xxljob import rundeck_2_xxljob


api = APIWrapper(__name__)


@api.admin_route.get("/rabbitmq/purge")
def rabbitmq_purge(**kwargs):
    """
    purge queue
    """
    name = kwargs.get("queue_name")
    if not name:
        raise UserError(message="缺少参数queue_name")
    res = DMPAPI(project_code='admin').rabbitmq_purge(name)
    return True, "", res


@api.admin_route.get("/get_dataset_used_table")
def get_dataset_used_table(request, response, **kwargs):
    """
    get_dataset_used_table
    """
    tenants = kwargs.get("tenants", "")
    table_name = kwargs.get("table_name", "")
    dataset_used_table_service.export_to_table(response, tenants, table_name)
    return True, "", ""


@api.admin_route.get("/get_dataset_used_table/excel")
def get_dataset_used_table(request, response, **kwargs):
    """
    get_dataset_used_table
    """
    tenants = kwargs.get("tenants", "")
    dataset_used_table_service.export_data_excel(response, tenants)


@api.admin_route.get('/export_move_config')
def export_move_config(response, **kwargs):
    export_config_insert_sql_file(kwargs, response)


@api.admin_route.get('/export_public_config')
def export_move_public(response, **kwargs):
    export_public_config_insert_sql_file(kwargs, response)


@api.admin_route.get('/export_move_rundeck')
def export_move_rundeck(response, **kwargs):
    export_rundec_insert_sql_file(kwargs, response)


@api.admin_route.get('/dataset_sql_transform')
def dataset_sql_transform(response, **kwargs):
    file_url = kwargs.get("file_url")
    SqlTransformService(file_url).transform(response)


@api.route.get('/health/check')
def health_check(request, response, **kwargs):
    """
    健康检查
    :param kwargs:
    :return:
    """
    msg = 'ok'
    if not kwargs.get('auth'):
        response.status = hug.falcon.HTTP_401
        msg = 'not auth'
    return True, '', msg


@api.admin_route.get('/rundeck_to_xxl_job')
def rundeck_to_xxl_job(request, response, **kwargs):
    """
    rundeck升级到xxl-job
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    is_async = kwargs.get('is_async', 0)
    if is_async:
        app_celery.rundeck_upgrade_to_xxljob.apply_async()
        return True, '', 'ok'
    return True, '', rundeck_2_xxljob()


@api.admin_route.get('/rundeck_to_xxl_job/log')
def rundeck_to_xxl_job_log(request, response, **kwargs):
    from dmplib.redis import RedisCache

    cache = RedisCache(key_prefix='rundeck_2_xxljob')
    key = 'record_task_status'

    v = cache.get(key)
    if v:
        if isinstance(v, bytes):
            v = v.decode('utf-8')
        try:
            v = json.loads(v)
        except:
            v = v
    else:
        v = '升级状态已过期'
    return True, '', v
