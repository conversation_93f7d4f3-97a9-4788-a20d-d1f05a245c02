import io
import json
import os
import tempfile
import traceback
import urllib
import zipfile
from hashlib import md5
from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from json import JSONDecodeError
from urllib.parse import unquote, urlparse

import requests

from dmplib import config
from urllib import parse

from components.oss import OSSFileProxy
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from issue_dashboard.services import deliver_service

ADB_2_ST_SQL_PATH = '/api/sqlparser/adb2sr'
SHU_XIN_TOKEN_PATH = '/api/priv/user/login'


class SqlTransformService:

    def __init__(self, file_url):
        self.file_url = file_url
        self.file = None
        self.cache = RedisCache(key_prefix='parse_zip_file')
        self.transform_st = False
        self.old_host = config.get("Transform.old_api_host")
        self.new_host = config.get("Transform.new_api_host")
        self.tmp_upload_folder = "tmp_sql_transform_download"
        self.token = ""
        if not os.path.exists(self.tmp_upload_folder):
            os.makedirs(self.tmp_upload_folder)

    def transform(self, response):
        self.token = shu_xin_login()
        export_data = self._download_zip()
        if export_data.get("datasets"):
            datasets = export_data.get("datasets")
            if not datasets:
                return
            for dataset in datasets:
                self._transform_dataset(dataset)
        self._update_zip(export_data)
        if response:
            response.set_header('Content-Type',
                                'application/octet-stream;charset={charset}'.format(charset="utf_8_sig"))
            response.set_header('Content-disposition', 'attachment; filename=' + urllib.parse.quote_plus(self.file_name))
            with open(self.tmp_upload_path, 'rb') as file:
                response.body = file.read()

    def _transform_dataset(self, dataset_all_obj):
        if self._transform_datasource(dataset_all_obj):
            dataset = dataset_all_obj.get("dataset")
            if not dataset:
                return
            content_str = dataset.get("content")
            try:
                content = json.loads(content_str, encoding='utf-8', strict=False)
            except Exception:
                traceback.print_exc()
            sql = content.get("sql")
            if content.get("transform_sr"):
                return
            if sql:
                state, new_sql = request_rdb_to_st(sql, self.token)
                if state:
                    content["sql"] = new_sql
                    content["transform_sr"] = True
                    dataset["content"] = json.dumps(content, ensure_ascii=False)
                else:
                    content["transform_sr"] = False
                    return False

    def _transform_datasource(self, dataset):
        try:
            if dataset.get("data_source"):
                data_source = dataset.get("data_source")
                conn_str = data_source.get("conn_str")
                if conn_str:
                    conn = json.loads(conn_str) or {}
                    if conn.get("host") and conn.get("host").startswith(self.old_host):
                        conn["host"] = self.new_host
                        data_source["conn_str"] = json.dumps(conn, ensure_ascii=False)
                        return True
        except Exception as e:
            print(str(e))
        return False

    def _update_zip(self, export_data):
        td, new_zip = tempfile.mkstemp(suffix=".zip", dir=self.tmp_upload_folder)
        with zipfile.ZipFile(self.tmp_upload_path, 'r') as zin, zipfile.ZipFile(new_zip, "w") as zou:
            for name in zin.infolist():
                if self.tmp_update_file_name and self.tmp_update_file_name == name.filename:
                    zou.writestr(name, json.dumps(export_data, ensure_ascii=False))
                else:
                    zou.writestr(name.filename, zin.read(name.filename))
        os.remove(self.tmp_upload_path)

        os.rename(new_zip, self.tmp_upload_path)

    def _download_zip(self):

        _url = urlparse(unquote(self.file_url))
        self.file_name = os.path.basename(_url.path)
        root = _url.path if _url.path else ''
        root = root[1:] if root.startswith('/') else root

        self.tmp_upload_path = os.path.join(self.tmp_upload_folder, self.file_name)

        OSSFileProxy().download_file_list(root, self.tmp_upload_folder)
        with zipfile.ZipFile(self.tmp_upload_path, "r") as zip_file:
            for name in zip_file.namelist():
                with zip_file.open(name) as zfile:
                    r_data = zfile.read()
                    if r_data:
                        try:
                            self.tmp_update_file_name = name
                            export_data = json.loads(r_data.decode('utf-8', 'ignore'))
                        except JSONDecodeError:
                            raise UserError(400, "文件格式异常，json解析失败")
                        break

        if not export_data:
            raise UserError(400, '无效的数据文件格式')

        return export_data


def shu_xin_login():
    shu_xin_host = config.get('Transform.shu_xin_host', "")
    params = {"account": config.get('Transform.shu_xin_host', "admin"),
              "password": config.get('Transform.shu_xin_pwd', "6cd79f66a67c1f77adfdd0accec12ac76dabc2ed")}
    url = shu_xin_host + SHU_XIN_TOKEN_PATH
    response = requests.post(url, json=params)
    set_cookie = response.headers.get("Set-Cookie")
    simple_cookie = SimpleCookie(set_cookie)
    token = simple_cookie.get("token")
    tv = token.value
    print("获取token:" + tv)
    return tv


def request_rdb_to_st(sql, token):
    shu_xin_host = config.get('Transform.shu_xin_host', "")
    headers = {"token": token}
    params = {
        "dialect": "adb",
        "sql": sql
    }
    response = requests.post(shu_xin_host + ADB_2_ST_SQL_PATH, json=params, headers=headers)
    if response.status_code == 200:
        try:
            receive_data = json.loads(response.text)
            if receive_data.get("result"):
                return True, receive_data.get("data").get("sql")
            else:
                return False, receive_data.get("msg")
        except Exception as e:
            return False, f'json loads error: {str(e)}, response.status: {response.status_code}, response.text: {response.text}'
    else:
        return False, ' 状态：' + str(response.status_code) + ' , ' + response.reason
