import urllib
from datetime import datetime

from dmplib.db.mysql_wrapper import get_db
from utils_helper.services.export_config_sql_service import get_insert_sql


def export_rundec_insert_sql_file(kwargs, response=None):
    tenant_code_str = kwargs.get("tenant_codes")
    tenant_codes = tenant_code_str
    if not isinstance(tenant_code_str, list):
        tenant_codes = tenant_code_str.split(',')
    params = {"tenant_codes": tenant_codes}
    delete_params = {"tenant_codes": "('" + "','".join(tenant_codes) + "')"}

    workflow_step_ids = get_workflow_step_ids(params)
    if workflow_step_ids:
        delete_params["workflow_step_ids"] = "('" + "','".join(workflow_step_ids) + "')"
    else:
        delete_params["workflow_step_ids"] = "('')"

    sql_str = ''
    sql_str += delete_sql(delete_params)
    sql_str += get_workflow_sql(params, kwargs)
    sql_str += get_scheduled_execution_sql(params, kwargs)
    sql_str += get_workflow_step_sql(params, kwargs)
    # sql_str += get_base_report_sql(params, kwargs)
    sql_str += get_workflow_workflow_step_sql(params, kwargs)
    # sql_str += get_execution_sql(params, kwargs)

    filename = 'rundec_' + str(datetime.now()) + '.sql'
    if response:
        response.set_header('Content-Type', 'application/octet-stream;charset={charset}'.format(charset="utf_8_sig"))
        response.set_header('Content-disposition', 'attachment; filename=' + urllib.parse.quote_plus(filename))
        response.body = sql_str
    return sql_str


def get_workflow_step_sql(params, kwargs):
    return get_insert_sql("rundeck.workflow_step",
                          """select * from rundeck.workflow_step where workflow_step.id  in (
                            SELECT workflow_workflow_step.workflow_step_id from  rundeck.workflow_workflow_step
                            inner join rundeck.scheduled_execution 
                            on workflow_workflow_step.workflow_commands_id=scheduled_execution.workflow_id
                            where scheduled_execution.group_path in %(tenant_codes)s
                        )""", params)


def get_workflow_workflow_step_sql(params, kwargs):
    return get_insert_sql("rundeck.workflow_workflow_step",
                          """select workflow_workflow_step.* from rundeck.workflow_workflow_step
                            INNER JOIN rundeck.scheduled_execution 
                            on scheduled_execution.workflow_id = workflow_workflow_step.workflow_commands_id
                            where scheduled_execution.group_path in %(tenant_codes)s""",
                          params)


def get_execution_sql(params, kwargs):
    return get_insert_sql("rundeck.execution",
                          """select execution.* from rundeck.execution
                            INNER JOIN rundeck.scheduled_execution 
                            on execution.scheduled_execution_id = scheduled_execution.id 
                            where scheduled_execution.group_path  in  %(tenant_codes)s""",
                          params)


def get_workflow_sql(params, kwargs):
    return get_insert_sql("rundeck.workflow",
                          """select workflow.* from rundeck.workflow
                                INNER JOIN rundeck.scheduled_execution on scheduled_execution.workflow_id = workflow.id
                                where scheduled_execution.group_path in %(tenant_codes)s""",
                          params)


def get_base_report_sql(params, kwargs):
    return get_insert_sql("rundeck.base_report",
                          """select base_report.*  from rundeck.base_report
                                INNER JOIN rundeck.scheduled_execution on base_report.jc_job_id = scheduled_execution.id
                                where scheduled_execution.group_path in %(tenant_codes)s""",
                          params)


def get_scheduled_execution_sql(params, kwargs):
    return get_insert_sql("rundeck.scheduled_execution",
                          """select * from rundeck.scheduled_execution
                                where scheduled_execution.group_path in %(tenant_codes)s""", params)


def get_workflow_step_ids(params):
    with get_db() as db:
        data_list = db.query("""
            select workflow_workflow_step.workflow_step_id workflow_step_id from rundeck.workflow_workflow_step
            INNER JOIN rundeck.scheduled_execution 
            on scheduled_execution.workflow_id = workflow_workflow_step.workflow_commands_id
            where scheduled_execution.group_path in %(tenant_codes)s;
        """, params)
        return [str(data.get("workflow_step_id")) for data in data_list] or []


def delete_sql(params):
    return """
        delete rundeck.workflow_workflow_step from rundeck.workflow_workflow_step
        INNER JOIN rundeck.scheduled_execution on scheduled_execution.workflow_id = workflow_workflow_step.workflow_commands_id
        where scheduled_execution.group_path in {tenant_codes};
        
        delete from  rundeck.workflow_step where id  in {workflow_step_ids};
        
        delete rundeck.execution from rundeck.execution
        INNER JOIN rundeck.scheduled_execution on execution.scheduled_execution_id = scheduled_execution.id
        where scheduled_execution.group_path  in {tenant_codes};
        
        delete rundeck.base_report  from rundeck.base_report
        INNER JOIN rundeck.scheduled_execution on base_report.jc_job_id = scheduled_execution.id
        where scheduled_execution.group_path in {tenant_codes};
        
        
        delete rundeck.workflow from rundeck.workflow
        INNER JOIN rundeck.scheduled_execution on scheduled_execution.workflow_id = workflow.id
        where scheduled_execution.group_path in {tenant_codes};
        
        delete rundeck.scheduled_execution from rundeck.scheduled_execution 
        where scheduled_execution.group_path in {tenant_codes};
        
    """.format(workflow_step_ids=params.get("workflow_step_ids"), tenant_codes=params.get("tenant_codes"))
