import urllib
from datetime import datetime

from dmplib.db.mysql_wrapper import get_db


def export_config_insert_sql_file(kwargs, response=None):
    tenant_code_str = kwargs.get("tenant_codes")
    tenant_codes = tenant_code_str
    if not isinstance(tenant_code_str, list):
        tenant_codes = tenant_code_str.split(',')
    params = {"tenant_codes": tenant_codes}

    sql_str = ''
    sql_str += get_user_reset_passwd_sql(params, kwargs)
    sql_str += get_tenant_open_log_sql(params, kwargs)
    sql_str += get_project_yzs_config_sql(params, kwargs)
    sql_str += get_project_value_added_func_sql(params, kwargs)
    sql_str += get_project_sql(params, kwargs)
    sql_str += get_project_setting_sql(params, kwargs)
    sql_str += get_project_app_sql(params, kwargs)
    sql_str += get_home_template_sql(params, kwargs)
    sql_str += get_rds_sql(params, kwargs)
    filename = 'dmp_config_' + str(datetime.now()) + '.sql'
    if response:
        response.set_header('Content-Type', 'application/octet-stream;charset={charset}'.format(charset="utf_8_sig"))
        response.set_header('Content-disposition', 'attachment; filename=' + urllib.parse.quote_plus(filename))
        response.body = sql_str
    return sql_str


def get_user_reset_passwd_sql(params, kwargs):
    return get_insert_sql("dap_bi_user_reset_passwd", "select * from dap_bi_user_reset_passwd where code in %(tenant_codes)s",
                          params)


def get_tenant_open_log_sql(params, kwargs):
    return get_insert_sql("dap_bi_tenant_open_log", "select * from dap_bi_tenant_open_log where code in %(tenant_codes)s",
                          params)


def get_project_yzs_config_sql(params, kwargs):
    return get_insert_sql("dap_bi_project_yzs_config", "select * from dap_bi_project_yzs_config where code in %(tenant_codes)s",
                          params)


def get_project_value_added_func_sql(params, kwargs):
    return get_insert_sql("dap_bi_project_value_added_func",
                          "select * from dap_bi_project_value_added_func where project_code in %(tenant_codes)s",
                          params)


def get_project_sql(params, kwargs):
    return get_insert_sql("dap_p_tenant",
                          "select * from dap_p_tenant where code in %(tenant_codes)s", params)

def get_project_setting_sql(params, kwargs):
    return get_insert_sql("dap_bi_tenant_setting",
                          "select * from dap_bi_tenant_setting where code in %(tenant_codes)s", params,
                          {"data_account": "", "data_pwd": ""})


def get_project_app_sql(params, kwargs):
    return get_insert_sql("dap_bi_project_app",
                          "select * from dap_bi_project_app pa where code in %(tenant_codes)s", params)


def get_home_template_sql(params, kwargs):
    return get_insert_sql("dap_bi_home_template",
                          "select * from dap_bi_home_template where tenant_code  in %(tenant_codes)s", params)


def get_rds_sql(params, kwargs):
    host = kwargs.get("host")
    port = kwargs.get("port")
    account = kwargs.get("user")
    pwd = kwargs.get("password")
    return get_insert_sql("dap_bi_rds",
                          "select * from dap_bi_rds where id in (select rds_id from dap_p_tenant where code in %(tenant_codes)s)",
                          params, {"host": host, "port": port, "account": account, "pwd": pwd})


def get_insert_sql(table, select_sql, params, replace_value=None):
    with get_db() as db:
        data_list = db.query(select_sql, params)
        if data_list and len(data_list) > 0:
            columns = get_column_list(data_list[0])
            return format_insert_sql(table, columns, data_list, replace_value)
    return "\n"


def get_column_list(data):
    return [t for t in data]


def format_insert_sql(table, columns, data_list, replace_value):
    all_sql = ''
    col_str = ','.join(['`' + c + '`' for c in columns])
    for data in data_list:
        values = []
        for col in columns:
            if data[col] and isinstance(data[col], bytes):
                v = data[col].hex()
            elif data[col] or data[col] == 0 or data[col] == '':
                v = '"' + str(data[col]) + '"'
            else:
                v = 'null'
            if replace_value and col in replace_value:
                v = '"' + str(replace_value[col]) + '"'
            values.append(v)
        value_str = ','.join(values)
        all_sql += 'replace INTO {table}({cols}) VALUES ({values});\n'.format(
            table=table, cols=col_str, values=value_str
        )
    return all_sql

