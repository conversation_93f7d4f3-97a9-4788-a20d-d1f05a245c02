import urllib
from datetime import datetime

from utils_helper.services.export_config_sql_service import get_insert_sql


def export_public_config_insert_sql_file(kwargs, response=None):

    sql_str = ''
    sql_str += get_project_app_oauth_sql()
    sql_str += get_email_template_sql()
    sql_str += get_users_sql()
    filename = 'dmp_public_config_' + str(datetime.now()) + '.sql'
    if response:
        response.set_header('Content-Type', 'application/octet-stream;charset={charset}'.format(charset="utf_8_sig"))
        response.set_header('Content-disposition', 'attachment; filename=' + urllib.parse.quote_plus(filename))
        response.body = sql_str
    return sql_str


def get_project_app_oauth_sql():
    return get_insert_sql("dap_bi_project_app_oauth", "select * from dap_bi_project_app_oauth", {})


def get_email_template_sql():
    return get_insert_sql("dap_bi_email_template", "select * from dap_bi_email_template", {})


def get_users_sql():
    return get_insert_sql("dap_p_admin_users", "select * from dap_p_admin_users", {})



