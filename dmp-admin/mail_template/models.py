#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/12/8.
"""
from base.models import BaseModel, QueryBaseModel


class EmailTemplateModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = ''
        self.name = ''
        self.type = ''
        self.subject = ''
        self.content = ''
        self.send_mode = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 200}))
        rules.append(('subject', 'string', {'max': 500}))
        return rules


class EmailTemplateQueryModel(QueryBaseModel):
    pass
