# -*- coding: utf-8 -*-
"""
    中间件
"""
# pylint: skip-file

import cgi
import datetime
import logging
import time

from .. import config, ONE_DOMAIN, APP_NAME
from ..metrics import http_metrics
from .globals import _AppCtxGlobals, _app_ctx_stack
from .context import DBContext


logger = logging.getLogger(__name__)


class MetricsHandler:
    def __init__(self):
        self.env_code = config.get_env_code()

    def process_request(self, req, _):
        metrics = http_metrics.HttpMetrics(self.env_code)
        req.context['metrics'] = metrics
        metrics.before_request(req)

    def process_response(self, req, resp, resource, req_succeeded=True):
        request_latency = None
        request_begin = req.context.get('request_begin')
        if request_begin:
            request_latency = (datetime.datetime.now() - request_begin).total_seconds()

        metrics = req.context.get('metrics')
        if metrics:
            metrics.before_response(req, resp, request_latency)


class AttachContext:
    """
    上下文处理
    """

    def __init__(self, slow_seconds=5, profile_rate=0):
        """init
            slow_seconds (int, optional): Defaults to 5. 认为slow api, then profing
            profile_rate (int, optional): Defaults to 0. profile的比率. 例如: 20表示1/20
        """

        self.slow_seconds = slow_seconds
        self.rate = profile_rate

        self.env_code = config.get_env_code()

    def process_request(self, req, _):
        if req.method != 'OPTIONS':
            req.context['request_begin'] = datetime.datetime.now()
        g = _AppCtxGlobals()

        # inject db
        db_ctx = DBContext()
        db_ctx.inject(g)

        if req.params.get("profiling"):
            setattr(g, "profiling", req.params.get("profiling"))
            setattr(g, "sqls", [])
            setattr(g, "logs", [])
            setattr(g, "redis", [])
            setattr(g, "query", [])
        req.context['g'] = g
        _app_ctx_stack.push(g)

    def process_response(self, req, resp, resource, req_succeeded=True):
        rv = _app_ctx_stack.pop()
        g = req.context['g']
        assert g is rv, 'Popped wrong app context.  (%r instead of %r)' % (rv, g)

        # close all connections
        db_ctx = DBContext.instance(g)
        if db_ctx:
            db_ctx.close_all()


class Parser(cgi.FieldStorage):
    pass


class MultipartMiddleware(object):
    """
    文件上传
    https://github.com/yohanboniface/falcon-multipart

    """

    def __init__(self, parser=None):
        self.parser = parser or Parser

    def parse(self, stream, environ):
        return self.parser(fp=stream, environ=environ)

    def process_request(self, req, resp, **kwargs):

        if 'multipart/form-data' not in (req.content_type or ''):
            return

        # This must be done to avoid a bug in cgi.FieldStorage.
        req.env.setdefault('QUERY_STRING', '')

        form = self.parse(stream=req.stream, environ=req.env)
        for key in form:
            field = form[key]

            if not getattr(field, 'filename', False):
                field = form.getlist(key)
                field = field[0] if len(field) == 1 else field
            # TODO: put files in req.files instead when #493 get merged.
            req._params[key] = field


class CrossDomain(object):
    """ cross domain """

    def __init__(self, allow=None):
        """
        :param allow: * or http://dmp.com.cn
        """
        self.allow = allow

    def process_request(self, req, resp):
        pass

    def process_response(self, req, resp, _, req_succeeded=True):
        if 'HTTP_ORIGIN' in req.env:
            resp.set_header('Access-Control-Allow-Origin', req.env['HTTP_ORIGIN'])
            resp.set_header('Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS')
            resp.set_header(
                'Access-Control-Allow-Headers',
                'Access-Control-Allow-Headers'
                'Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,'
                'Keep-Alive,X-Requested-With,If-Modified-Since',
            )
            resp.set_header('Access-Control-Allow-Credentials', 'true')


PYINSTRUMENT_PROFILE_FIELD = "__profile__"


class PyInstrumentMiddleware(object):

    def process_request(self, req, resp):
        from pyinstrument import Profiler
        if PYINSTRUMENT_PROFILE_FIELD in req.params:
            req.profiler = Profiler()
            req.profiler.start()

    def process_response(self, req, resp, _, req_succeeded=True):
        if hasattr(req, "profiler"):
            try:
                req.profiler.stop()
                output_html = req.profiler.output_html()
                resp.set_header('Content-Type', 'text/html; charset=utf-8')
                resp.body = output_html
            except:
                import traceback
                resp.set_header('Content-Type', 'text/html; charset=utf-8')
                tb = traceback.format_exc().replace('\n', '<br/>')
                resp.body = f"""<h1>本次分析失败，请刷新再试！</h1><br/>{tb}"""


class TimingMiddleware(object):
    """record request timing"""

    def __init__(self):
        self.req_time = {}

    def process_request(self, req, resp):
        self.req_time[req] = time.time()

    def process_response(self, req, resp, _, req_succeeded=True):
        st = self.req_time.pop(req, None) or 0
        interval = (time.time() - st) * 1000
        body = resp.data or resp.body or ''
        logger.info(f"{resp.status.split()[0]} {req.method} {req.relative_uri} ({req.remote_addr}) {len(body)} {interval:.2f}ms")


TOKEN_NAME = str(config.get('App.custom_cookie_token_name') or 'token').strip()


class CookieTokenMiddlewareRouter(object):
    """
    处理cookie中token名，自定义token名的中间件
    默认的或者不设置token配置，还是叫以前的token
    如果设置了，那么cookies中就会只使用新设置的token名
    """

    def process_request(self, request, response):
        request.cookies['token'] = request.cookies.pop(TOKEN_NAME, '') or ''
        if ONE_DOMAIN in [1, '1'] or TOKEN_NAME != 'token':
            app_name = APP_NAME
            request.new_host = request.host + f"/{app_name}" if app_name in [1, '1'] and APP_NAME else ''

    def process_response(self, request, response, resource, _req_succeeded):
        if response._cookies:
            token = response._cookies.pop('token', None)
            if token:
                token.set(TOKEN_NAME, token.value, token.value)
                response._cookies[TOKEN_NAME] = token
