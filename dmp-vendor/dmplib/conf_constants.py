# App.runtime
DEFAULT_RUNTIME_ENV = 'test'
# Component.component_endpoint
DEFAULT_COMPONENT_ENDPOINT = 'https://dmp-open.mypaas.com.cn/dmp/component/package/'
# Component.component_menuicon_endpoint
DEFAULT_COMPONENT_ICON_ENDPOINT = 'https://dmp-open.mypaas.com.cn/dmp/icons/component_menus/'
# Domain.dmp_dashboard_template
DEFAULT_TEMPLATE_CENTER_URL = 'https://dmp-tpl.mypaas.com.cn/templet-center'
# Domain.dmp_management
DEFAULT_DEVOPS_URL = 'https://devops-ops.mypaas.com.cn'
# Function.api_sql_complex
DEFAULT_API_COMPLEX_SQL_SWITCH = 0
# Function.api_sql_complex_limit
DEFAULT_API_SOMPLEX_SQL_LIMIT = 10000*10000*10000
# Function.enable_large_screen_move
DEFAULT_ENABLE_LARGE_SCREEN_MOVE = 0
# Grayscale.gray_env
IS_GRAY_ENV = 0
# JWT.dashboard_template_secret
DEFAULT_DASHBOARD_TEMPLATE_SECRET = 'uDAFcXK0'
# JWT.dmp_management_secret
DEFAULT_DMP_MANAGEMENT_SECRET = 'TT6$YBto9kzcFXT%'
# JWT.init_password_secret
DEFAULT_INIT_PASSWORD_SECRET = 'XAuTsZWy'
# LoginWebServer.AppSecret
DEFAULT_LOGIN_SERVER_SECRET = '0a55fb5adc954700a17eede0d57ce539'
# LoginWebServer.AppKey
DEFAULT_LOGIN_SERVER_KEY = 'e2224ebd74ec4348a8109cc2d3a13e01'
# MOP.domain
DEFAULT_MOP_DOMAIN = 'https://dmp-mop.mypaas.com.cn'
# Product.dashboard_snapshot_blacklist
DISABLE_DASHBOARD_SNAPSHOT_ENV ='fangkai-dmp-saas-hw-prod,dmp_yunke,yk-hw-dap-saas-new,dmp_myfuwu,bigdata-hw-prod,cy-saas-hw-bigdata,yk-kf-saas-prod,ycg-pulsar,woxiang_ailiyun,ywy-hw-saas'
# Product.record_getdata_api_log
RECORD_API_LOG = 0
# PublishCenter.secret
DEFAULT_PUBLISHCENTER_SECRET = 'S4kTQxVqoAjlAzmMN39yz6Ymo7FABA'
# Redis.celery_db
DEFAULT_CELERY_DB = 0
# Rundeck.port
DEFAULT_RUNDECK_PORT = 4441
# Rundeck.project_name
DEFAULT_RUNDECK_PROJECT_NAME = 'dmp'
# Rundeck.server
DEFAULT_RUNDECK_SERVER_NAME = 'dmp-rundeck4'
# Rundeck.token
DEFAULT_RUNDECK_TOKEN = 'bf6PqvUrwDdicTlsLPUvOEp44jHwRD71'
# SelfService.app_key
DEFAULT_SELFSERVICE_KEY = 5.70E+15
# SelfService.app_secret
DEFAULT_SELFSERVICE_SECRET = 'exvxiSOaQBSsZEuBQiuRVyZlTLnyLmQI'
# SelfService.host
DEFAULT_SELFSERVICE_HOST = 'https://bigdata-openapi-test.mypaas.com.cn'
# SelfService.is_enable_indicator_permission
SELFSERVICE_ENABLE_INDICATOR_PERMISSION = '0'
# SelfService.pulsar_project_code
SELFSERVICE_PULSAR_PROJECT_CODE = 'data_asstes'
# SelfService.self_redirect_url
SELFSERVICE_REDIRECT_URL= 'https://bigdata-test.myyscm.com/dmp/yfw/self-report-access-auth'
# SelfService.yl_api_secret
SELFSERVICE_YL_API_SECRET = 'lsjfsldfALDdlafLGALDdlALowilxA'
# App.name
APP_NAME = 'dmp'
ONE_DOMAIN = 1
# DBInstaller.saas_env_url
DBINSTALLER_SAAS_ENV_URL = 'https://dmp-admin.tj.mycyjg.com'
LOG_SLOW_LOG_SECONDS = 3
ENABLE_PROFILING = 0
# Log.level
LOG_LEVEL = 'ERROR'
# Log.sentry_dsn
LOG_SENTRY_DSN = ''