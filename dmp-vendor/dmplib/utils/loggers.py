# -*- coding: utf-8 -*-
"""
    for description
"""

import logging
import os
import datetime
import sys
import tempfile

from raven.handlers.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>
from raven import Client, setup_logging

from ..conf_constants import LOG_LEVEL, LOG_SENTRY_DSN
from .. import config


class InfoFilter(logging.Filter):
    def filter(self, record):
        return record.levelno in (logging.DEBUG, logging.INFO)


def init_logging(file_name="app"):
    _level = LOG_LEVEL
    _handlers = 'console'
    _fmt = '%(asctime)s %(filename)s[line:%(lineno)d]  %(exc_text)s %(message)s'
    _formatter = logging.Formatter(_fmt)
    _logger = logging.root
    _logger.setLevel(_level)

    _handlers = _handlers.split(',')

    if 'file' in _handlers:
        log_date = datetime.datetime.now().strftime("%Y-%m-%d")
        app_root = os.environ.get('DMP_ROOT_PATH')
        if app_root:
            log_folder = os.path.join(app_root, "runtime", "log")
        else:
            log_folder = tempfile.gettempdir()

        log_path = os.path.join(log_folder, file_name + "_" + log_date + ".log")

        if not os.path.exists(log_folder):
            os.makedirs(log_folder)

        _file_handler = logging.FileHandler(log_path, encoding="UTF-8")
        _file_handler.setLevel(_level)
        _file_handler.setFormatter(_formatter)
        _logger.addHandler(_file_handler)

    if 'console' in _handlers:
        stdout_handler = logging.StreamHandler(sys.stdout)
        stdout_handler.setLevel(_level)
        stdout_handler.addFilter(InfoFilter())

        stderr_handler = logging.StreamHandler(sys.stderr)
        stderr_handler.setLevel(logging.WARNING)
        _logger.addHandler(stdout_handler)
        _logger.addHandler(stderr_handler)

    if 'sentry' in _handlers:
        sentry_dsn = LOG_SENTRY_DSN
        if sentry_dsn is not None and sentry_dsn != '':
            tags = {'env_code': config.get_env_code()}
            client = Client(
                dsn=sentry_dsn,
                level=_level,
                tags=tags,
                string_max_length=1024,
                sanitize_keys=['password', 'pwd', 'token', 'apikey', 'secret', 'passwd'],
            )
            _sentry_handler = SentryHandler(client)
            setup_logging(_sentry_handler)
