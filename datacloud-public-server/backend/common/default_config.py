def get_all_data():
    return {
        # 异步服务引擎：redis、rabbitmq。默认值为：rabbitmq

        'App.celery_broker': 'rabbitmq',

        # local 本地部署，cloud 云端部署（默认：cloud）

        'App.deployment': 'cloud',

        # 筛选重构开关：1 表示是新筛选，0表示是旧筛选

        'App.filter_pre_load': '0',

        # 是否关闭直连数据集sql缓存，1表示关闭，0表示不关闭

        'Cache.close_direct_sql_cache': '0',

        # 是否关闭角色权限缓存（0-不关闭缓存，1-关闭缓存）

        'Cache.close_role_privilege_cache': '0',

        # 直连数据集缓存前缀

        'Cache.direct_dataset_cache_prefix_key': 'direct_sql_prefix_key',

        # 默认值7, 表示直连数据集在Redis中记录sql访问次数的过期天数

        'Cache.direct_dataset_cache_visit_expire': '7',

        # 直连DB数据集sql的缓存条件，格式count,day|count2,day2，例如3,7|1,2，表示如果在（过去7天内访问超过3次）或（过去2天内访问超过1次），则对sql进行缓存，day最大不超过10。

        'Cache.direct_dataset_sql_cache_condition': '3,7',

        # 缓存时sql执行的超时时间

        'Cache.direct_dataset_sql_time_out': '60',

        # 已安装组件缓存key

        'Cache.installed_components_cache_key': 'installed_components_v30_yq',

        # 已发布报告的元数据缓存key(dashboard、flow、dataset会共用此key，修改前请沟通清楚)

        'Cache.released_dashboard_metadata_cache_key': 'dmp_v4',

        # 调度数据集访问次数/sql文本缓存前缀，必须配置，默认值 schedule_sql_prefix_key

        'Cache.schedule_dataset_cache_prefix_key': 'schedule_sql_prefix_key',

        # 默认值7，表示调度数据集在Redis中记录sql访问次数的过期天数

        'Cache.schedule_dataset_cache_visit_expire': '7',

        # 调度数据集预缓存条件，格式 count,day|count2,day2 ，例如 3,7|1,2 ，表示如果在（过去7天内访问超过3次）或（过去2天内访问超过1次），则对sql进行缓存，必须配置，默认值 1,1 ，day最大不超过10。

        'Cache.schedule_dataset_sql_cache_condition': '3,7',

        # 缓存时sql执行的超时时间

        'Cache.schedule_dataset_sql_time_out': '60',

        # 直连的sql数据集和api数据集中表个数限制（前端编辑报告时筛选器可选数据集）

        'DatasetConfig.filterable_table_limit': '20',

        # 支持配置保留的天数

        'DatasetConfig.max_day_replications': '0',

        # 最大数据集版本数量

        'DatasetConfig.max_version_replications': '5',

        # 默认500，api调度数据集分页取数

        'DatasetConfig.pagesize_retrieve_api_dataset': '500',

        # datax超时

        'External.datax_timeout': '600',

        # 表格组件取数返回的列数，默认或不设置时值为100

        'Function.chart_column_limit_num': '100',

        # 导入/分发报表包里面的图片上传处理并行线程数

        'Function.dashboard_import_image_threads': '6',

        # 天眼报告同步查询sql条数limit，默认值  1500

        'Function.fast_sync_batch_num': '1500',

        # 天眼报告同步线程数，默认值5

        'Function.fast_sync_threads': '5',

        # 基础数据平台获取报告权限接口

        'IngratePlatform.report_authorize_url': '/api/basicdata/CheckUserReportAuthorize',

        # cookie名称，防止统一域名时，不同产品之间的cookie key冲突，可以调整

        'App.custom_cookie_token_name': 'mydp-token',

        'App.custom_cookie_admin_token_name': 'dmp-admin-token',

        # cookie密钥，用于cookie中user信息的加密解密

        'JWT.component_secret': 'YC2UFKz7',

        # 8002

        'Metrics.server_port': '18102',

        # omp开户密码v1秘钥

        'OMP.pwd_v1_key': 'mysoft1234567890',

        # omp开户密码v2秘钥

        'OMP.pwd_v2_key': 'Mysoft95938@2022',

        # omp开户密码v3秘钥

        'OMP.pwd_v3_key': 'Mysoft95938@2022',

        # 数芯api接口密钥

        'PPT.jwt_secret': 'LdGskFiUsuiqWYGxYBguihXzhARzntRF',

        # 取数秘钥

        'PPT.sso_secret': 'S4kTQxVqoAjlAzmMN39yz6Ymo7FABA',

        # body超过10M不放入队列中

        'RabbitMQ.body_limit': '10',

        # queue_name_datax

        'RabbitMQ.queue_name_datax': 'dmp_datax',

        # queue_name_datax_offline

        'RabbitMQ.queue_name_datax_offline': 'dmp_datax_offline',

        # 消息队列queue_name

        'RabbitMQ.queue_name_download': 'dmp_download',

        # queue_name_flow

        'RabbitMQ.queue_name_flow': 'dmp_flow',

        # queue_name_flow_feeds

        'RabbitMQ.queue_name_flow_feeds': 'dmp_flow_feeds',

        # queue_name_flow_offline

        'RabbitMQ.queue_name_flow_offline': 'dmp_flow',

        # Flow-priority

        'RabbitMQ.queue_name_flow_priority': 'dmp_flow_priority',

        # 新流程配置

        'RabbitMQ.queue_name_work_flow': 'dmp_work_flow',

        # 默认值vhost_tj_sj， 历史环境需要配 /

        'RabbitMQ.vhost': 'vhost_tj_sj',

        # Redis.max_connections=100

        'Redis.max_connections': '100',

        # 链接超时时间

        'ReportCenter.open_print_time_out': '3600',

        # 打印预览超时时间（秒）

        'ReportCenter.open_print_view_time_out': '7200',

        # 简讯发送进程池进程数量

        'Subscribe.max_process': '1',

        # 简讯发送线程池进程数量

        'Subscribe.max_workers': '10',

        # wt加密密钥

        'Superportal.app_secret': 'OTMwNWIxNTMyZTI0N2JmNTc4NGJlOTdh',

        # 告警emal

        'XXL-JOB.alarm_email': '<EMAIL>',

        # 默认执行器名称

        'XXL-JOB.default_group_appname': 'dmp-task-executor',

        # 分组名称

        'XXL-JOB.default_group_title': 'dmp',

        # 执行失败重试次数

        'XXL-JOB.executor_fail_retry': '3',

        # 执行器host

        'XXL-JOB.executor_host': 'dmp-task-group.dmp.svc',

        # 执行器超时时间

        'XXL-JOB.executor_timeout': '10',

        # delivery_worker

        'DBInstaller.delivery_worker': '2',

        # project_data_db_suffix

        'DBInstaller.project_data_db_suffix': 'data',

        # project_db_prefix

        'DBInstaller.project_db_prefix': 'dmp',

        # 空库data库

        'DBInstaller.project_empty_data_db': 'dmp_empty_data',

        # project_empty_db

        'DBInstaller.project_empty_db': 'dmp_empty',

        # rds_access_key_id

        'DBInstaller.rds_access_key_id': '',

        # rds_access_key_secret

        'DBInstaller.rds_access_key_secret': '',

        # expires

        'JWT.expires': '7200',

        # secret

        'JWT.secret': 'YC2UFKz7',

        # 更新中心推送消息，email,多个账号中间用"间隔

        'PublishCenter.emails': '`<EMAIL>;<EMAIL>`',

        # 开户时候，默认使用更新中心包

        'PublishCenter.upgrade': '1',

        # 创建租户的时候，默认开启套打功能

        'ReportCenter.open_print_enable': '1',

        # sso_secret

        'Akso.sso_secret': 'OTMwNWIxNTMyZTI0N2JmNTc4NGJlOTdh',

        # 组件取数接口里一次取多少组件数据，默认是6，不能小于1

        'App.chart_batch_fetch': '1',

        # 是否是saas模式，1是，0否

        'App.saas_mode': '0',

        # 关闭数据集sql缓存，（0或空：不关闭，1：关闭）

        'Cache.close_sql_cache': '0',

        # 已安装组件是否有更新行为标志位缓存key

        'Cache.components_refresh_flag_cache_key': 'component_update_on',

        # 数据集运行预览调ERP 接口超时时间，默认30 s

        'External.api_timeout': '30',

        # 组件取数的limit默认值配置，默认为1500

        'Function.chart_query_default_limit': '1500',

        # 报告锁定时长，分钟为单位，设置为小于等于0则表示关闭锁定功能

        'Function.dashboard_lock_time': '30',

        # 是否开启报告版本查询，1：开启 0： 关闭 ，设置默认开启

        'Function.enable_versioned_query': '1',

        # 为serial时，取数为串行执行；为parallel时，取数为并行执行

        'Function.subtotal_query_method': 'serial',

        # 下载时需要转换为空的特殊字符

        'Function.chart_download_invalid_filename_chars': 'AZ',

        # 下载的单个文件的取数的分页的单页记录数限制, 整数类型，默认为500

        'Function.chart_download_limit_page_size': '500',

        # 下载的单个文件的记录数目限制，整数类型， 默认为20000，必须为Function.chart_download_limit_page_size的整数倍

        'Function.chart_download_limit_per_file': '20000',

        # 不开启小计时，下载的单个文件的取数的分页的单页记录数限制, 整数类型

        'Function.chart_download_no_subtotal_limit_page_size': '50000',

        # 不开启小计时，下载的单个文件的记录数目限制，整数类型；必须为Function.chart_download_no_subtotal_limit_page_size的整数倍

        'Function.chart_download_no_subtotal_limit_per_file': '50000',

        # 多进程下载任务的进程数量限制, 整数类型，默认为2

        'Function.chart_download_process_limit': '2',

        # key

        'AMap.key': '0b1fbd62335bf8a390da5eccfffb0d06',

        # path

        'Chromium.path': '/usr/lib/chromium/chromium',

        # get_message_timeout

        'Email.get_message_timeout': '30',

        # get_message_user_per_time

        'Email.get_message_user_per_time': '5',

        # Email.send_person_per_time 发邮件每批次的接收人数（人/批）， 不填默认为10

        'Email.send_person_per_time': '10',

        # Email.smtp_timeout 发邮件的超时时间，不填默认是30

        'Email.smtp_timeout': '30',

        # root
        'OSS.root': 'dmp-flow',

    }
