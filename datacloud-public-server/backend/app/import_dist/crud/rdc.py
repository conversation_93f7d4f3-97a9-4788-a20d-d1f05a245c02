from backend.app.import_dist.model.import_dist import RDC, ImportDist
from backend.common.enums import ImportStatus

from backend.database.db_util import uuid4_str, CurrentSession
from backend.app.import_dist.schema.import_dist import RdcParams
from sqlalchemy import text


async def add_one_rdc_record(db: CurrentSession, params: RdcParams, file_oss_url: str) -> str:
    """
    添加一条RDC记录
    """
    model_id = uuid4_str()
    add_model = RDC(
        id=model_id,
        tenant_code=params.tenantCode,
        tenant_type=params.tenantType,
        rdc_task_id=params.taskId,
        customer_guid=params.customerGuid,
        app_code=params.appCode,
        version=params.version,
        app_key=params.appKey,
        env_code=params.envCode,
        operator=params.operator,
        business_mode=params.businessMode,
        app_type=params.appType,
        app_name=params.appName,
        upgrade_scene=params.upgradeScene,
        file_oss_url=file_oss_url,
    )
    db.add(add_model)
    db.commit()
    return model_id


async def add_one_import_record(db: CurrentSession, **kwargs) -> str:
    """
    添加一条添加记录
    """
    model_id = uuid4_str()
    add_model = ImportDist(id=model_id, **kwargs)
    db.add(add_model)
    db.commit()
    return model_id


async def update_one_rdc_record(db: CurrentSession, id: str, data: dict):
    """
    更新导入RDC的数据
    """
    db.query(RDC).filter(RDC.id == id).update(data)
    db.commit()


async def get_completed_record_appcodes(db: CurrentSession, code: str):
    """
    查询租户下所有完成的应用编码(未定义dap_bi_project_create_record)这里就不用orm的方式了
    :param db:
    :param code:
    :return:
    """
    sql = """ select app_codes AS app_codes from dap_bi_project_create_record 
           where status = 'COMPLETED' and code = :code """
    params = {'code': code}
    data = db.execute(text(sql), params=params).mappings()
    app_codes = []
    if data:
        for row in data:
            s = row.get('app_codes')
            if not s:
                continue
            app_codes += s.split(',')
        return list(set(app_codes))
    return app_codes


async def get_completed_record_version(db: CurrentSession, code: str, app_code: str):
    """
    查询对应的app_code、code、有无记录，且版本不一致
    """
    # todo 这里需要区分状态（例如：初始化、更新失败）场景，还需要考虑版本合并的情况下，跳过版本？
    sql = """select rdc.version as version from dap_bi_datacloud_public_rdc rdc 
                inner join dap_bi_datacloud_public_import_dist import on rdc.rdc_task_id = import.rdc_task_id 
                where rdc.tenant_code = :tenant_code
                and rdc.app_code = :app_code and import.status!=:status
    """
    # 排查初始化的记录
    params = {'tenant_code': code, 'app_code': app_code, 'status': ImportStatus.init.value}
    data = db.execute(text(sql), params=params).mappings()
    app_version = []
    # 有记录的情况下，有无重合的version记录
    if data:
        for row in data:
            s = row.get('version')
            if not s:
                continue
            app_version += s.split(',')
        return list(set(app_version))
    return app_version

