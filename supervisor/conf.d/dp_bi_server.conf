[program:dmp-flow]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=python /home/<USER>/dmp-flow/app_flow_exec_new.py                                                    ;启动程序的命令
environment=QUEUE_NAME_DATAX_OFFLINE="dmp_datax",CONSUMER_NUM=%(ENV_FLOW_NUM)s,SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                      ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-flow/                                                                           ;相当于在该目录下执行程序
;startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
;priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-flow-err.log
stdout_logfile=/var/log/supervisor/dmp-flow-out.log


[program:dmp-flow-priority]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=python /home/<USER>/dmp-flow/app_flow_exec_new.py                                                    ;启动程序的命令
environment=QUEUE_NAME_FLOW_OFFLINE="dmp_flow_priority",QUEUE_NAME_DATAX_OFFLINE="dmp_datax",CONSUMER_NUM=%(ENV_FLOW_PRIORITY_NUM)s,SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                      ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-flow/                                                                           ;相当于在该目录下执行程序
;startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
;priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-flow-priority-err.log
stdout_logfile=/var/log/supervisor/dmp-flow-priority-out.log

[program:dmp-flow-feeds]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=python /home/<USER>/dmp-flow-feeds/app.py                                                  ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                      ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-flow-feeds/                                                                           ;相当于在该目录下执行程序
;startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
;priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-flow-feeds-err.log
stdout_logfile=/var/log/supervisor/dmp-flow-feeds-out.log


[program:dmp-task-executor-0]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=python /home/<USER>/dmp-task-executor/executor_app.py                                                 ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                       ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-task-executor/                                                                           ;相当于在该目录下执行程序
;startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
;priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
environment=PORT="9001"
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-task-executor-0-err.log
stdout_logfile=/var/log/supervisor/dmp-task-executor-0-out.log


[program:dmp-celery-lite]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=celery -A app_celery worker --loglevel=%(ENV_LOG_LEVEL)s --concurrency=%(ENV_CELERY_LITE_PROCESS_NUM)s -n worker@download -P threads -Q celery,parser  ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                      ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/webapp/                                                                           ;相当于在该目录下执行程序                                                                                          ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数                                                                                        ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-celery-lite-err.loggit
stdout_logfile=/var/log/supervisor/dmp-celery-lite-out.log

[program:dmp-celery-offline]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=celery -A app_celery worker --loglevel=%(ENV_LOG_LEVEL)s --concurrency=%(ENV_CELERY_OFFLINE_PROCESS_NUM)s -n worker@download -Q celery-slow,download,self-service,upgrade  -P threads ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                      ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/webapp/                                                                           ;相当于在该目录下执行程序                                                                                          ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数                                                                                        ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-celery-offline-err.log
stdout_logfile=/var/log/supervisor/dmp-celery-offline-out.log


[program:dmp-celery-feeds]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=celery -A app_celery worker --loglevel=%(ENV_LOG_LEVEL)s --concurrency=%(ENV_CELERY_FEEDS_PROCESS_NUM)s -n worker@feeds -Q feeds  -P threads ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                        ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/webapp/                                                                           ;相当于在该目录下执行程序
;startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
;priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
; stderr_logfile=/dev/stderr
; stdout_logfile=/dev/stdout
; stdout_maxbytes=0
; stderr_maxbytes=0
; stdout_logfile_maxbytes = 0
; stdout_logfile_backups = 0
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-celery-feeds-err.log
stdout_logfile=/var/log/supervisor/dmp-celery-feeds-out.log


[program:dmp]
user=root                                         ;指定运行用户
directory=/home/<USER>/webapp/
command=fast-boot run-program gunicorn app:__hug_wsgi__ -c gunicorn.py
environment=FastTracker_ConfigPath="FastTracker.json",FastTracker_Enable=%(ENV_FAST_ENABLE)s,FastTracker_EnvCode=%(ENV_FAST_ENV_CODE)s,FastTracker_ProductCode=%(ENV_FAST_PRODUCT_CODE)s,FastTracker_AppCode=%(ENV_FAST_APP_CODE)s,SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                    ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                  ;设置改程序是否自动重启
startretries=10                                   ;重启程序的次数
priority=998                                      ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-err.log
stdout_logfile=/var/log/supervisor/dmp-out.log


[program:dmp-admin]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=gunicorn app:__hug_wsgi__ -b %(ENV_ADMIN_APP_BIND)s --limit-request-line=16384 --limit-request-fields=1000 --limit-request-field_size=32768 --timeout=60 -w %(ENV_APP_ADMIN_WORKS)s --log-level=INFO -k gevent --pythonpath /home/<USER>/dmp-admin  ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"
autostart=true                                                                                        ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-admin/                                                                           ;相当于在该目录下执行程序
startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-admin-err.log
stdout_logfile=/var/log/supervisor/dmp-admin-out.log


[program:dmp-admin-celery]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=celery -A app_celery worker --loglevel=INFO --concurrency=%(ENV_ADMIN_CELERY_PROCESS_NUM)s --prefetch-multiplier=1 -n worker@%(ENV_HOSTNAME)s  -P threads ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"                                                                                  ;所有系统环境变量都传递给被管理的进程
autostart=true                                                                                        ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-admin/                                                                           ;相当于在该目录下执行程序
startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-admin-celery-err.log
stdout_logfile=/var/log/supervisor/dmp-admin-celery-out.log


[program:dmp-admin-celery-flower]                                                                                   ;程序的名字，在supervisor中可以用这个名字来管理该程序。
user=root                                                                                             ;指定运行用户
command=celery -A app_celery flower --url_prefix=flower --basic_auth=admin:bigdata2019! --auth_provider=flower.views.auth.BasicAuth --port=5555 ;启动程序的命令
environment=SKYLINE_NACOS_FILE_EXTENSION="%(ENV_SKYLINE_NACOS_FILE_EXTENSION)s",SKYLINE_NACOS_ENCRYPTION_ALGORITHM="%(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM)s",SKYLINE_NACOS_USERNAME="%(ENV_SKYLINE_NACOS_USERNAME)s",SKYLINE_NACOS_NAMESPACE_ID="%(ENV_SKYLINE_NACOS_NAMESPACE_ID)s",SKYLINE_NACOS_GROUP="%(ENV_SKYLINE_NACOS_GROUP)s",SKYLINE_NACOS_SERVER_ADDRESSES="%(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)s",SKYLINE_NACOS_DATA_ID="%(ENV_SKYLINE_NACOS_DATA_ID)s",SKYLINE_NACOS_PASSWORD="%(ENV_SKYLINE_NACOS_PASSWORD)s",SKYLINE_NACOS_NAME="%(ENV_SKYLINE_NACOS_NAME)s",SKYLINE_NACOS_SECRET="%(ENV_SKYLINE_NACOS_SECRET)s"                                                                                  ;所有系统环境变量都传递给被管理的进程
autostart=true                                                                                        ;设置改程序是否虽supervisor的启动而启动
autorestart=true                                                                                      ;设置改程序是否自动重启
directory=/home/<USER>/dmp-admin/                                                                           ;相当于在该目录下执行程序
startsecs=5                                                                                           ;重新启动时，等待的时间
startretries=10                                                                                       ;重启程序的次数
priority=999                                                                                          ;设置进程启动和停止的优先级
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/dmp-admin-celery-flower-err.log
stdout_logfile=/var/log/supervisor/dmp-admin-celery-flower-out.log

[program:public-server]
directory=/home/<USER>/datacloud-public-server
command=uvicorn backend.main:app --host 0.0.0.0 --port 9000 --workers 1
user=root
autostart=true
autorestart=true
; startretries=5
redirect_stderr=true
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/public-server-err.log
stdout_logfile=/var/log/supervisor/public-server-out.log



[program:public-consumer]
directory=/home/<USER>/datacloud-public-server
command=python -u backend/consumers/entrypoint.py
user=root
autostart=true
autorestart=true
; startretries=5
stdout_logfile_maxbytes=15MB  ; stdout 日志文件大小，默认50MB
stdout_logfile_backups=3   ; stdout 日志文件备份数，默认是10
stderr_logfile_maxbytes=15MB  ; stderr 日志文件大小，默认50MB
stderr_logfile_backups=3   ; stderr 日志文件备份数，默认是10
stderr_logfile=/var/log/supervisor/public-consumer-err.log
stdout_logfile=/var/log/supervisor/public-consumer-out.log


[program:log-forwarder]
command=/bin/sh -c "sleep 20 && tail -F /var/log/supervisor/*.log  | grep -v -E 'CRIT uncaptured|supervisor|Illegal seek'"
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0
